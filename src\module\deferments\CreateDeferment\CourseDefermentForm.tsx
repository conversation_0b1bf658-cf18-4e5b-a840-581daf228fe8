/* eslint-disable @typescript-eslint/no-explicit-any */

import CommonDefermentInputs from "src/module/deferments/CreateDeferment/CommonDefermentInputs";
import { FormControl, Text, Checkbox } from "@chakra-ui/react";
import React, { useMemo, useState, useEffect } from "react";
import { ICreateDefermentForm } from "src/module/deferments/CreateDeferment/CreateDefermentForm";
import { useStudentDashboard } from "@/hooks/useStudent/useStudentDashboard";
import { sortProgrammeLevelEnrollments } from "src/module/courses/utils/sortProgrammeEnrollments";

const CourseDefermentForm = ({
  values,
  handleChange,
  handleBlur,
  touched,
  errors,
  setFieldValue,
  isSubmitting,
  isSubmitEnabled,
}: ICreateDefermentForm) => {
  const { data } = useStudentDashboard();

  const courses = useMemo(() => {
    const enrollments = sortProgrammeLevelEnrollments(
      data?.programme_level_enrollment,
    ).filter((enrollment) => !!enrollment.programme_level_id);

    const last_enrollment = enrollments.length - 1;
    const last_semester =
      (enrollments?.[last_enrollment]?.semesters?.length || 0) - 1;
    return (
      data?.programme_level_enrollment[last_enrollment]?.semesters[
        last_semester
      ].courses || []
    );
  }, [data]);

  const [selectedCourses, setSelectedCourses] = useState<string[]>([]);
  const handleCheckboxChange = (value: string) => {
    setSelectedCourses((prevSelected) =>
      prevSelected.includes(value)
        ? prevSelected.filter((course) => course !== value)
        : [...prevSelected, value],
    );

    handleChange({ target: { name: "courses", value: selectedCourses } });
  };

  useEffect(() => {
    setFieldValue("courses", selectedCourses);
  }, [selectedCourses]);

  return (
    <>
      <FormControl mb={6}>
        <Text marginBottom={4} color="#0A3150" fontWeight={600} fontSize={14}>
          Select the course(s) you would like to defer
          <span className="text-red-500">*</span>
        </Text>
        <div className="flex flex-col space-y-6 bg-[#F9FAFB] py-4 pl-4">
          {courses.map((course: any) => (
            <Checkbox
              key={course.course_id}
              isChecked={selectedCourses.includes(course.course_id)}
              onChange={() => handleCheckboxChange(course.course_id)}
            >
              {course.course_name}
            </Checkbox>
          ))}
        </div>
      </FormControl>

      <CommonDefermentInputs
        handleChange={handleChange}
        values={values}
        handleBlur={handleBlur}
        touched={touched}
        errors={errors}
        setFieldValue={setFieldValue}
        isSubmitting={isSubmitting}
        isSubmitEnabled={isSubmitEnabled}
      />
    </>
  );
};

export default CourseDefermentForm;
