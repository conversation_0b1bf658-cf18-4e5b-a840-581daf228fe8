import { useToast } from "@chakra-ui/react";
import { useMutation } from "@tanstack/react-query";
import { IPinOtp } from "src/api/repository/Tuition-fund/forgotPinOtp";
import { ChangePinOTP } from "src/api/repository/Tuition-fund/changePinOtp";
import axios from "axios";
import { extractAxiosError } from "@/lib/utils/helpers";

export const useChangePinOTP = () => {
  const toast = useToast();
  return useMutation({
    mutationFn: async (values: IPinOtp) => {
      return await ChangePinOTP(values);
    },
    onError: (error: any) => {
      if (axios.isAxiosError(error)) {
        toast({
          description: extractAxiosError(error),
          status: "error",
        });
      }
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "OTP sent successfully",
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    },
  });
};
