import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { QueryDataPaging } from "src/api/config/api.d";
import { getExamLocationList } from "src/api/repository/examLocation";

export interface IParamsExamLocation extends QueryDataPaging {
  search?: string;
}

export const useExamLocationList = ({
  page,
  perPage,
  search,
}: QueryDataPaging) => {
  const query = useQuery({
    queryKey: ["getExamLocationList", { perPage, page, search }],
    queryFn: () => {
      return getExamLocationList({ page, perPage, search });
    },
    retry: false,
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
    select: (response) => {
      const {
        data,
        currentPage,
        nextPage,
        perPage,
        prevPage,
        total,
        totalPages,
      } = response.data;

      return {
        data,
        meta: {
          currentPage,
          nextPage,
          perPage,
          prevPage,
          total,
          totalPages,
        },
      };
    },
  });

  return query;
};
