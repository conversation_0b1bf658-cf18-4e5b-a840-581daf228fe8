"use client";
import { FormikBag, withFormik } from "formik";
import Reset<PERSON>asswordForm from "./ResetPasswordForm";

import {
  IResetPasswordContainerProps,
  IResetPasswordValue,
} from "./ResetPassword.d";
import { checkValueError, validateRequired } from "@/lib/utils/validation";
import { IValidations } from "@/lib/utils/validation.d";

const validateFields: IValidations<IResetPasswordValue> = {
  password: [
    {
      validator: validateRequired,
      code: "This field is required",
    },
  ],
  confirm_password: [
    {
      validator: validateRequired,
      code: "This field is required",
    },
  ],
  reset_token: [
    {
      validator: validateRequired,
      code: "This field is required",
    },
  ],
};

export const onSubmit = async (
  values: IResetPasswordValue,
  {
    setErrors,
    props,
    setSubmitting,
  }: FormikBag<IResetPasswordContainerProps, IResetPasswordValue>,
) => {
  setSubmitting(true);
  try {
    await props.handleReset(values);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (e: any) {
    setErrors(e);
  }
};

const ResetPasswordContainer = withFormik<
  IResetPasswordContainerProps,
  IResetPasswordValue
>({
  mapPropsToValues: () => {
    return { password: "", confirm_password: "", reset_token: "" };
  },
  validate: checkValueError(validateFields),
  handleSubmit: onSubmit,
  validateOnChange: true,
})(ResetPasswordForm);

export default ResetPasswordContainer;
