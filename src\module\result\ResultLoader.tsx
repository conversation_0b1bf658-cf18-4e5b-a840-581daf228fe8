import { Skeleton } from "@/components/ui/skeleton";
import React from "react";

const ResultLoader = () => {
  return (
    <div>
      <div className="mt-20 grid grid-cols-1 gap-5 lg:grid-cols-3">
        <Skeleton className="h-40 rounded-sm bg-gray-200" />
        <Skeleton className="h-40 rounded-sm bg-gray-200" />
        <Skeleton className="h-40 rounded-sm bg-blue-100" />
      </div>
      <div className="mt-5 flex flex-col [&>*:nth-child(even)]:bg-gray-200 [&>*:nth-child(odd)]:bg-blue-100">
        <Skeleton className="h-10 w-full lg:h-12" />
        <Skeleton className="h-10 w-full lg:h-12" />
        <Skeleton className="h-10 w-full lg:h-12" />
        <Skeleton className="h-10 w-full lg:h-12" />
        <Skeleton className="h-10 w-full lg:h-12" />
        <Skeleton className="h-10 w-full lg:h-12" />
      </div>
    </div>
  );
};

export default ResultLoader;
