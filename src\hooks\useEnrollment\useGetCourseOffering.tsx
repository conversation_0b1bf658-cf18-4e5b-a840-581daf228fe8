/* eslint-disable @typescript-eslint/no-explicit-any */
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { getCourseOffering } from "../../api/repository/Enrollment/enrollment";
import { ICourseOfferingParams } from "../../api/repository/Enrollment/enrollment.d";

export const useGetCourseOffering = ({
  programme_id,
  atp_id,
  level,
  programme_intake_id,
}: ICourseOfferingParams) => {
  const query = useQuery({
    queryKey: [
      "getCourseOffering",
      { programme_id, programme_intake_id, atp_id, level },
    ],
    queryFn: () =>
      getCourseOffering({ programme_id, atp_id, level, programme_intake_id }),
    retry: false,
    enabled: !!programme_id && !!atp_id && !!level && !!programme_intake_id,
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
    select: (response) => {
      const data = response.data;
      return data;
    },
  });
  return query;
};
