import { NextRequest } from "next/server";
import {
  getCASTicket,
  getUserByAccessToken,
  isTicketInvalid,
  refreshToken,
  validateCasService,
} from "src/module/auth/CAS/cas.utils";

export async function GET(req: NextRequest) {
  const reqUrl = req.url;
  const { searchParams } = new URL(reqUrl);
  const service = searchParams.get("service");
  const ticket = searchParams.get("ticket");

  if (!ticket || !service) {
    return new Response("no\n");
  }
  const urlDecodedService = decodeURIComponent(service);
  const validService = await validateCasService(urlDecodedService);
  if (!validService) {
    return new Response("no\n");
  }

  try {
    const casTicket = await getCASTicket(ticket);
    if (await isTicketInvalid(casTicket, "ST")) {
      return new Response("no\n");
    }
    const tokenDetails = await refreshToken(casTicket.refresh_token);
    const user = await getUserByAccessToken(tokenDetails.access_token);
    if (user && casTicket && casTicket.cas_service_id === validService.id) {
      return new Response(`yes\n${user.contact_information.email}\n`);
    }
  } catch (e) {
    console.error(e);
  }

  return new Response("no\n");
}
