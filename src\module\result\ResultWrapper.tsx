"use client";
import React, { useMemo } from "react";
import ResultContainer from "./ResultContainer";
import { useStudentResult } from "@/hooks/useStudentResults/useStudentResult";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import ResultLoader from "./ResultLoader";
import { useStudentCourse } from "@/hooks/useStudent/useStudentCourse";

const ResultWrapper = () => {
  const student = useAuthStore((state) => state.student);
  const user = useAuthStore((state) => state.user);

  const studentId: string = user?.id ?? "";
  const { data: studentProgramme, isLoading: isLoadingCourse } =
    useStudentCourse({
      studentId,
    });
  const activeProgramme = useMemo(
    () => studentProgramme?.[0],
    [studentProgramme],
  );

  const { data, isLoading } = useStudentResult({
    student_id: student?.personal_details?.id,
    programme_id: activeProgramme?.programme_id,
  });
  return (
    <div>
      {isLoading || isLoadingCourse ? (
        <ResultLoader />
      ) : (
        <ResultContainer data={data!} />
      )}
    </div>
  );
};

export default ResultWrapper;
