import { ReactNode } from "react";

export interface ITableData {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

export interface ITableProps {
  titleList: string[];
  pathList: string[];
  data: ITableData[];
  isLoading?: boolean;
  footer?: string | ReactNode;
  variant?: string;
  classHeader?: string;
  classFooter?: string;
}

export interface TableHeaderProps {
  titleList: string[];
  classHeader?: string;
}

export interface TableBodyProps {
  pathList: string[];
  data: ITableData[];
  isLoading?: boolean;
}
