import { Box, Text, useBreakpointValue } from "@chakra-ui/react";
import { ReactNode } from "react";

export const DashboardBanner = ({
  image,
  title,
  desc,
  buttonCTA,
  titleColor = "#C9B199",
  descColor = "#fff",
}: {
  image: string;
  title: string;
  desc: string;
  buttonCTA?: ReactNode;
  titleColor?: string;
  descColor?: string;
}) => {
  const backgroundImage = useBreakpointValue({
    base: undefined,
    md: `url(${image})`,
  });

  return (
    <Box>
      <Box
        bgImage={backgroundImage}
        className={`flex h-full flex-col justify-center rounded-2xl bg-cover bg-center bg-no-repeat md:h-[300px] md:pl-[50%] md:pr-10 xl:pr-14`}
      >
        <Text
          color={titleColor}
          fontSize="12px"
          fontWeight={800}
          textTransform="uppercase"
          letterSpacing={2.5}
        >
          {title}
        </Text>
        <Text
          color={{ base: "#0a3150", md: descColor }}
          fontSize="24px"
          fontWeight={700}
          mt={2}
        >
          {desc}
        </Text>
        <Box>{buttonCTA}</Box>
      </Box>
    </Box>
  );
};
