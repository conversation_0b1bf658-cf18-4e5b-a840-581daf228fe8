/* eslint-disable @typescript-eslint/no-explicit-any */
import axios, { AxiosResponse } from "axios";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";

const TIMEOUT = 30000;

export const MIVA_SIS_SERVICE_ENDPOINT =
  process.env.NEXT_PUBLIC_SIS_SERVICE_ENDPOINT ||
  "https://sis-be.staging.miva.university";

const baseApi = axios.create({
  baseURL: MIVA_SIS_SERVICE_ENDPOINT,
  timeout: TIMEOUT, // Set a timeout
  headers: {
    "Content-Type": "application/json",
    "X-Origin-Portal": "student",
  },
});

// Request interceptor
baseApi.interceptors.request.use(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  async (config: any) => {
    // Modify the request config before sending
    // For example, you can add headers, authentication tokens, etc.
    const accessToken = useAuthStore.getState().access_token;

    const newConfig = { ...config };
    if (newConfig.headers && !newConfig.headers[`Authorization`]) {
      newConfig.headers[`Authorization`] = `Bearer ${accessToken}`;
    }
    return newConfig;
  },
  (error: any) => {
    // Handle request error
    return Promise.reject(error);
  },
);

// Response interceptor
baseApi.interceptors.response.use(
  (response: AxiosResponse<AxiosResponse<any>>) => {
    return response;
  },
  (error) => {
    // Handle response error
    if (error.response) {
      // The request was made, but the server responded with a status code
      // that falls out of the range of 2xx
      if (error.response.status == 401) {
        useAuthStore.setState((prev) => ({
          ...prev,
          access_token: "",
          refresh_token: "",
          user: undefined,
        }));
        window.location.href = "/login";
      }
    } else if (error.request) {
      // The request was made, but no response was received
      console.error("No response received:", error.request);
    } else {
      console.error("Request setup error:", error.message);
    }

    return Promise.reject(error);
  },
);

// Without authentication and interceptors
const authApi = axios.create({
  baseURL: MIVA_SIS_SERVICE_ENDPOINT,
  timeout: TIMEOUT, // Set a timeout
  headers: {
    "Content-Type": "application/json",
    "X-Origin-Portal": "student",
  },
});

export { baseApi, authApi };
