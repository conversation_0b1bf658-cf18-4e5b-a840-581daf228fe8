/* eslint-disable @typescript-eslint/no-explicit-any */

import React, { useState, useEffect, useRef } from "react";
import InfiniteScroll from "react-infinite-scroll-component";
import { useExamLocationListInfiniteScroll } from "./hooks/useExamLocationListInfiniteScroll";
import { ReloadIcon } from "@radix-ui/react-icons";
import { IExamLocation } from "../../../api/repository/examLocation";

const ExamLocationsList = ({ handleChange }: { handleChange: any }) => {
  const {
    search,
    setSearch,
    examLocations,
    loadMoreExamLocations,
    hasMore,
    setExamLocation,
  } = useExamLocationListInfiniteScroll();

  const [isOpen, setIsOpen] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<string | undefined>(
    undefined,
  );
  const dropdownRef = useRef<HTMLDivElement>(null);

  const onSearchChange = (keyword: string) => {
    setSearch(keyword);
  };

  const handleLocationSelect = (location: IExamLocation) => {
    setSelectedLocation(location.Center);
    setExamLocation(location);
    handleChange({ target: { name: "exam_center", value: location?.Id } });
    setIsOpen(false);
  };

  const handleClear = () => {
    setSelectedLocation(undefined);
    setSearch("");
    setExamLocation(undefined);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (!selectedLocation) {
      setSearch("");
    }
  }, [selectedLocation]);

  return (
    <div className="relative" ref={dropdownRef}>
      <div className="relative mt-2">
        <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19.07 14.8292L17 12.7092C16.5547 12.286 15.9931 12.0055 15.3872 11.904C14.7813 11.8024 14.1589 11.8843 13.6 12.1392L12.7 11.2392C13.7605 9.82213 14.2449 8.05589 14.0555 6.29604C13.8662 4.5362 13.0172 2.91343 11.6794 1.75441C10.3417 0.595384 8.61452 -0.0138341 6.84565 0.0493935C5.07678 0.112621 3.39754 0.843599 2.14596 2.09518C0.894381 3.34676 0.163402 5.026 0.100175 6.79487C0.0369472 8.56374 0.646165 10.2909 1.80519 11.6286C2.96421 12.9664 4.58699 13.8154 6.34683 14.0048C8.10667 14.1941 9.87291 13.7098 11.29 12.6492L12.18 13.5392C11.8951 14.0988 11.793 14.7338 11.8881 15.3545C11.9831 15.9753 12.2706 16.5505 12.71 16.9992L14.83 19.1192C15.3925 19.681 16.155 19.9966 16.95 19.9966C17.745 19.9966 18.5075 19.681 19.07 19.1192C19.3557 18.8398 19.5828 18.5061 19.7378 18.1378C19.8928 17.7694 19.9726 17.3738 19.9726 16.9742C19.9726 16.5746 19.8928 16.179 19.7378 15.8106C19.5828 15.4423 19.3557 15.1086 19.07 14.8292ZM10.59 10.5892C9.89023 11.2872 8.99929 11.7622 8.02973 11.9541C7.06017 12.146 6.05549 12.0462 5.14259 11.6674C4.2297 11.2886 3.44955 10.6477 2.9007 9.82575C2.35185 9.00377 2.05893 8.03758 2.05893 7.04921C2.05893 6.06084 2.35185 5.09464 2.9007 4.27267C3.44955 3.4507 4.2297 2.80983 5.14259 2.43102C6.05549 2.05221 7.06017 1.95246 8.02973 2.14436C8.99929 2.33626 9.89023 2.81121 10.59 3.50921C11.0556 3.97367 11.4251 4.52542 11.6771 5.13287C11.9292 5.74032 12.0589 6.39154 12.0589 7.04921C12.0589 7.70688 11.9292 8.35809 11.6771 8.96555C11.4251 9.573 11.0556 10.1248 10.59 10.5892ZM17.66 17.6592C17.567 17.7529 17.4564 17.8273 17.3346 17.8781C17.2127 17.9289 17.082 17.955 16.95 17.955C16.818 17.955 16.6873 17.9289 16.5654 17.8781C16.4436 17.8273 16.333 17.7529 16.24 17.6592L14.12 15.5392C14.0263 15.4462 13.9519 15.3356 13.9011 15.2138C13.8503 15.0919 13.8242 14.9612 13.8242 14.8292C13.8242 14.6972 13.8503 14.5665 13.9011 14.4446C13.9519 14.3228 14.0263 14.2122 14.12 14.1192C14.213 14.0255 14.3236 13.9511 14.4454 13.9003C14.5673 13.8495 14.698 13.8234 14.83 13.8234C14.962 13.8234 15.0927 13.8495 15.2146 13.9003C15.3364 13.9511 15.447 14.0255 15.54 14.1192L17.66 16.2392C17.7537 16.3322 17.8281 16.4428 17.8789 16.5646C17.9296 16.6865 17.9558 16.8172 17.9558 16.9492C17.9558 17.0812 17.9296 17.2119 17.8789 17.3338C17.8281 17.4556 17.7537 17.5662 17.66 17.6592Z"
              fill="#8EA0AF"
            />
          </svg>
        </div>
        <input
          type="text"
          placeholder="Search Exam Locations"
          onFocus={() => setIsOpen(true)}
          value={selectedLocation || search}
          onChange={(e) => onSearchChange(e.target.value)}
          className="block w-full rounded-md border border-gray-300 py-2 pl-10 pr-10 text-sm focus:outline-none focus:ring-1 focus:ring-gray-800"
        />
        {selectedLocation && (
          <div
            className="absolute inset-y-0 right-0 flex cursor-pointer items-center pr-3"
            onClick={handleClear}
          >
            <span className="uil uil-times text-gray-400"></span>
          </div>
        )}
      </div>

      {isOpen && (
        <div
          className="absolute left-0 right-0 z-50 mt-2 max-h-[200px] overflow-y-auto rounded-md border border-[#E7EAEE] bg-white shadow-md"
          id="scrollableList"
        >
          <InfiniteScroll
            dataLength={examLocations.length}
            next={loadMoreExamLocations}
            hasMore={hasMore}
            loader={
              <div className="flex items-center px-4 py-2 text-sm font-medium text-gray-700">
                <ReloadIcon className="mr-2 h-4 w-4 animate-spin" /> Loading...
              </div>
            }
            endMessage={
              <div className="p-2 text-center text-xs text-[#A0AEC0]">
                {examLocations.length > 0
                  ? "All exam locations loaded"
                  : "No exam locations fetched"}
              </div>
            }
            scrollableTarget="scrollableList"
          >
            {examLocations.map((location) => (
              <div
                key={location.Id}
                onClick={() => handleLocationSelect(location)}
                className="cursor-pointer p-2 hover:bg-gray-100"
              >
                <div className="font-medium">{location.Center}</div>
              </div>
            ))}
          </InfiniteScroll>
        </div>
      )}
    </div>
  );
};

export default ExamLocationsList;
