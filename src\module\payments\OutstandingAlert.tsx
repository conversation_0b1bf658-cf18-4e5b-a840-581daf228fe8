/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import { Transaction } from "./Payments";
import { formatCurrency } from "@/lib/utils/helpers";
import { formatDate } from "@/lib/utils/helpers";
import { Button } from "@/components/ui/button";
import { MIVA_SIS_PAYMENT_URL } from "@/constants/api";
import { Item } from "@radix-ui/react-select";

export function OustandingAlert({
  data,
  oldestOutstanding,
}: {
  data: Transaction[];
  oldestOutstanding: Transaction;
}) {
  const accessToken = useAuthStore.getState().access_token;
  const user = useAuthStore((state) => state.student);
  const handleNavigate = (student_id: string, installment_plan_id: string) => {
    const url = `${MIVA_SIS_PAYMENT_URL}/payment-with-installment?studentSisId=${user?.student_profile.student_id}&userToken=${accessToken}&installmentId=${installment_plan_id}`;
    window.open(url, "_blank"); // Open in a new tab
  };

  const cleanDueDate = (dateString: string) => {
    if (!dateString) return "";
    // First remove duplicate timezone
    let cleaned = dateString.replace(/(\+\d{4})\s+\+\d{4}$/, "$1");
    // Convert to ISO format by removing milliseconds and fixing timezone format
    cleaned = cleaned.replace(/(\.\d+)?\s*(\+\d{4})$/, "$2");
    cleaned = cleaned.replace(" ", "T"); // Add 'T' separator for ISO format
    return cleaned;
  };

  return (
    <>
      {data?.map((item: Transaction) => (
        <Alert
          key={item.fee_installment_id}
          variant="destructive"
          className="mt-5 border border-[#E83831] bg-[#FDEBEA] p-5"
        >
          <AlertDescription className="flex flex-col items-start justify-between lg:flex-row">
            <div className="flex flex-col justify-between lg:gap-y-5">
              <p> You have an outstanding payment of</p>
              <h2 className="text-4xl font-black lg:text-5xl">
                {formatCurrency(
                  parseInt(item?.amount_in_naira),
                  item.currency.toUpperCase(),
                )}
              </h2>
            </div>
            <div className="mt-5 flex flex-col justify-between lg:mt-0 lg:gap-y-2">
              <p> Next Payment Date</p>
              <p className="text-xl font-black">
                {" "}
                {formatDate(cleanDueDate(item.due_date))}
              </p>
              <Button
                disabled={item !== oldestOutstanding}
                onClick={() =>
                  handleNavigate(item.student_id, item.installment_plan_id)
                }
                className={`rounded-[8px] p-[8px] text-[12px] font-bold text-[#ffffff] ${!item.outstanding ? "bg-[#E83831]" : "bg-[#103150]"}`}
              >
                Make Payment
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      ))}
    </>
  );
}
