/* eslint-disable react-hooks/exhaustive-deps */
import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useToast } from "@chakra-ui/react";
import axios from "axios";
import { extractAxiosError } from "@/lib/utils/helpers";
import { fetchAllCourses } from "../../api/repository/Courses/courses";

interface IUseStudentByEnrolledCourses {
  studentId: string;
}

export const useStudentCourse = ({
  studentId,
}: IUseStudentByEnrolledCourses) => {
  const query = useQuery({
    queryKey: ["fetchAllCourses", studentId],
    queryFn: () => fetchAllCourses({ studentId: studentId as string }),
    enabled: !!studentId,
    select: (response) => response.data,
  });
  const toast = useToast();
  useEffect(() => {
    if (query.error && axios.isAxiosError(query.error)) {
      toast({
        description: extractAxiosError(query.error),
        status: "error",
      });
    }
  }, [query.isError]);

  return query;
};
