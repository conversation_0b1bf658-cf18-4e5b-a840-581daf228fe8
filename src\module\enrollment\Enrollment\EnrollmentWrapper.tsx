"use client";

import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import PageTitle from "@/components/commons/PageTitle/PageTitle";
import { Box, Text } from "@chakra-ui/react";
import CourseOfferingList from "./components/CourseOfferingList";

import "./Checkbox.scss";
import { useEnrollment } from "./useEnrollment";
import { Suspense } from "react";
import CommonLoading from "@/components/commons/CommonLoading/CommonLoading";
import { beautify } from "@/lib/utils/string";

const EnrollmentWrapper = () => {
  const {
    enrollmentData: {
      programmeName = "",
      programmeId = "",
      programmeIntakeId = "",
      level = "",
    },
    activeSemester,
    isLoadingApplication,
    isLoadingSemester,
    isLoadingActiveEnrollment,
    semestersId,
    courseChecked,
    setCourseChecked,
    handleCreateEnrollment,
    paymentCompletionIsFetching,
  } = useEnrollment();

  return (
    <Box>
      <PageTitle>
        Enrolling for {programmeName} ({beautify(level)}) -{" "}
        {beautify(activeSemester)}
      </PageTitle>
      <CourseOfferingList
        loading={
          isLoadingApplication ||
          isLoadingSemester ||
          isLoadingActiveEnrollment ||
          paymentCompletionIsFetching
        }
        programme_id={programmeId}
        programme_intake_id={programmeIntakeId}
        atp_id={semestersId}
        level={level}
        courseChecked={courseChecked}
        setCourseChecked={setCourseChecked}
        semester={activeSemester}
      />
      <Box textAlign="right" mt={4}>
        <ButtonCTA
          isDisabled={courseChecked.length === 0}
          onClick={() => handleCreateEnrollment()}
          fontSize="12px"
          padding="8px 24px"
          height="auto"
        >
          Complete Enrollment
          <Text as="span" fontSize={16} ml={2}>
            <i className="uil uil-arrow-right"></i>
          </Text>
        </ButtonCTA>
      </Box>
    </Box>
  );
};

const EnrollmentWrapperWithSuspense = () => (
  <Suspense fallback={<CommonLoading size="medium" />}>
    <EnrollmentWrapper />
  </Suspense>
);

export default EnrollmentWrapperWithSuspense;
