import CommonDefermentInputs from "src/module/deferments/CreateDeferment/CommonDefermentInputs";
import { ICreateDefermentForm } from "src/module/deferments/CreateDeferment/CreateDefermentForm";

const SemesterDefermentForm = ({
  values,
  handleChange,
  handleBlur,
  touched,
  errors,
  setFieldValue,
  isSubmitting,
  isSubmitEnabled,
}: ICreateDefermentForm) => {
  return (
    <>
      <CommonDefermentInputs
        handleChange={handleChange}
        values={values}
        handleBlur={handleBlur}
        touched={touched}
        errors={errors}
        setFieldValue={setFieldValue}
        isSubmitting={isSubmitting}
        isSubmitEnabled={isSubmitEnabled}
      />
    </>
  );
};

export default SemesterDefermentForm;
