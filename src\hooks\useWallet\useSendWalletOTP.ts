import { useMutation } from "@tanstack/react-query";
import { useToast } from "@chakra-ui/react";
import axios from "axios";
import { extractAxiosError } from "@/lib/utils/helpers";
import { sendWalletOTP } from "../../api/repository/wallet";
import { SendWalletOTPRequest } from "../../api/repository/wallet.d";

export const useSendWalletOTP = () => {
  const toast = useToast();

  const mutation = useMutation({
    mutationFn: (payload: SendWalletOTPRequest) => sendWalletOTP(payload),
    onSuccess: (data) => {
      toast({
        description: data.message || "OTP sent successfully",
        status: "success",
      });
    },
    onError: (error) => {
      if (axios.isAxiosError(error)) {
        toast({
          description: extractAxiosError(error),
          status: "error",
        });
      } else {
        toast({
          description: "Failed to send OTP. Please try again.",
          status: "error",
        });
      }
    },
  });

  return {
    sendOTP: mutation.mutate,
    isLoading: mutation.isPending,
    error: mutation.error,
    data: mutation.data,
    isSuccess: mutation.isSuccess,
  };
};
