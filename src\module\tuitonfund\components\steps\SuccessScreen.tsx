"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import Image from "next/image";

const SuccessScreen: React.FC = () => {
  const router = useRouter();

  return (
    <div className="h-[525px] w-[320px] rounded-lg bg-[#092D49] py-8 text-center text-white lg:h-[525px] lg:w-[600px] lg:rounded-[24px]">
      <div className="mb-6 flex h-1/2 justify-center">
        <div className="inline-flex self-end p-8">
          <Image
            src="/images/icons/empty-wallet-tick.svg"
            alt="Success"
            width={64}
            height={64}
            className="h-[147px] w-[165px]"
          />
        </div>
      </div>
      <div className="m-auto h-1/2 w-[200px] lg:w-[400px]">
        <h2 className="mb-2 text-2xl font-bold">You&apos;re All Set!</h2>

        <p className="mb-8">
          Your Tuition Fund is ready to go. Start paying your
          <br />
          fees in bits – at your pace, on your terms.
        </p>

        <Button
          variant="primary"
          className="bg-white px-6 text-[#092D49] hover:bg-white/30"
          onClick={() => router.push("/tuition-fund")}
        >
          <span className="font-medium">Go to Dashboard</span>
        </Button>
      </div>
    </div>
  );
};

export default SuccessScreen;
