"use client";

import CommonLoading from "@/components/commons/CommonLoading/CommonLoading";
import Pagination from "@/components/commons/CommonPagination/CommonPagination";
import TransactionTable from "@/components/commons/Table/TransactionTable";
import { BaseColor } from "@/constants/colors";
import { useStudentNextInstallment } from "@/hooks/useStudent/useStudentDashboard";
import { Box } from "@chakra-ui/react";
import { NextInstallment } from "src/api/repository/student";
import DashboardTitle from "../dashboard/components/DashboardTitle";
import { NextInstallmentAlert } from "./NextInstallmentAlert";
import { OustandingAlert } from "./OutstandingAlert";
import { setTagBgColor } from "./payments.utils";
import { usePayments } from "./usePayments";
import { NewSessionBanner } from "./NewSessionBanner";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import { useStudentCourse } from "@/hooks/useStudent/useStudentCourse";
import { useMemo } from "react";

const PaymentWrapper = () => {
  const {
    isLoading,
    filteredOutstandingTransactions,
    oldestOutstanding,
    allTransactions,
    params,
    handlePageChange,
    handleItemsPerPageChange,
    studentTransactions,
  } = usePayments();

  const { nextInstallment, isLoading: nextInstallmentLoading } =
    useStudentNextInstallment();

  // Get current enrollment status
  const user = useAuthStore((state) => state.user);
  const { data: studentEnrollments } = useStudentCourse({
    studentId: user?.id ?? "",
  });

  const currentEnrollment = useMemo(() => {
    if (Array.isArray(studentEnrollments)) {
      return studentEnrollments[0];
    }
    return { status: "" };
  }, [studentEnrollments]);

  const enrollmentStatus = currentEnrollment?.status?.toLowerCase();

  // Check if enrollment status is neither "discontinued" nor "deferred"
  const showNextInstallmentAlert =
    filteredOutstandingTransactions.length === 0 &&
    !["discontinue", "deferred"].includes(enrollmentStatus) &&
    nextInstallment;

  return (
    <div>
      <DashboardTitle title="Payments" />
      {isLoading || nextInstallmentLoading ? (
        <CommonLoading />
      ) : (
        <>
          <NewSessionBanner />
          {/* Red Banner: Outstanding Payments */}
          <OustandingAlert
            data={filteredOutstandingTransactions?.slice(0, 2)}
            oldestOutstanding={oldestOutstanding}
          />

          {/* Green Banner: Next Installment */}
          {showNextInstallmentAlert && (
            <NextInstallmentAlert nextInstallment={nextInstallment} />
          )}

          <div className="mt-8 rounded-[16px] bg-white p-6">
            <Box>
              <TransactionTable
                transactions={allTransactions}
                baseColor={BaseColor.PRIMARY}
                tableBaseColor={BaseColor.PRIMARY}
                tableRowBaseColor={BaseColor.PRIMARY}
                isLoading={isLoading}
                oldestOutstanding={oldestOutstanding}
                setTagBgColor={setTagBgColor}
              />
            </Box>
            <div className="flex justify-end p-5">
              <Pagination
                currentPage={params.page}
                totalPages={studentTransactions?.totalPages ?? 1}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleItemsPerPageChange}
                itemsPerPage={params.perPage}
              />
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default PaymentWrapper;
