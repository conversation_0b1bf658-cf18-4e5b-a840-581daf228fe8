import { APIResponse } from "../../config/api.d";
import { baseApi } from "../../config/api";
import { MODULE_ROUTE, Routes } from "../../config/routes";

import {
  IProgrammeIntakeSemesterParams,
  IProgrammeIntakeSemesterData,
} from "./programmeIntakeSemester.d";

export async function getSemesterByProgrammeIntake({
  programme_intake_id,
}: IProgrammeIntakeSemesterParams): Promise<
  APIResponse<IProgrammeIntakeSemesterData[]>
> {
  try {
    const response = await baseApi.get(Routes[MODULE_ROUTE.MISC].GET_SEMESTER, {
      params: {
        programme_intake_id,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error getSemesterByProgrammeIntake error:", error);
    throw error;
  }
}
