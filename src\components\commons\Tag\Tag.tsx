import { FC } from "react";

import { BaseColor } from "@/constants/colors";
import { TagProps } from "./Tag.d";
import { Tag as ChakraTag } from "@chakra-ui/react";

const Tag: FC<TagProps> = ({
  children,
  baseColor = BaseColor.DEFAULT,
  ...rest
}) => {
  return (
    <ChakraTag
      borderRadius="full"
      px={6}
      py={2}
      backgroundColor={`${baseColor}1a`}
      fontSize={"12px"}
      fontWeight={700}
      textColor={baseColor}
      {...rest}
    >
      {children}
    </ChakraTag>
  );
};

export default Tag;
