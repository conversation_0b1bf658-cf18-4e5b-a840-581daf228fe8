import { Semester } from "../student";

type CourseStatus = "ENROLLED" | "COMPLETED" | "DISCONTINUED";
type EnrollmentStatus = "ENROLLED" | "PENDING";
type GroupType = "CORE" | "ELECTIVE";

export interface Course {
  course_name: string;
  course_code: string;
  credit_unit: number;
  status: CourseStatus;
  score?: number;
  symbol?: string;
  result?: string;
  created_at?: string;
  course_id?: string;
  course_offering_id?: string;
  group_type?: string;
  onEnroll: (id: string) => void;
}

interface CourseStatusSummary {
  completed: number;
  discontinue: number;
  enrolled: number;
}

export interface ProgrammeLevel {
  programme_level_id: string;
  level: string;
  academic_time_period_name: string;
  academic_time_period_type: string;
  start_date: string;
  programme_level_enrollment_id: string;
  status: EnrollmentStatus;
  end_date: string;
  programme_intake_id: string;
  semesters: Semester[];
  course_status_summary: CourseStatusSummary;
  required_units: number;
}

export interface StudentEnrollment {
  student_id: string;
  bundle_name: string;
  enrollment_start_date: string;
  enrollment_end_date: string;
  programme_name: string;
  programme_id: string;
  status: EnrollmentStatus;
  levels: ProgrammeLevel[];
  faculty_name: string;
  student_unique_id: number;
  student_name: string;
  student_image: string;
  enrollment_id: string;
}

export interface EnrollmentData {
  data: StudentEnrollment[];
}
