/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { Box, Button, Divider, Image, Text } from "@chakra-ui/react";
import Link from "next/link";
import { APIResponse } from "src/api/config/api.d";
import { useState } from "react";
import { getApplication } from "../../../api/repository/Application/application";
import { useSideBarStore } from "../../../store/SideBarStore/sideBar";

import Icon from "@/components/commons/Icons/Icon";
import {
  EIconName,
  EIconSize,
  TypeIcon,
} from "@/components/commons/Icons/Icon.enums";
import {
  enrolledMenus,
  unEnrolledMenus,
} from "@/components/templates/SideBar/SideBarMenuData";
import { E_STATUS_APPLICATION } from "@/constants/enums";
import { cn } from "@/lib/utils";
import { usePathname } from "next/navigation";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import { canVisitDashboard } from "src/utils/common";

const SideBarWrapper = () => {
  const pathname = usePathname();
  const user = useAuthStore((state) => state.student);

  const enrollStatus = useAuthStore((state) => state.enrollStatus);
  const isExpanded = useSideBarStore((state) => state.isExpanded);

  return (
    <Box className="lg:min-w-[250px] lg:max-w-[250px] lg:flex-[250px]">
      <Box
        data-aos="slide-right"
        data-aos-duration="200"
        data-aos-delay="0"
        className={cn(
          "fixed left-[-280px] top-0 z-10 flex h-screen flex-col bg-white p-[16px] shadow-sm md:p-[32px] lg:sticky lg:left-0",
          { "left-0": isExpanded },
        )}
      >
        <Box
          gap={3}
          className="left-[20px] top-[14px] hidden lg:static lg:flex"
        >
          <Image
            src="/images/logo-miva-sidebar.png"
            alt="logo-miva-sidebar"
            className="w-[120px] md:w-[136px]"
          />
        </Box>
        <Divider
          mt="29px"
          opacity="1"
          borderColor="#DDE2E7"
          borderBottomWidth="1px"
          position="absolute"
          left={0}
          className="lg:hidden"
        />
        {canVisitDashboard(enrollStatus) ? (
          <div className="mt-[60px] flex flex-grow cursor-pointer flex-col gap-[8px] lg:mt-[48px]">
            {enrolledMenus.map((item) => {
              const isActive = pathname.startsWith(item.to);
              return (
                <Link
                  href={item.to}
                  className={cn(
                    "flex min-h-[44px] items-center justify-start gap-[12px] rounded-[6px] px-[20px] py-[10px] text-[#5B758A] hover:bg-[#E7EAEE]",
                    {
                      "bg-[#E7EAEE] text-[#0A3150]": isActive,
                    },
                  )}
                  key={item.title}
                >
                  <Icon
                    name={isActive ? item.activeIcon : item.icon}
                    size={EIconSize.SM}
                    type={TypeIcon.SVG}
                  />
                  {item.title}
                </Link>
              );
            })}
          </div>
        ) : (
          <div className="mt-[60px] flex flex-grow cursor-pointer flex-col gap-[8px] lg:mt-[48px]">
            {unEnrolledMenus.map((item) => {
              const isActive = pathname.startsWith(item.to);
              return (
                <Link
                  href={item.to}
                  className={cn(
                    "flex min-h-[44px] items-center justify-start gap-[12px] rounded-[6px] px-[20px] py-[10px] text-[#5B758A] hover:bg-[#E7EAEE]",
                    {
                      "bg-[#E7EAEE] text-[#0A3150]": isActive,
                    },
                  )}
                  key={item.title}
                >
                  <Icon
                    name={isActive ? item.activeIcon : item.icon}
                    size={EIconSize.SM}
                    type={TypeIcon.SVG}
                  />
                  {item.title}
                </Link>
              );
            })}
          </div>
        )}

        <Box
          bg="url(/images/help-bg1.png), url(/images/help-bg2.png)"
          alignSelf="flex-end"
          maxW={200}
          bgColor="#0A3150"
          textColor="white"
          padding={4}
          borderRadius={10}
          bgRepeat="no-repeat"
          bgPosition="top left, bottom right"
          bgSize={70}
          position="relative"
        >
          <Box
            position="absolute"
            left={0}
            right={0}
            marginLeft="auto"
            marginRight="auto"
            width={70}
            top="-30px"
          >
            <Icon
              name={EIconName.QUESTION}
              type={TypeIcon.SVG}
              size={EIconSize.XXL_72}
            />
          </Box>
          <Box my={8} textAlign="center" paddingX={3}>
            <Text fontWeight="semibold" fontSize="16px" mb={3}>
              Help Center
            </Text>
            <Text fontSize="14px">
              Having Trouble? Please contact us for more questions.
            </Text>
          </Box>
          <Link
            href={
              user?.application_details?.application_status ===
                E_STATUS_APPLICATION.ACCEPTED ||
              user?.application_details?.application_status ===
                E_STATUS_APPLICATION.ENROLLED
                ? "https://wa.me//+2348168397949"
                : "https://wa.link/4h9qzg"
            }
            target="_blank"
          >
            <Button fontSize={12} bgColor="white">
              <Icon
                name={EIconName.WHATSAPP}
                type={TypeIcon.SVG}
                size={EIconSize.SM_16}
              />
              <span className="ml-1">
                {user?.application_details?.application_status ===
                  E_STATUS_APPLICATION.ACCEPTED ||
                user?.application_details?.application_status ===
                  E_STATUS_APPLICATION.ENROLLED
                  ? "+234 8168397949"
                  : "+234 9132300000"}{" "}
              </span>
            </Button>
          </Link>
        </Box>
      </Box>
    </Box>
  );
};

export default SideBarWrapper;
