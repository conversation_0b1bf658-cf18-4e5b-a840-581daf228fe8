import { useMemo, useState } from "react";
import { IStudentTransactionParam } from "src/api/repository/student";
import { useStudentTransactions } from "@/hooks/useStudent/useStudentDashboard";
import { Transaction } from "./Payments";

export const usePayments = () => {
  const [params, setParams] = useState<IStudentTransactionParam>({
    page: 1,
    perPage: 10,
  });

  const handleItemsPerPageChange = (items: number) => {
    setParams((prevParams) => ({
      ...prevParams,
      perPage: items,
      page: 1, // Reset to the first page when items per page changes
    }));
  };

  const handlePageChange = (page: number) => {
    setParams((prevParams) => ({ ...prevParams, page }));
  };

  const defaultTransaction: Transaction = {
    amount: "0",
    created_at: "",
    currency: "",
    date_paid: "",
    description: "",
    outstanding: false,
    discount: 0,
    payment_id: "",
    payment_external_id: "",
    payment_method: "",
    payment_type: "",
    reference: "",
    payment_status: "",
    student_id: "",
    updated_at: "",
    receipt_id: "",
    receipt_number: "",
    amount_in_naira: "0",
    fee_installment_id: "",
    due_date: "",
    installment_plan_id: "",
  };

  const { data: studentTransactions, isLoading } =
    useStudentTransactions(params);

  const allTransactions: Transaction[] = useMemo(
    () => [
      ...(studentTransactions?.owed_records?.map((owed) => ({
        amount: owed.amount_in_naira.toString(),
        created_at: "",
        currency: "NGN",
        date_paid: "",
        description: `Owed: ${owed.installment_name}`,
        outstanding: true,
        discount: 0,
        payment_id: "",
        payment_external_id: "",
        payment_method: "",
        payment_type: "",
        reference: "",
        payment_status: "outstanding",
        student_id: "",
        updated_at: "",
        receipt_id: "",
        receipt_number: "",
        amount_in_naira: owed.amount_in_naira.toString(),
        fee_installment_id: owed.fee_installment_id,
        due_date: owed.due_date,
        status: "outstanding",
        installment_name: owed.installment_name,
        programme: owed.programme,
        installment_plan_id: owed.installment_plan_id,
      })) || []),
      ...(studentTransactions?.paid?.map((paid) => ({
        amount: paid.amount.toString(),
        created_at: paid.created_at,
        currency: paid.currency,
        date_paid: paid.date_paid,
        description: paid.description,
        outstanding: false,
        discount: paid.discount,
        payment_id: paid.payment_id,
        payment_external_id: paid.payment_external_id,
        payment_method: paid.payment_method,
        payment_type: paid.payment_type,
        reference: paid.reference,
        payment_status: paid.status,
        student_id: paid.student_id,
        updated_at: paid.updated_at,
        receipt_id: paid.receipt_id,
        receipt_number: paid.receipt_num,
        amount_in_naira: paid.amount_in_naira.toString(),
        fee_installment_id: paid.fee_installment_id,
        due_date: paid.due_date,
        status: paid.status,
        installment_name: paid.installment_name,
        programme: paid.programme,
        installment_plan_id: "",
      })) || []),
    ],
    [studentTransactions?.owed_records, studentTransactions?.paid],
  );

  const filteredOutstandingTransactions = useMemo(
    () =>
      allTransactions
        ?.filter(
          (item) => item?.payment_status?.toLowerCase() === "outstanding",
        )
        ?.sort(
          (a, b) =>
            new Date(a.due_date).getTime() - new Date(b.due_date).getTime(),
        ),
    [allTransactions],
  );

  return {
    oldestOutstanding:
      filteredOutstandingTransactions.length > 0
        ? filteredOutstandingTransactions[0]
        : defaultTransaction,
    isLoading,
    filteredOutstandingTransactions,
    allTransactions,
    params,
    handlePageChange,
    handleItemsPerPageChange,
    studentTransactions,
  };
};
