/* eslint-disable @typescript-eslint/no-explicit-any */
import { APIResponse } from "src/api/config/api.d";
import { baseApi } from "src/api/config/api";
import { MODULE_ROUTE, Routes } from "src/api/config/routes";

export const withdrawalRequest = async (values: IWithdrawalFormValues) => {
  try {
    const response = await baseApi.post<APIResponse<IWithdrawalFormValues>>(
      Routes[MODULE_ROUTE.STUDENT].TUITION_FUND_WITHDRAWAL_REQUEST,
      values,
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching courses:", error);
    throw error;
  }
};
