"use client";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import { formatCurrency, formatDate } from "@/lib/utils/helpers";
import { Button } from "@/components/ui/button";
import { MIVA_SIS_PAYMENT_URL } from "@/constants/api";

export function NextInstallmentAlert({
  nextInstallment,
}: {
  nextInstallment: any;
}) {
  const accessToken = useAuthStore.getState().access_token;
  const user = useAuthStore((state) => state.student);

  if (!nextInstallment) return null;

  // Always use the amount in naira
  const amount = nextInstallment.amount_in_naira;

  const handleNavigate = (installment_plan_id: string) => {
    const url = `${MIVA_SIS_PAYMENT_URL}/payment-with-installment?studentSisId=${user?.student_profile.student_id}&userToken=${accessToken}&installmentId=${installment_plan_id}`;
    window.open(url, "_blank");
  };

  return (
    <Alert className="mt-5 border border-[#28A745] bg-[#EAF9EB] p-5">
      <AlertDescription className="flex flex-col items-start justify-between lg:flex-row">
        <div className="flex flex-col justify-between lg:gap-y-5">
          <p>Your next installment is due on</p>
          <h2 className="text-4xl font-black lg:text-5xl">
            {formatCurrency(parseInt(amount || "0"), "NGN")}
          </h2>
        </div>
        <div className="mt-5 flex flex-col justify-between lg:mt-0 lg:gap-y-2">
          <p>Due Date</p>
          <p className="text-xl font-black">
            {formatDate(nextInstallment.due_date)}
          </p>
          <Button
            onClick={() => handleNavigate(nextInstallment.installment_plan_id)}
            className="rounded-[8px] bg-[#28A745] p-[8px] text-[12px] font-bold text-[#ffffff]"
          >
            Make Payment
          </Button>
        </div>
      </AlertDescription>
    </Alert>
  );
}
