import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import { Box, Text, Skeleton } from "@chakra-ui/react";
import { useNewSession } from "./useNewSession";
import { BaseColor } from "@/constants/colors";
import { DashboardBanner } from "../dashboard/components/DashboardBanner";

export const NewSessionBanner = () => {
  const {
    newSessionIsActive,
    handleNewLevelEnrolment,
    paymentCompletionIsFetching,
    paymentCompletion,
  } = useNewSession();

  return (
    <>
      {paymentCompletionIsFetching && <Skeleton my={8} height="60px" />}
      {newSessionIsActive && !paymentCompletionIsFetching && (
        <Box mt={8}>
          <DashboardBanner
            image="/images/next-level.png"
            title="WELCOME TO A NEW SESSION!"
            desc={
              paymentCompletion?.type == "payment_completed"
                ? "Register your courses to continue your academic journey"
                : "Pay your tuition and enrol in courses to complete your academic journey."
            }
            buttonCTA={
              <ButtonCTA
                onClick={handleNewLevelEnrolment}
                fontSize={12}
                height="auto"
                py={4}
                px={6}
                color="white"
                mt={5}
                bg={
                  paymentCompletion?.type == "payment_completed"
                    ? BaseColor.SUCCESS
                    : "#E83831"
                }
              >
                {paymentCompletion?.type == "payment_completed"
                  ? "Register your courses"
                  : "Pay tuition"}
              </ButtonCTA>
            }
          />
        </Box>
      )}
    </>
  );
};
