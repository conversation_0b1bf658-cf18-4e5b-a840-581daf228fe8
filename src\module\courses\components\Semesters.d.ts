/* eslint-disable @typescript-eslint/no-explicit-any */
import { Semester } from "src/api/repository/student";
import { Course } from "./Courses.type";
import { ProgrammeLevel } from "src/api/repository/Courses/courses.d";

export interface IDataSemester {
  status?: string;
  courses: Course[];
}

export interface ISemestersProps {
  status?: string;
  name?: string;
  semesters: Semester[];
  semesterList?: any[];
  otherDetails: {
    student_id: string;
    status: string;
    programme_id: string;
  };
  activeLevel: ProgrammeLevel;
}
