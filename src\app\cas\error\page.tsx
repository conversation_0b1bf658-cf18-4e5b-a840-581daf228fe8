"use client";
import { Alert, AlertDescription } from "@/components/ui/alert";

const CASErrorPage = () => {
  return (
    <div className="m-8 flex justify-center">
      <Alert
        variant="destructive"
        className="mt-5 border border-[#E83831] bg-[#FDEBEA] p-5"
      >
        <AlertDescription className="flex flex-col items-start justify-between">
          <div className="flex flex-col justify-between lg:gap-y-5">
            <p className="text-lg font-bold">CAS access denied</p>
          </div>
          <div className="mt-5 flex flex-col justify-between lg:mt-0 lg:gap-y-2">
            <p>
              You are unable to access CAS please contact your CAS administrator
            </p>
          </div>
        </AlertDescription>
      </Alert>
    </div>
  );
};

export default CASErrorPage;
