export type Transaction = {
  amount: string;
  created_at: string;
  programme_name?: string;
  currency: string;
  date_paid: string;
  description: string;
  outstanding?: boolean;
  discount: number;
  payment_id: string;
  payment_external_id: string;
  payment_method: string;
  payment_type: string;
  reference: string;
  payment_status: string;
  student_id: string;
  updated_at: string;
  receipt_id: string;
  receipt_number: string;
  receipt_num?: string;
  programme?: string;
  amount_in_naira: string;
  fee_installment_id: string;
  due_date: string;
  status?: string;
  installment_name?: string;
  installment_plan_id?: string;
};

interface TableProps {
  transactions: Transaction[];
  baseColor?: string;
  tableBaseColor: string;
  tableRowBaseColor: string;
  setTagBgColor: (status: string) => string;
  transactionTDStyles?: React.CSSProperties;
  isLoading: boolean;
  oldestOutstanding: Transaction;
}
