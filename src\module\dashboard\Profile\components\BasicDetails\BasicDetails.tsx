import React from "react";
import { Grid, Text, Box, Flex } from "@chakra-ui/react";
import { BaseColor } from "@/constants/colors";

import InputField from "@/components/commons/InputField/InputField";
import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import DatePickerInput from "@/components/commons/InputField/DateInput";
import SelectInput from "@/components/commons/InputField/SelectInput";
import PhoneInputFlag from "@/components/commons/InputField/PhoneInputFlag";
import { FormikProps } from "formik";
import { InputError } from "@/components/commons/InputField/InputError";
import { IBasicDetails } from "../../Profile";
import { getCountries, getCountry, getStates } from "country-state-picker";
import { cn } from "@/lib/utils";
import { EButtonType } from "@/components/commons/ButtonCTA/ButtonCTA.d";
import { capitalizeFirstLetter } from "@/lib/utils/helpers";

const RadioBox = ({
  selected,
  children,
  isDisabled,
}: {
  selected: boolean;
  children: React.ReactNode;
  isDisabled?: boolean;
}): React.ReactElement => (
  <Box
    className={cn(
      "w-full border border-[#0A3150]",
      isDisabled && "cursor-not-allowed opacity-40",
    )}
    borderRadius={"8px"}
    paddingX={"18px"}
    paddingY={"9px"}
    display={"flex"}
    border={selected ? "1px solid #0A3150" : "1px solid #CED6DC"}
    background={selected ? "#DDE2E7" : "white"}
    fontWeight={selected ? 600 : 400}
    gap={2}
  >
    {children}
  </Box>
);

export const BasicDetails = (props: FormikProps<IBasicDetails>) => {
  const [readOnly, setReadOnly] = React.useState(true);
  const industryOptions = [
    { value: "advertising_marketing", name: "Advertising and Marketing" },
    { value: "aerospace", name: "Aerospace" },
    { value: "agriculture_forestry", name: "Agriculture and Forestry" },
    {
      value: "arts_entertainment_recreation",
      name: "Arts, Entertainment, and Recreation",
    },
    {
      value: "banking_accounting_finance",
      name: "Banking, Accounting & Finance",
    },
    {
      value: "business_services",
      name: "Business Services",
    },
    { value: "construction", name: "Construction" },
    { value: "ecommerce", name: "E-Commerce" },
    { value: "education", name: "Education" },
    { value: "energy_utilities", name: "Energy and Utilities" },
    { value: "engineering", name: "Engineering" },
    { value: "fashion", name: "Fashion" },
    { value: "food_beverage", name: "Food and Beverage" },
    { value: "government", name: "Government" },
    { value: "healthcare", name: "Healthcare" },
    { value: "hospitality_tourism", name: "Hospitality and Tourism" },
    { value: "information_technology", name: "Information Technology" },
    { value: "insurance", name: "Insurance" },
    { value: "law", name: "Law" },
    { value: "manufacturing", name: "Manufacturing" },
    { value: "media_entertainment", name: "Media and Entertainment" },
    {
      value: "nonprofit_social_services",
      name: "Non-profit and Social Services",
    },
    { value: "oil_gas", name: "Oil and Gas" },
    { value: "retail", name: "Retail" },
    { value: "science_pharmaceuticals", name: "Science and Pharmaceuticals" },
    { value: "telecommunication", name: "Telecommunication" },
    {
      value: "transportation_logistics",
      name: "Transportation and Logistics",
    },
    { value: "others", name: "Others" },
  ];
  const {
    touched,
    errors,
    handleSubmit,
    values,
    handleChange,
    handleBlur,
    isSubmitting,
    setFieldValue,
  } = props;
  return (
    <Box
      className="h-auto"
      rowGap={"24px"}
      display="flex"
      flexDirection="column"
    >
      <Flex justifyContent="space-between" alignContent="center">
        <Text fontSize={"18px"} color={BaseColor.PRIMARY} fontWeight={600}>
          Personal Information
        </Text>
        <ButtonCTA
          onClick={() => setReadOnly((prev) => !prev)}
          variant="outline"
          customType={EButtonType.PRIMARY}
          height="40px"
          fontSize="14px"
          leftIcon={<i className="uil-edit-alt text-lg" />}
        >
          {readOnly ? "Edit" : "Disable Edit"}
        </ButtonCTA>
      </Flex>

      <Grid
        templateColumns={{ base: "repeat(1, 1fr)", lg: "repeat(2, 1fr)" }}
        gap={6}
      >
        <div>
          <SelectInput
            options={[
              {
                name: "Mr",
              },
              {
                name: "Mrs",
              },
              {
                name: "Miss",
              },
              {
                name: "Ms",
              },
              {
                name: "Dr",
              },
              {
                name: "Prof",
              },
            ]}
            name="title"
            onChange={handleChange}
            onBlur={handleBlur}
            value={capitalizeFirstLetter(values.title.toLowerCase())}
            label="Title"
            isDisabled={readOnly}
          />
          <InputError touched={touched.title} error={errors.title} />
        </div>
        <div>
          <InputField
            name="first_name"
            onChange={handleChange}
            onBlur={handleBlur}
            value={values.first_name}
            label="First Name"
            isDisabled
          />
          <InputError touched={touched.first_name} error={errors.first_name} />
        </div>
      </Grid>
      <Grid
        templateColumns={{ base: "repeat(1, 1fr)", lg: "repeat(2, 1fr)" }}
        gap={6}
      >
        <div>
          <InputField
            name="last_name"
            onChange={handleChange}
            onBlur={handleBlur}
            value={values.last_name}
            label="Last Name"
            isDisabled
          />
          <InputError touched={touched.last_name} error={errors.last_name} />
        </div>
        <div>
          <InputField
            name="other_name"
            onChange={handleChange}
            onBlur={handleBlur}
            value={values.other_name}
            label="Other Names"
            isDisabled={readOnly}
          />
          <InputError touched={touched.other_name} error={errors.other_name} />
        </div>
      </Grid>
      <Grid
        templateColumns={{ base: "repeat(1, 1fr)", lg: "repeat(2, 1fr)" }}
        gap={6}
      >
        <div>
          <DatePickerInput
            name="dob"
            onChange={handleChange}
            onBlur={handleBlur}
            value={values.dob}
            label="Date of Birth"
            type="date"
            isDisabled
          />
          <InputError touched={touched.dob} error={errors.dob} />
        </div>
        <div>
          <Text
            fontSize={14}
            fontWeight={600}
            color={"#0A3150"}
            paddingBottom={"8px"}
          >
            Gender
          </Text>
          <div className="flex items-center gap-10">
            <RadioBox selected={values.gender === "MALE"} isDisabled={readOnly}>
              <input
                type="radio"
                name="gender"
                value="MALE"
                checked={values.gender === "MALE"}
                onChange={handleChange}
                id="male"
                disabled={readOnly}
              />
              <label htmlFor="male">
                <Text color={"#0A3150"} fontSize={"14px"}>
                  Male
                </Text>
              </label>
            </RadioBox>
            <RadioBox
              selected={values.gender === "FEMALE"}
              isDisabled={readOnly}
            >
              <input
                type="radio"
                name="gender"
                value="FEMALE"
                checked={values.gender === "FEMALE"}
                onChange={handleChange}
                id="female"
                disabled={readOnly}
              />
              <label htmlFor="female">
                <Text color={"#0A3150"} fontSize={"14px"}>
                  Female
                </Text>
              </label>
            </RadioBox>
          </div>
          <InputError touched={touched.gender} error={errors.gender} />
        </div>
      </Grid>
      <Grid
        templateColumns={{ base: "repeat(1, 1fr)", lg: "repeat(2, 1fr)" }}
        gap={6}
      >
        <div>
          <SelectInput
            options={[
              {
                name: "MARRIED",
              },
              {
                name: "SINGLE",
              },
            ]}
            name="marital_status"
            onChange={handleChange}
            onBlur={handleBlur}
            value={values.marital_status}
            label="Marital Status"
            isDisabled={readOnly}
          />
          <InputError
            touched={touched.marital_status}
            error={errors.marital_status}
          />
        </div>
      </Grid>
      <Text
        fontSize={"18px"}
        color={BaseColor.PRIMARY}
        fontWeight={600}
        paddingTop={"14px"}
      >
        Contact Information
      </Text>
      <Grid
        templateColumns={{ base: "repeat(1, 1fr)", lg: "repeat(2, 1fr)" }}
        gap={6}
      >
        <div>
          <PhoneInputFlag
            label={"Phone Number"}
            onChange={(_phone: string) => {
              setFieldValue("phone_number", _phone, true);
            }}
            value={values.phone_number}
            id="phone_number"
            disabled={readOnly}
          />
          <InputError
            touched={touched.phone_number}
            error={errors.phone_number}
          />
        </div>
        <div>
          <InputField
            name="email"
            onChange={handleChange}
            onBlur={handleBlur}
            value={values.email}
            label="Email Address"
            isDisabled
          />
          <InputError touched={touched.email} error={errors.email} />
        </div>
      </Grid>
      <Grid
        templateColumns={{ base: "repeat(1, 1fr)", lg: "repeat(2, 1fr)" }}
        gap={6}
      >
        <div>
          <SelectInput
            options={getCountries()}
            name="country"
            onChange={handleChange}
            onBlur={handleBlur}
            value={values.country}
            label="Country"
            isDisabled={readOnly}
          />
          <InputError touched={touched.country} error={errors.country} />
        </div>
        <div>
          <SelectInput
            options={getStates(getCountry(values.country)?.code)?.map(
              (state: string) => ({ name: state }),
            )}
            name="state"
            onChange={handleChange}
            onBlur={handleBlur}
            value={values.state}
            label="State"
            isDisabled={readOnly}
          />
          <InputError touched={touched.state} error={errors.state} />
        </div>
      </Grid>
      <Grid
        templateColumns={{ base: "repeat(1, 1fr)", lg: "repeat(2, 1fr)" }}
        gap={6}
      >
        <div>
          <InputField
            name="lga"
            onChange={handleChange}
            onBlur={handleBlur}
            value={values.lga}
            label="LGA"
            isDisabled={readOnly}
          />
          <InputError touched={touched.lga} error={errors.lga} />
        </div>
        <div>
          <InputField
            name="city"
            onChange={handleChange}
            onBlur={handleBlur}
            value={values.city}
            label="City"
            isDisabled={readOnly}
          />
          <InputError touched={touched.city} error={errors.city} />
        </div>
      </Grid>
      <Box>
        <InputField
          name="home_address"
          onChange={handleChange}
          onBlur={handleBlur}
          value={values.home_address}
          label="Residential Address"
          isDisabled={readOnly}
        />
        <InputError
          touched={touched.home_address}
          error={errors.home_address}
        />
      </Box>
      <Grid
        templateColumns={{ base: "repeat(1, 1fr)", lg: "repeat(2, 1fr)" }}
        gap={6}
      >
        <div>
          <InputField
            name="next_of_kin"
            onChange={handleChange}
            onBlur={handleBlur}
            value={values.next_of_kin}
            label="Next of Kin"
            isDisabled={readOnly}
          />
          <InputError
            touched={touched.next_of_kin}
            error={errors.next_of_kin}
          />
        </div>
        <div>
          <PhoneInputFlag
            label={"Next of Kin’s Phone Number"}
            onChange={(_phone: string) => {
              setFieldValue("next_of_kin_phone_number", _phone, true);
            }}
            value={values.next_of_kin_phone_number}
            id="next_of_kin_phone_number"
            disabled={readOnly}
          />
          <InputError
            touched={touched.next_of_kin_phone_number}
            error={errors.next_of_kin_phone_number}
          />
        </div>
      </Grid>
      <Grid
        templateColumns={{ base: "repeat(1, 1fr)", lg: "repeat(2, 1fr)" }}
        gap={6}
      >
        <div>
          <SelectInput
            options={getCountries()}
            name="nationality"
            onChange={handleChange}
            onBlur={handleBlur}
            value={values.nationality}
            label="Nationality"
            isDisabled={readOnly}
          />
          <InputError
            touched={touched.nationality}
            error={errors.nationality}
          />
        </div>
        <div>
          <InputField
            name="national_id"
            onChange={handleChange}
            onBlur={handleBlur}
            value={values.national_id}
            label="National Id"
            isDisabled={readOnly}
          />
          <InputError
            touched={touched.national_id}
            error={errors.national_id}
          />
        </div>
      </Grid>
      <Text
        fontSize={"18px"}
        color={BaseColor.PRIMARY}
        fontWeight={600}
        paddingTop={"14px"}
      >
        Employment Information
      </Text>
      <Grid
        templateColumns={{ base: "repeat(1, 1fr)", lg: "repeat(2, 1fr)" }}
        gap={6}
      >
        <div>
          <SelectInput
            options={[
              {
                name: "Employed",
              },
              {
                name: "Unemployed",
              },
            ]}
            name="employment_status"
            onChange={handleChange}
            onBlur={handleBlur}
            value={values.employment_status}
            label="Employment Status"
            isDisabled={readOnly}
          />
          <InputError
            touched={touched.employment_status}
            error={errors.employment_status}
          />
        </div>
        <div>
          <SelectInput
            options={industryOptions}
            name="industry"
            onChange={handleChange}
            onBlur={handleBlur}
            value={values.industry}
            label="Industry"
            isDisabled={readOnly}
          />
          <InputError touched={touched.state} error={errors.state} />
        </div>
        <div>
          <InputField
            name="company_name"
            onChange={handleChange}
            onBlur={handleBlur}
            value={values.company_name}
            label="Current Employer/ Company Name"
            isDisabled={readOnly}
          />
          <InputError
            touched={touched.company_name}
            error={errors.company_name}
          />
        </div>
      </Grid>
      <div className="flex justify-end">
        <ButtonCTA
          size="lg"
          background="#BB9E7F"
          color="white"
          isLoading={isSubmitting}
          isDisabled={readOnly}
          onClick={() => {
            handleSubmit();
          }}
        >
          Save
        </ButtonCTA>
      </div>
    </Box>
  );
};
