import { useMutation } from "@tanstack/react-query";
import { useToast } from "@chakra-ui/react";
import {
  ChangePin,
  IPinChange,
} from "src/api/repository/Tuition-fund/changePin";
import { extractAxiosError } from "@/lib/utils/helpers";
import axios from "axios";
export const useChangePin = () => {
  const toast = useToast();
  return useMutation({
    mutationFn: async (values: IPinChange) => {
      return await ChangePin(values);
    },
    onError: (error: any) => {
      if (axios.isAxiosError(error)) {
        toast({
          description: extractAxiosError(error),
          status: "error",
        });
      }
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Pin changed successfully",
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    },
  });
};
