"use client";

import { TParamPageCommon } from "@/constants/types";
import ApplicationContainer from "./ApplicationContainer";

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const ApplicationWrapper = ({ searchParams }: TParamPageCommon) => {
  const handleSubmitApplicationForm = async () => {
    // TODO add logic handle submit application form
  };
  return (
    <main>
      <ApplicationContainer handleSubmit={handleSubmitApplicationForm} />
    </main>
  );
};

export default ApplicationWrapper;
