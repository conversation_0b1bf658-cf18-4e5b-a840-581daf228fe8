import React from "react";
import { BaseColor } from "@/constants/colors";

import { Box, Text, Grid } from "@chakra-ui/react";
import InputField from "@/components/commons/InputField/InputField";

const EducationDetails = ({
  matric_number,
  student_id,
}: {
  matric_number?: string;
  student_id?: string;
}) => {
  return (
    <Box>
      <Text
        fontSize={"18px"}
        color={BaseColor.PRIMARY}
        fontWeight={600}
        paddingBottom={"24px"}
      >
        Education
      </Text>
      <Grid
        templateColumns={{ base: "repeat(1, 1fr)", lg: "repeat(2, 1fr)" }}
        gap={6}
      >
        <div>
          <InputField
            name="matric_number"
            value={matric_number}
            label="Matric Number"
            isDisabled
          />
        </div>
        <div>
          <InputField
            name="student_id"
            value={student_id}
            label="Student ID"
            isDisabled
          />
        </div>
      </Grid>
    </Box>
  );
};

export default EducationDetails;
