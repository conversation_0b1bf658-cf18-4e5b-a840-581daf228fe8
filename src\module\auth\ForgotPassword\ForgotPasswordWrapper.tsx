"use client";

import { Flex } from "@chakra-ui/react";

import { TParamPageCommon } from "@/constants/types";
import ForgotPasswordContainer from "./ForgotPasswordContainer";
import RightImageBlock from "../components/RightImageBlock";
import { EVIEW_PORT } from "@/constants/enums";
import { useForgotPassword } from "./useForgotPassword";
import ResetPasswordResend from "./ForgotPasswordResend";

const ForgotPasswordWrapper = ({ searchParams }: TParamPageCommon) => {
  const { handleInitiateReset, handleResend, verifyEmail } =
    useForgotPassword();

  return (
    <Flex alignItems="center" gap={{ xl: "172px", md: "40px" }}>
      {verifyEmail ? (
        <ResetPasswordResend email={verifyEmail} handleResend={handleResend} />
      ) : (
        <ForgotPasswordContainer handleInitiateReset={handleInitiateReset} />
      )}
      {searchParams?.viewport !== EVIEW_PORT.MOBILE && <RightImageBlock />}
    </Flex>
  );
};

export default ForgotPasswordWrapper;
