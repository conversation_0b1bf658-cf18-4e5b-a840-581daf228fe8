import { cn } from "@/lib/utils";
import { Stack, Text } from "@chakra-ui/react";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";

export const PhoneInputFlag = ({
  label,
  onChange,
  error = undefined,
  disabled = false,
  value,
  id,
}: {
  label: string;
  onChange: (phoneNumber: string) => void;
  error?: string | undefined;
  disabled?: boolean;
  value: string;
  id?: string;
}) => {
  return (
    <Stack width="100%" display="block" id={id}>
      <Text
        fontSize={14}
        fontWeight={600}
        color={"#0A3150"}
        paddingBottom={"8px"}
      >
        {label}
      </Text>

      <PhoneInput
        country="ng"
        placeholder=" "
        value={value || ""}
        onChange={onChange}
        inputClass={cn(
          "!h-[40px] !w-full !border-[#cbd5e1] !pl-[56px] focus:shadow-none",
        )}
        buttonClass={cn("!z-30 !border-[#cbd5e1] !bg-white !px-[4px]")}
        dropdownClass="!shadow-md border-1 !border-[#f1f1f3] !rounded !ml-[-5px] text-xs"
        containerClass={cn("!font-primary", disabled && "opacity-40")}
        countryCodeEditable={false}
        disabled={disabled}
      />

      {error ? (
        <Text color="red" fontSize="14px">
          {error}
        </Text>
      ) : null}
    </Stack>
  );
};

export default PhoneInputFlag;
