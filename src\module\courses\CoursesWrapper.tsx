/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import { useState, useEffect, useMemo } from "react";
import {
  Ta<PERSON>,
  <PERSON>b<PERSON>ist,
  TabPanels,
  Tab,
  Box,
  Tag,
  Text,
  useMediaQuery,
  Flex,
  Select,
  Skeleton,
  Stack,
} from "@chakra-ui/react";

import { BaseColor } from "@/constants/colors";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import { useStudentCourse } from "src/hooks/useStudent/useStudentCourse";
import Semesters from "./components/Semesters";
import { formatEnrolmentStatus, getColorScheme } from "./utils/CourseOptions";
import { useNewSession } from "../payments/useNewSession";
import {
  ProgrammeLevel,
  StudentEnrollment,
} from "src/api/repository/Courses/courses.d";
import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import { useRouter } from "next/navigation";
import { sortSemesters } from "src/utils/semesters";
import { sortProgrammeLevelEnrollments } from "./utils/sortProgrammeEnrollments";

const SemesterDetails = ({
  activeLevel,
  activeCourse,
  canStartNewSessionEnrollment,
  otherDetails,
}: {
  activeLevel: ProgrammeLevel | undefined;
  activeCourse: StudentEnrollment | undefined;
  canStartNewSessionEnrollment: boolean;
  otherDetails: {
    student_id: string;
    status: string;
    programme_id: string;
    enrollment_end_date: string;
    intake_id: string;
  };
}) => {
  const router = useRouter();

  const handleSecondSemesterEnrolment = () => {
    router.push(`/enrollment`);
  };

  return (
    activeLevel &&
    activeCourse && (
      <Box>
        <div className="flex items-center gap-2 py-4">
          <Text fontSize={"20px"} fontWeight={700} color={BaseColor.PRIMARY}>
            {activeCourse.programme_name}
          </Text>
          <Tag
            variant="subtle"
            textTransform="capitalize"
            fontSize={12}
            fontWeight={600}
            rounded={16}
            py={0.5}
            px={3}
            colorScheme={getColorScheme(
              formatEnrolmentStatus(activeCourse.status),
            )}
          >
            {formatEnrolmentStatus(activeCourse.status)}
          </Tag>
        </div>
        <div>
          {activeLevel.status.toLowerCase() != "enrolled" &&
            canStartNewSessionEnrollment && (
              <Box
                display="flex"
                flexDirection="column"
                justifyContent="center"
                alignItems="center"
                my={8}
              >
                <img src="/images/available-courses.svg" alt="semester-ended" />
                <Text
                  mt={3}
                  fontWeight={700}
                  fontSize={"24px"}
                  color={BaseColor.PRIMARY}
                >
                  Courses Available
                </Text>
                <Text
                  textAlign="center"
                  fontWeight={500}
                  color={BaseColor.PRIMARY_300}
                >
                  Your first semester courses are now open for enrolment
                </Text>
                <ButtonCTA onClick={handleSecondSemesterEnrolment} mt={4}>
                  Enrol now
                </ButtonCTA>
              </Box>
            )}
          <Semesters
            semesters={activeLevel?.semesters?.sort((a, b) =>
              sortSemesters(a.semester_type, b.semester_type),
            )}
            activeLevel={activeLevel}
            otherDetails={otherDetails}
          />
        </div>
      </Box>
    )
  );
};

const CoursesWrapper = () => {
  const [isLargerThan769px] = useMediaQuery("(min-width: 769px)");
  const [activeTab, setActiveTab] = useState<string>("");

  const user = useAuthStore((state) => state.user);
  const studentId: string = user?.id ?? "";

  const { data: studentProgramme, isLoading: isLoadingCourse } =
    useStudentCourse({
      studentId,
    });
  const activeProgramme = useMemo(
    () => studentProgramme?.[0],
    [studentProgramme],
  );

  const sortedLevels = useMemo(
    () => sortProgrammeLevelEnrollments(activeProgramme?.levels || []),
    [activeProgramme?.levels.length],
  );

  const activeLevel = useMemo(
    () => sortedLevels.find((level) => level.level == activeTab),
    [activeTab, sortedLevels],
  );

  const handleTabSelect = (tab: string) => {
    setActiveTab(tab);
  };

  useEffect(() => {
    if (activeProgramme && !activeTab) {
      const val = sortedLevels.findLast(
        (level) => !!level.programme_intake_id,
      )?.level;
      setActiveTab(val || "");
    }
  }, [activeProgramme, sortedLevels]);

  const otherDetails = {
    student_id: activeProgramme?.student_id ?? "",
    status: activeProgramme?.status ?? "",
    programme_id: activeProgramme?.programme_id ?? "",
    enrollment_end_date: activeProgramme?.enrollment_end_date ?? "",
    intake_id:
      sortedLevels.find((level) => level.level === activeTab)
        ?.programme_intake_id ?? "",
  };

  const { newSessionIsActive, activeEnrollment, paymentCompletion } =
    useNewSession();

  const canStartNewSessionEnrollment = (level?: ProgrammeLevel) =>
    !!(
      paymentCompletion?.type == "payment_completed" &&
      newSessionIsActive &&
      activeEnrollment?.next_programme_intake_level == level?.level
    );

  return (
    <Box mt="40px">
      <Text mb="40px" color="#0A3150" fontSize="24" fontWeight="bold">
        Welcome, {user?.biography?.first_name}
      </Text>
      <>
        {isLoadingCourse ? (
          <Stack p="24px" background="white">
            <Skeleton height="20px" />
            <Skeleton height="20px" />
            <Skeleton height="20px" />
          </Stack>
        ) : (
          <>
            {isLargerThan769px ? (
              <Tabs defaultValue={activeTab} variant="enclosed">
                <TabList mb="0px" gap="10px" border="unset">
                  {sortedLevels.map((level) => (
                    <Tab
                      mb="0px"
                      border="unset"
                      key={level?.level}
                      fontWeight="600"
                      fontSize="14px"
                      borderRadius="16px 16px 0 0"
                      color={
                        activeTab?.includes(level?.level)
                          ? "#0A3150"
                          : "#5B758A"
                      }
                      background={
                        activeTab?.includes(level?.level) ? "white" : "#E0E3E8"
                      }
                      onClick={() => handleTabSelect(level.level)}
                      isDisabled={
                        !(
                          level.status.toLowerCase() == "enrolled" ||
                          level.status.toLowerCase() == "completed" ||
                          canStartNewSessionEnrollment(level)
                        )
                      }
                      _disabled={{
                        cursor:
                          level.status.toLowerCase() !== "enrolled"
                            ? "not-allowed"
                            : "pointer",
                        opacity: 0.9,
                      }}
                    >
                      {level?.level.replace(/_/g, " ")}
                    </Tab>
                  ))}
                </TabList>
                <TabPanels
                  p="0 24px 24px 0px"
                  background="white"
                  borderRadius="0 16px 16px 16px"
                >
                  <Box pr={3} pl={8} py={4}>
                    <SemesterDetails
                      canStartNewSessionEnrollment={canStartNewSessionEnrollment(
                        activeLevel,
                      )}
                      activeLevel={activeLevel}
                      activeCourse={activeProgramme}
                      otherDetails={otherDetails}
                    />
                  </Box>
                </TabPanels>
              </Tabs>
            ) : (
              <Flex flexDir="column" alignItems="flex-start">
                <Select
                  value={activeTab}
                  width="150px"
                  background="white"
                  onChange={(e) => setActiveTab(e.target.value)}
                  mb="20px"
                  placeholder="Select option"
                >
                  {sortedLevels.map((level) => (
                    <option key={level?.level} value={level?.level}>
                      {level?.level.replace(/_/g, " ")}
                    </option>
                  ))}
                </Select>

                <Box p="16px" w="100%" background="white" borderRadius="16px">
                  <SemesterDetails
                    canStartNewSessionEnrollment={canStartNewSessionEnrollment(
                      activeLevel,
                    )}
                    activeLevel={activeLevel}
                    activeCourse={activeProgramme}
                    otherDetails={otherDetails}
                  />
                </Box>
              </Flex>
            )}
          </>
        )}
      </>
    </Box>
  );
};

export default CoursesWrapper;
