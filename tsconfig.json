{
  "compilerOptions": {
    "incremental": true, // https://nextjs.org/docs/basic-features/typescript#incremental-type-checking
    "baseUrl": ".",
    "paths": {
      "@/components/*": ["./src/components/*"],
      "@/pages/*": ["./src/pages/*"],
      "@/lib/*": ["./src/lib/*"],
      "@/hooks/*": ["./src/hooks/*"],
      "@/styles/*": ["./src/styles/*"],
      "@/constants/*": ["./src/constants/*"],
      "@/mocks/*": ["./__mocks__/*"],
      "@/tests/*": ["./.jest/*"]
    },
    "noUnusedLocals": false,
    "target": "es5",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "plugins": [
      {
        "name": "next"
      }
    ]
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.d.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules", "node_modules/@iconscout/react-unicons"]
}
