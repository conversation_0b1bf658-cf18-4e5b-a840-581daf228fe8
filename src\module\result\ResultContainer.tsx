"use client";
import React, { useMemo, useState } from "react";
import { Flex, Text, Select } from "@chakra-ui/react";
import CardResult from "./components/CardResult";
import TableResult from "./components/TableResult";
import { StudentResultDetails } from "./StudentDetails";
import Link from "next/link";
import { sortSemesters } from "src/utils/semesters";
import { sortProgrammeLevelEnrollments } from "../courses/utils/sortProgrammeEnrollments";

const ResultContainer = ({ data }: { data: StudentResultDetails }) => {
  const sortedLevels = useMemo(
    () => sortProgrammeLevelEnrollments(data?.programme_level_enrollment || []),
    [data?.programme_level_enrollment],
  );
  const [activeLevel, setActiveLevel] = useState(sortedLevels[0]);

  const uniqueLevels = useMemo(() => {
    return (
      sortedLevels
        .filter(
          (level, index, self) =>
            self.findIndex((item) => item.level === level.level) === index,
        )
        .sort((a, b) => a.level.localeCompare(b.level)) || []
    );
  }, [sortedLevels]);

  return (
    <div>
      <Text
        color="#0A3150"
        fontSize="24px"
        fontWeight="bold"
        mb="28px"
        mt="40px"
      >
        Result
      </Text>
      <CardResult summary={data?.summary} />
      <Flex
        gap="16px"
        alignItems="center"
        flexWrap="wrap"
        justifyContent="space-between"
        mt="48px"
      >
        <Flex
          width={{ base: "100%", md: "290px" }}
          justifyContent={{ base: "space-between" }}
          alignItems="center"
          gap="13px"
          minWidth="350px"
        >
          <Text minWidth="150px" fontSize="16px" fontWeight="bold">
            Academic session
          </Text>
          <Select
            maxWidth="200px"
            background="white"
            defaultValue={sortedLevels[0]?.level ?? "Select Option"}
            onChange={(e) => {
              const selectedLevel = uniqueLevels.filter(
                (item) => item.level === e.target.value,
              )[0];
              setActiveLevel(selectedLevel);
            }}
          >
            {uniqueLevels?.map((level, index) => (
              <option key={index} value={level.level}>
                <span className="text-sm font-semibold capitalize">
                  {level?.level?.replace(/_/g, " ")?.toLowerCase()}
                </span>
              </option>
            ))}
          </Select>
        </Flex>
        <Link
          href="https://miva.university/request-transcript/"
          target="_blank"
          className="flex items-center justify-center bg-[#E83831] p-2 text-white md:w-[180px]"
        >
          Request Transcript
        </Link>
      </Flex>
      {activeLevel && (
        <div className="mt-12">
          {activeLevel?.semesters
            .sort((a, b) => sortSemesters(a.semester_type, b.semester_type)) // Sort by semester_type
            .map((semester) => (
              <TableResult
                key={semester.semester_id} // Add a unique key
                title={semester.semester_type.replace(/_/g, " ").toLowerCase()}
                data={semester.courses}
                totalCredits={semester.total_credit_unit}
                gpa={semester.gpa}
                cgpa={semester.total_grade_point}
              />
            ))}
        </div>
      )}
    </div>
  );
};

export default ResultContainer;
