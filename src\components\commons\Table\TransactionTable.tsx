import Tag from "@/components/commons/Tag/Tag";
import { Button } from "@/components/ui/button";
import { formatCurrency, formatDate } from "@/lib/utils/helpers";
import {
  Table,
  TableContainer,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tr,
  useDisclosure,
} from "@chakra-ui/react";
import React, { useState } from "react";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import { CommonModal } from "../CommonModal/CommonModal";
import ReceiptModal, { ReceiptData } from "./ReceiptModal";
import { TableProps, Transaction } from "./Transaction";
import { MIVA_SIS_PAYMENT_URL } from "@/constants/api";

const TransactionTable: React.FC<TableProps> = ({
  transactions,
  baseColor,
  setTagBgColor,
  tableBaseColor,
  tableRowBaseColor,
  transactionTDStyles,
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const user = useAuthStore((state) => state.student);
  const accessToken = useAuthStore.getState().access_token;
  const [receiptData, setReceiptData] = useState<ReceiptData>();

  const owedTransactions = transactions.filter(
    (transaction) => transaction.status?.toLowerCase() !== "success",
  );

  const paidTransactions = transactions.filter((transaction) =>
    transaction.amount ? Number(transaction.amount) > 0 : false,
  );

  // If there are owed transactions, show all transactions; otherwise, only show paid transactions
  const displayedTransactions =
    owedTransactions.length > 0 ? transactions : paidTransactions;

  const handleViewReceipt = (transaction: Transaction) => {
    setReceiptData({
      receipt_number: transaction?.receipt_num ?? "",
      first_name: user?.personal_details?.biography?.first_name,
      last_name: user?.personal_details?.biography?.last_name,
      created_at: transaction.created_at,
      amount: Number(transaction.amount),
      currency: transaction.currency.toUpperCase(),
      payment_method: transaction.payment_method,
      description: transaction.description,
      status: transaction.status ?? "",
      student_id_2: user?.student_profile?.student_id,
      programmename: transaction.programme ?? "",
      receipt_id: transaction.receipt_id,
    });
    onOpen();
  };

  const handleNavigate = (student_id: string, installment_plan_id?: string) => {
    const url = `${MIVA_SIS_PAYMENT_URL}/payment-with-installment?studentSisId=${user?.student_profile.student_id}&userToken=${accessToken}&installmentId=${installment_plan_id}`;
    window.open(url, "_blank"); // Open in a new tab
  };

  const cleanDate = (dateString: string) => {
    if (!dateString) return "";
    // Remove the duplicate timezone offset
    return dateString.replace(/(\+\d{4})\s+\+\d{4}$/, "$1");
  };

  return (
    <TableContainer bg={"#ffffff"} borderRadius={"16px"}>
      <Table
        variant="simple"
        fontSize={14}
        fontWeight={500}
        color={tableBaseColor}
        borderBottom={0}
      >
        <Thead borderRadius={10}>
          <Tr color="white" bg={tableRowBaseColor}>
            <Th
              borderLeftRadius="8px"
              fontSize={"14px"}
              color={"#ffffff"}
              textTransform={"capitalize"}
            >
              Date
            </Th>
            <Th
              fontSize={"14px"}
              color={"#ffffff"}
              textTransform={"capitalize"}
            >
              Description
            </Th>
            <Th
              fontSize={"14px"}
              color={"#ffffff"}
              textTransform={"capitalize"}
            >
              Amount
            </Th>
            <Th
              fontSize={"14px"}
              color={"#ffffff"}
              textTransform={"capitalize"}
            >
              Status
            </Th>
            <Th
              fontSize={"14px"}
              color={"#ffffff"}
              textTransform={"capitalize"}
              borderRightRadius="8px"
            ></Th>
          </Tr>
        </Thead>
        <Tbody>
          {transactions.map((transaction, index) => {
            const isPaid = !transaction.outstanding;

            return (
              <Tr key={index}>
                {/* Date */}
                <Td sx={transactionTDStyles}>
                  {formatDate(
                    isPaid
                      ? transaction.created_at
                      : transaction.due_date?.split(" +")[0],
                  )}
                </Td>

                {/* Description */}
                <Td sx={transactionTDStyles}>
                  {isPaid
                    ? transaction.description || "-"
                    : transaction.installment_name || "-"}
                </Td>

                {/* Amount */}
                <Td sx={transactionTDStyles}>
                  {formatCurrency(
                    Number(
                      isPaid ? transaction.amount : transaction.amount_in_naira,
                    ),
                    transaction.currency
                      ? transaction.currency.toUpperCase()
                      : "NGN",
                  )}
                </Td>

                {/* Status */}
                <Td sx={transactionTDStyles}>
                  <Tag
                    borderRadius="full"
                    px={6}
                    py={2}
                    baseColor={setTagBgColor(isPaid ? "paid" : "outstanding")}
                  >
                    <svg width="4" height="4" viewBox="0 0 4 4" fill="none">
                      <circle
                        opacity="0.8"
                        cx="2"
                        cy="2"
                        r="2"
                        fill="currentColor"
                      />
                    </svg>
                    <span className="ml-1 font-semibold capitalize">
                      {isPaid ? "Paid" : "Outstanding"}
                    </span>
                  </Tag>
                </Td>

                {/* Action Button */}
                <Td sx={transactionTDStyles} textAlign="center">
                  {isPaid ? (
                    <Button onClick={() => handleViewReceipt(transaction)}>
                      <Text
                        color={baseColor}
                        fontWeight={600}
                        fontSize={"12px"}
                      >
                        View Receipt
                      </Text>
                    </Button>
                  ) : (
                    <Button
                      onClick={() =>
                        handleNavigate(
                          transaction.student_id,
                          transaction?.installment_plan_id,
                        )
                      }
                      className="rounded-[8px] bg-[#E83831] p-[8px] text-[12px] font-bold text-[#ffffff]"
                    >
                      Make Payment
                    </Button>
                  )}
                </Td>
              </Tr>
            );
          })}
        </Tbody>
      </Table>
      <CommonModal isOpen={isOpen} onClose={onClose} title="Receipt" size="4xl">
        <div className="p-4">
          {receiptData && <ReceiptModal {...receiptData} />}
        </div>
      </CommonModal>
    </TableContainer>
  );
};

export default TransactionTable;
