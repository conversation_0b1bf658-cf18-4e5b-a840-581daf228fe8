import { IStringObject } from "@/constants/types";

export const CourseTab: IStringObject = {
  "100_LEVEL": "100_level",
  "200_LEVEL": "200_level",
  "300_LEVEL": "300_level",
  "400_LEVEL": "400_level",
  OUTSTANDING_COURSE: "outstanding_courses",
};

export const mapCourseToLabel: IStringObject = {
  [CourseTab["100_LEVEL"]]: "100 Level",
  [CourseTab["200_LEVEL"]]: "200 Level",
  [CourseTab["300_LEVEL"]]: "300 Level",
  [CourseTab["400_LEVEL"]]: "400 Level",
  [CourseTab["OUTSTANDING_COURSE"]]: "Outstanding",
};

export const SEMESTER_STATUS: IStringObject = {
  COURSE_AVAILABLE: "course_available",
  COURSE_UNAVAILABLE: "course_unavailable",
  NO_OUTSTANDING_ENROLLMENTS: "no_outstanding_enrollments",
};

export const COURSE_STATUS: IStringObject = {
  COMPLETED: "completed",
  CARRY_OVER: "carry_over",
  DEFERRED: "deferred",
  DISCONTINUED: "discontinued",
  ENROLLED: "enrolled",
  OUTSTANDING: "outstanding",
};

export const MAP_COURSE_STATUS_LABEL: IStringObject = {
  [COURSE_STATUS.COMPLETED]: "Completed",
  [COURSE_STATUS.CARRY_OVER]: "Carry over",
  [COURSE_STATUS.DEFERRED]: "Deferred",
  [COURSE_STATUS.DISCONTINUED]: "Discontinued",
  [COURSE_STATUS.ENROLLED]: "Enrolled",
  [COURSE_STATUS.OUTSTANDING]: "Outstanding",
};

export const LIST_SEMESTER_STATUS: string[] = [
  SEMESTER_STATUS.COURSE_AVAILABLE,
  SEMESTER_STATUS.COURSE_UNAVAILABLE,
  SEMESTER_STATUS.NO_OUTSTANDING_ENROLLMENTS,
];
