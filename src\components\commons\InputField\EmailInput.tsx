import Icon from "@/components/commons/Icons/Icon";
import { EIconName } from "@/components/commons/Icons/Icon.enums";
import { Input, InputGroup, InputLeftElement } from "@chakra-ui/react";
import { FC } from "react";

import { IEmailInputProps } from "./EmailInput.d";

const EmailInput: FC<IEmailInputProps> = ({
  onChange,
  placeholder = "Email",
}) => {
  return (
    <InputGroup>
      <InputLeftElement height="100%" pointerEvents="none">
        <Icon name={EIconName.ENVELOPE} />
      </InputLeftElement>
      <Input
        onChange={onChange}
        placeholder={placeholder}
        type="text"
        size="lg"
      />
    </InputGroup>
  );
};

export default EmailInput;
