import { BaseColor } from "@/constants/colors";
import { Text } from "@chakra-ui/react";

export const InputError = ({
  touched,
  error,
}: {
  touched?: boolean;
  error?: string;
}): React.ReactNode => {
  return (
    touched &&
    error && (
      <Text
        className="error-message"
        fontSize="14px"
        color={BaseColor.DANGER}
        paddingLeft={5}
        paddingTop={2}
      >
        {error}
      </Text>
    )
  );
};
