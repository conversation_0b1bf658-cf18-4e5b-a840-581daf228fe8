/* eslint-disable @typescript-eslint/no-explicit-any */
import { useRouter } from "next/navigation";
import axios from "axios";
import { useToast } from "@chakra-ui/react";
import { createDeferment } from "src/api/repository/deferments";
import { ICreateDeferment } from "src/module/deferments/CreateDeferment/CreateDefermentForm";
import { useFileUpload } from "src/module/deferments/CreateDeferment/hooks/useFileUpload";

export const useCreateDeferment = () => {
  const router = useRouter();
  const toast = useToast();
  const { uploadFiles, error } = useFileUpload();

  const handleCreateDeferment = async (payload: ICreateDeferment) => {
    try {
      const uploadedUrls = await uploadFiles(payload.documents);

      if (error) {
        toast({
          description: error,
          status: "error",
        });
        return;
      }

      const resp = await createDeferment({
        ...payload,
        documents: uploadedUrls,
      });
      const response = axios.isAxiosError(resp);
      if (!response) {
        toast({
          description: "Deferment request created successfully",
          status: "success",
        });
        router.push("/dashboard");
      }
      return resp;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        toast({
          description: error?.response?.data?.errors[0],
          status: "error",
        });
      }
      return error;
    }
  };

  return {
    handleCreateDeferment,
  };
};
