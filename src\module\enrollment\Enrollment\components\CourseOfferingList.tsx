"use client";

import { FC, ChangeEvent, useMemo } from "react";
import { BaseColor } from "@/constants/colors";
import { Box, Text, Flex, Checkbox, Spinner } from "@chakra-ui/react";
import { useGetCourseOffering } from "@/hooks/useEnrollment/useGetCourseOffering";
import { GROUP_TYPE_COURSE } from "@/constants/commons";

import {
  ICourseOfferingListParams,
  ICourseItem,
  IGroupCourseItem,
} from "./CourseOfferingList.d";

const CourseOfferingList: FC<ICourseOfferingListParams> = ({
  programme_id,
  programme_intake_id,
  atp_id,
  level,
  courseChecked,
  setCourseChecked,
  loading,
  semester,
}) => {
  const { data: dataCourseOffering, isLoading: isLoadingCourse } =
    useGetCourseOffering({
      programme_id,
      programme_intake_id,
      atp_id,
      level,
    });

  const isLoading = useMemo(() => {
    return isLoadingCourse || loading;
  }, [isLoadingCourse, loading]);

  const { courseCore, courseElective } = useMemo(() => {
    let courseOfCore: ICourseItem[] = [];
    let courseOfElective: ICourseItem[] = [];
    if (Array.isArray(dataCourseOffering)) {
      (dataCourseOffering || []).forEach((item: IGroupCourseItem) => {
        if (
          item.group_type === GROUP_TYPE_COURSE.CORE &&
          item.group_name === semester
        ) {
          courseOfCore = [...courseOfCore, ...item.courses];
        }
        if (
          item.group_type === GROUP_TYPE_COURSE.ELECTIVE &&
          item.group_name === semester
        ) {
          courseOfElective = [...courseOfElective, ...item.courses];
        }
      });
      return { courseCore: courseOfCore, courseElective: courseOfElective };
    }
    return { courseCore: courseOfCore, courseElective: courseOfElective };
  }, [dataCourseOffering, semester]);

  const handleCheckCourse = (
    e: ChangeEvent<HTMLInputElement>,
    courseId: string,
  ) => {
    if (e?.target?.checked) {
      return setCourseChecked([...courseChecked, courseId]);
    }
    setCourseChecked(courseChecked.filter((item) => item !== courseId));
  };

  return (
    <Box
      p={{ md: "24px", base: "16px" }}
      borderRadius="16px"
      background="white"
      mt={"12px"}
    >
      <Box background="white">
        <Text fontWeight="semibold" fontSize="20px" color={BaseColor.PRIMARY}>
          Course Selection
        </Text>
        <Flex justifyContent="space-between" mt={4}>
          <Text fontWeight="semibold" fontSize="16px" color={BaseColor.PRIMARY}>
            Compulsory Course(s)
          </Text>
          <Text fontWeight="semibold" fontSize="16px" color={BaseColor.PRIMARY}>
            Course Unit
          </Text>
        </Flex>
        <>
          {isLoading ? (
            <Flex p="16px" justifyContent="center" alignItems="center">
              <Spinner />
            </Flex>
          ) : (
            <>
              {courseCore &&
              Array.isArray(courseCore) &&
              courseCore.length > 0 ? (
                <>
                  {courseCore.map((course) => (
                    <Flex
                      key={course.course_offering_id}
                      justifyContent="space-between"
                      mt={4}
                      rounded={10}
                      bg="#F9FAFB"
                      py={3}
                      px={5}
                    >
                      <Checkbox
                        id={`course-${course.course_offering_id}`}
                        fontSize={"10px"}
                        size="lg"
                        checked={courseChecked.some(
                          (item) => item === course.course_offering_id,
                        )}
                        rounded={10}
                        className="primary-checkbox"
                        onChange={(e) =>
                          handleCheckCourse(e, course.course_offering_id)
                        }
                      >
                        <Text fontSize="16px" color={BaseColor.PRIMARY}>
                          {course?.course_name}
                        </Text>
                      </Checkbox>
                      <Text fontSize="16px" color={BaseColor.PRIMARY}>
                        {course?.credit_unit}
                      </Text>
                    </Flex>
                  ))}
                </>
              ) : (
                <Box>
                  <Text fontSize="12px" color="#8EA0AF" alignItems="center">
                    There is no data
                  </Text>
                </Box>
              )}
            </>
          )}
        </>

        <Box mt={6}>
          <Text fontWeight="semibold" fontSize="16px" color={BaseColor.PRIMARY}>
            Elective Course(s)
          </Text>
          <Text
            fontWeight="semibold"
            fontSize="12px"
            color={BaseColor.PRIMARY_300}
            mt={2}
          >
            Select one (1) elective course
          </Text>
          <>
            {isLoading ? (
              <Flex p="16px" justifyContent="center" alignItems="center">
                <Spinner />
              </Flex>
            ) : (
              <>
                {Array.isArray(courseElective) && courseElective.length ? (
                  <>
                    {courseElective.map((course) => (
                      <Flex
                        key={course.course_offering_id}
                        justifyContent="space-between"
                        mt={4}
                        rounded={10}
                        bg="#F9FAFB"
                        py={3}
                        px={5}
                      >
                        <Checkbox
                          fontSize={"10px"}
                          size="lg"
                          rounded={10}
                          checked={courseChecked.some(
                            (item) => item === course.course_offering_id,
                          )}
                          className="primary-checkbox"
                          onChange={(e) =>
                            handleCheckCourse(e, course.course_offering_id)
                          }
                        >
                          <Text fontSize="16px" color={BaseColor.PRIMARY}>
                            {course.course_name}
                          </Text>
                        </Checkbox>
                        <Text fontSize="16px" color={BaseColor.PRIMARY}>
                          {course.credit_unit}
                        </Text>
                      </Flex>
                    ))}
                  </>
                ) : (
                  <Box>
                    <Text fontSize="12px" color="#8EA0AF" alignItems="center">
                      There is no data
                    </Text>
                  </Box>
                )}
              </>
            )}
          </>
        </Box>
      </Box>
    </Box>
  );
};

export default CourseOfferingList;
