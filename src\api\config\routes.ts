/**
 * Module routes to point to correct URL of specific service
 */
enum MODULE_ROUTE {
  APPLICATION = "application",
  COURSE = "course",
  COHORT = "cohort",
  DEFERMENT = "deferment",
  FACULTY = "faculty",
  AUTH = "auth",
  STUDENT = "student",
  MISC = "misc",
  NOTIFICATION = "notifications",
  ENROLLMENT = "enrollment",
  RESULT = "result",
  CAS = "cas-service",
  PROGRAMME_INTAKE = "programmeIntake",
  WALLET = "wallet",
}

const Routes = {
  [MODULE_ROUTE.APPLICATION]: {
    CREATE: `${MODULE_ROUTE.APPLICATION}/create`,
    UPDATE: (id: string) => `${MODULE_ROUTE.APPLICATION}/${id}`,
    GET: (id: string) => `${MODULE_ROUTE.APPLICATION}/${id}`,
    APPLICATION_LIST: `/${MODULE_ROUTE.MISC}/applications`,
  },
  [MODULE_ROUTE.COURSE]: {
    DETAIL: (id: string) => `${MODULE_ROUTE.COURSE}/${id}`,
  },
  [MODULE_ROUTE.COHORT]: {
    ALL: `${MODULE_ROUTE.COHORT}/all`,
    BY_ID: (id: string) => `${MODULE_ROUTE.COHORT}/${id}`,
  },
  [MODULE_ROUTE.DEFERMENT]: {
    CREATE: `${MODULE_ROUTE.DEFERMENT}/create`,
  },
  [MODULE_ROUTE.FACULTY]: {
    LIST: `list/${MODULE_ROUTE.FACULTY}`,
    BY_ID: `${MODULE_ROUTE.FACULTY}`,
  },
  [MODULE_ROUTE.AUTH]: {
    LOGIN: `${MODULE_ROUTE.AUTH}/login`,
    LOGIN_VERIFY: `${MODULE_ROUTE.AUTH}/login/verify`,
    REQUEST_RESET: `${MODULE_ROUTE.AUTH}/request-password-reset`,
    RESET: `${MODULE_ROUTE.AUTH}/reset-password`,
    RESEND: `${MODULE_ROUTE.AUTH}/request-verification`,
    ME: `${MODULE_ROUTE.AUTH}/me`,
    ACTIVATE: `${MODULE_ROUTE.AUTH}/verify/user`,
    RESEND_MAIL: `${MODULE_ROUTE.AUTH}/resend-activation-email`,
    REFRESH_TOKEN: `${MODULE_ROUTE.AUTH}/refresh-token`,
  },
  [MODULE_ROUTE.STUDENT]: {
    GET: `${MODULE_ROUTE.STUDENT}/get`,
    UPDATE_PROFILE: `${MODULE_ROUTE.STUDENT}/profile`,
    DASHBOARD: `${MODULE_ROUTE.STUDENT}/dashboard`,
    COURSE_LIST: `/enrollment/${MODULE_ROUTE.STUDENT}/enrolled-courses`,
    UPDATE_PRE_ENROLLMENT_STATUS: `${MODULE_ROUTE.STUDENT}/payments/applications/enrollment-update`,
    STAFF: `${MODULE_ROUTE.STUDENT}/staff/list`,
    DOCUMENT: `${MODULE_ROUTE.STUDENT}/document`,
    TRANSACTIONS: `${MODULE_ROUTE.STUDENT}/users/record`,
    NEXT_INSTALLMENT: `${MODULE_ROUTE.STUDENT}/next-installment`,
    TUITION_FUND_WITHDRAWAL_REQUEST: `${MODULE_ROUTE.STUDENT}/tuition-fund/withdrawal-request`,
    RECEIPTS: {
      DOWNLOAD: `${MODULE_ROUTE.STUDENT}/payments/students/receipt-download`,
    },
  },
  [MODULE_ROUTE.MISC]: {
    GET_APPLICATION: `${MODULE_ROUTE.MISC}/application`,
    GET_COURSE_OFFERING: `${MODULE_ROUTE.MISC}/courseOffering`,
    GET_SEMESTER: `${MODULE_ROUTE.MISC}/programmeIntake/semester`,
    GET_EXAM_LOCATION_LIST: `${MODULE_ROUTE.MISC}/exam-location/list`,
    UPLOAD: `${MODULE_ROUTE.MISC}/upload_image`,
    DOCUMENT_UPLOAD: `${MODULE_ROUTE.MISC}/document/upload`,
    INTAKE_BY_LEVEL: `${MODULE_ROUTE.MISC}/programmeIntake/level`,
  },
  [MODULE_ROUTE.NOTIFICATION]: {
    GET: `${MODULE_ROUTE.STUDENT}/${MODULE_ROUTE.NOTIFICATION}`,
    MARK_ALL: `${MODULE_ROUTE.STUDENT}/${MODULE_ROUTE.NOTIFICATION}/read`,
    MARK_AS_READ: (id: string) =>
      `${MODULE_ROUTE.STUDENT}/${MODULE_ROUTE.NOTIFICATION}/${id}/read`,
  },
  [MODULE_ROUTE.ENROLLMENT]: {
    CREATE_ENROLLMENT: `${MODULE_ROUTE.ENROLLMENT}/enrol`,
    SEMESTER_ENROLLMENT: `${MODULE_ROUTE.ENROLLMENT}/course-enrol`,
    ACTIVE_ENROLLMENT: `${MODULE_ROUTE.ENROLLMENT}/student/details`,
    PAYMENT_COMPLETION: `${MODULE_ROUTE.ENROLLMENT}/student/payment/completion`,
  },
  [MODULE_ROUTE.RESULT]: {
    STUDENT_RESULT: `${MODULE_ROUTE.RESULT}/transcript/${MODULE_ROUTE.STUDENT}`,
  },
  [MODULE_ROUTE.CAS]: {
    LIST: `${MODULE_ROUTE.CAS}/list`,
    GET: `${MODULE_ROUTE.CAS}/get`,
    CREATE_TICKET: "cas-ticket/create",
    DELETE_TICKET: "cas-ticket/delete",
    GENERATE_TICKET: "/cas/generateServiceTicket",
    GET_TICKET: "cas-ticket/ticket",
  },
  [MODULE_ROUTE.PROGRAMME_INTAKE]: {
    NEXT: `student/${MODULE_ROUTE.PROGRAMME_INTAKE}/next-cohorts`,
  },
  [MODULE_ROUTE.WALLET]: {
    SETUP: `${MODULE_ROUTE.WALLET}/setup`,
    SEND_OTP: `${MODULE_ROUTE.WALLET}/send-wallet-otp`,
    VERIFY_OTP: `${MODULE_ROUTE.WALLET}/verify-wallet-otp`,
    FORGOT_PIN_OTP: `${MODULE_ROUTE.WALLET}/forget-pin`,
    CHANGE_PIN_OTP: `${MODULE_ROUTE.WALLET}/change-pin-otp-request`,
    RESET_PIN: `${MODULE_ROUTE.WALLET}/reset-pin`,
    CHANGE_PIN: `${MODULE_ROUTE.WALLET}/change-pin`,
    DASHBOARD: `${MODULE_ROUTE.WALLET}/dashboard`,
    TRANSACTIONS: `${MODULE_ROUTE.WALLET}/transactions`,
  },
};

export { MODULE_ROUTE, Routes };
