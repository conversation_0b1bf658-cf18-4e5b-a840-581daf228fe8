import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { getApplication } from "../../api/repository/Application/application";

export const useApplicationDetail = (id: string) => {
  const query = useQuery({
    queryKey: ["getApplication", id],
    enabled: !!id,
    queryFn: () => getApplication(id),
    retry: false,
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
  });
  return query;
};
