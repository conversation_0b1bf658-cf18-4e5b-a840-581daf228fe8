import { PreEnrollmentStatus } from "src/api/repository/Enrollment/enrollment";
import { E_STATUS_APPLICATION } from "../../constants/enums";
import { ApplicationResponse } from "src/api/repository/Application/application.d";

export interface IdataApplication {
  Application_status: string;
  Application_type: string;
  Cohort: string;
  CreatedAt: string;
  ID: string;
  Programme: string;
  UpdatedAt: string;
  User_id: string;
}

export interface IApplicationContainerProps {
  loading: boolean;
  data: ApplicationResponse[];
  item?: ApplicationResponse;
  studentName: string;
  studentSisId: string;
}

export interface IApplicationState {
  status: E_STATUS_APPLICATION | string;
  studyLever: string;
  enrollmentStatus: PreEnrollmentStatus;
  courseUnit: string;
  programmeImage: string;
  tuitionPerSemester: string;
  tuitionFeePerSession: string;
  studyDuration: string;
  id: string;
  studentName: string;
  studentSisId: string;
  description: string;
  department: string;
  name: string;
  applicationType: string;
  startDate: string;
  atpEnrollmentStartDate: string;
  atpEnrollmentEndDate: string;
}
