import { extractAxiosError } from "@/lib/utils/helpers";
import { useToast } from "@chakra-ui/react";
import axios from "axios";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect } from "react";
import { authApi } from "src/api/config/api";
import { APIResponse } from "src/api/config/api.d";
import { MODULE_ROUTE, Routes } from "src/api/config/routes";
import { IResetPasswordValue } from "./ResetPassword.d";
import { useCASQueryParams } from "../CAS/useCASQueryParams";

export const useResetPassword = () => {
  const router = useRouter();
  const toast = useToast();

  const searchParams = useSearchParams();
  const email = decodeURIComponent(searchParams.get("email") || "");
  const { queryString } = useCASQueryParams();

  useEffect(() => {
    if (!email) {
      router.replace(`forgot-password${queryString}`);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [email]);

  const handleReset = async ({
    password,
    confirm_password,
    reset_token,
  }: IResetPasswordValue) => {
    try {
      await authApi.post<APIResponse<string>>(Routes[MODULE_ROUTE.AUTH].RESET, {
        email,
        password,
        confirm_password,
        reset_token,
      });
      toast({
        description: "Password reset successful",
        status: "success",
      });
      router.replace(`login${queryString}`);
    } catch (error) {
      if (axios.isAxiosError(error)) {
        toast({
          description: extractAxiosError(error),
          status: "error",
        });
      }
    }
  };

  return {
    handleReset,
  };
};
