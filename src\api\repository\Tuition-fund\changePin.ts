import { baseApi } from "../../config/api";
import { MODULE_ROUTE, Routes } from "../../config/routes";

export interface IPinChange {
  new_pin: string;
  old_pin: string;
  user_id: string;
  verify_code: string;
}

export async function ChangePin(payload: IPinChange) {
  try {
    const response = await baseApi.post(
      Routes[MODULE_ROUTE.WALLET].CHANGE_PIN,
      payload,
    );
    return response.data;
  } catch (error) {
    console.error("Error creating staff:", error);
    throw error;
  }
}
