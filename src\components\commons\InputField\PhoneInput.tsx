import { FC } from "react";
import {
  Input,
  InputGroup,
  InputLeftAddon,
  InputLeftElement,
} from "@chakra-ui/react";
import Icon from "@/components/commons/Icons/Icon";
import { EIconName } from "@/components/commons/Icons/Icon.enums";

import { IPhoneInputProps } from "./PhoneInput.d";

const PhoneInput: FC<IPhoneInputProps> = ({
  prefixPhoneNumber,
  onChange,
  placeholder = "Phone number",
}) => {
  return (
    <InputGroup>
      {!prefixPhoneNumber ? (
        <InputLeftElement height="100%" pointerEvents="none">
          <Icon name={EIconName.PHONE} />
        </InputLeftElement>
      ) : (
        <InputLeftAddon>+{prefixPhoneNumber}</InputLeftAddon>
      )}
      <Input
        onChange={onChange}
        type="tel"
        size="lg"
        placeholder={placeholder}
      />
    </InputGroup>
  );
};

export default PhoneInput;
