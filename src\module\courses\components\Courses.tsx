import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import { BaseColor } from "@/constants/colors";
import { Flex, SimpleGrid, Text } from "@chakra-ui/react";
import CourseSection from "./CourseSection";
import { useToast } from "@chakra-ui/react";
import { courseEnrollment } from "src/api/repository/Enrollment/enrollment";
import { useStudentCourse } from "@/hooks/useStudent/useStudentCourse";
import { Course, Semester } from "src/api/repository/student";

const Courses = ({
  courses,
  semester,
  goToClass,
  semesterStartDate,
  semesterDetails,
  enrollmentEndDate,
  otherDetails,
  handleSemesterEnrolment,
}: {
  courses: Course[];
  semester: string;
  semesterStartDate: string;
  goToClass: boolean;
  handleSemesterEnrolment: () => void;
  otherDetails: {
    student_id: string;
    status: string;
    programme_id: string;
    intake_id?: string;
  };
  semesterDetails: Semester;
  enrollmentEndDate: string;
}) => {
  const toast = useToast();
  const { refetch } = useStudentCourse({
    studentId: otherDetails?.student_id || "",
  });

  const handleGoToClass = () => {
    window.open("https://class.miva.university/", "_blank");
  };

  const handleCourseEnrollment = async (courseId: string) => {
    try {
      const payload = {
        course_offering_id: courseId,
        intake_id: otherDetails?.intake_id || "",
        student_id: otherDetails?.student_id || "",
        semester_atp_id: semesterDetails?.semester_id || "",
        programme_id: otherDetails?.programme_id || "",
        is_moodle: true,
      };
      await courseEnrollment(payload);
      toast({
        description: "Course enrolled successfully",
        status: "success",
      });
      refetch();
    } catch (error: any) {
      toast({
        description: error.response.data.errors[0],
        status: "error",
      });
    }
  };

  return (
    <>
      <div>
        <Flex
          justifyContent="space-between"
          py={4}
          className="mt-4 border-b border-b-[#E7EAEE]"
        >
          <Text
            fontWeight="semibold"
            fontSize="18px"
            color={BaseColor.PRIMARY}
            textTransform="capitalize"
          >
            {semester}
          </Text>
          {goToClass && courses && (
            <ButtonCTA
              onClick={handleGoToClass}
              fontSize={12}
              height="auto"
              py={3}
              px={7}
            >
              Go to Class
            </ButtonCTA>
          )}
        </Flex>
        {!courses ? (
          <div className="my-6 flex flex-col items-center justify-center bg-[#F9FAFB] py-[64px]">
            {otherDetails.status !== "ENROLLED" ||
            semesterStartDate === "" ||
            new Date(semesterStartDate) > new Date() ? (
              <>
                <img
                  src="/images/unavailable-course.svg"
                  alt="semester-not-started"
                />
                <Text
                  mt={3}
                  fontWeight={700}
                  fontSize={"24px"}
                  color={BaseColor.PRIMARY}
                >
                  Courses Unavailable
                </Text>
                <Text fontWeight={500} color={BaseColor.PRIMARY_300}>
                  {semester} courses are not yet available for enrollment
                </Text>
              </>
            ) : (
              <>
                <img src="/images/available-courses.svg" alt="semester-ended" />
                <Text
                  mt={3}
                  fontWeight={700}
                  fontSize={"24px"}
                  color={BaseColor.PRIMARY}
                >
                  Courses Available
                </Text>
                <Text fontWeight={500} color={BaseColor.PRIMARY_300}>
                  Your {semester.toLowerCase()} courses are now open for
                  enrolment
                </Text>
                <ButtonCTA onClick={handleSemesterEnrolment} mt={4}>
                  Enrol now
                </ButtonCTA>
              </>
            )}
          </div>
        ) : (
          <SimpleGrid
            pt={6}
            columns={{ base: 1, sm: 2, lg: 3, xl: 4 }}
            spacing="20px"
          >
            {courses?.map((course, index) => (
              <CourseSection
                key={index}
                course={course}
                semesterEnrollmentEndDate={enrollmentEndDate}
                onEnroll={() =>
                  handleCourseEnrollment(course.course_offering_id || "")
                }
                otherDetails={otherDetails}
              />
            ))}
          </SimpleGrid>
        )}
      </div>
    </>
  );
};

export default Courses;
