import React from "react";
import { useRouter } from "next/navigation";
import {
  Image,
  Text,
  Menu,
  Box,
  MenuButton,
  MenuList,
  MenuItem,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@chakra-ui/react";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import { useNotifications } from "src/hooks/useNotifications";
import Icon from "@/components/commons/Icons/Icon";
import { EIconName, EIconSize } from "@/components/commons/Icons/Icon.enums";
import { useSideBarStore } from "src/store/SideBarStore/sideBar";

const Header = () => {
  const router = useRouter();
  const logout = useAuthStore((state) => state.logout);
  const user = useAuthStore((state) => state.user);
  const {
    data: notificationsData,
    isLoading,
    markAllAsRead,
    markAsRead,
    isMarkingAsRead,
  } = useNotifications();
  const isExpanded = useSideBarStore((state) => state.isExpanded);
  const setIsExpanded = useSideBarStore((state) => state.setIsExpanded);

  const goToProfile = () => {
    router.push("/profile");
  };

  const handleLogout = () => {
    router.push("/login");
    logout();
  };
  const notifications = notificationsData?.data || [];
  return (
    <header className="sticky top-0 z-50 flex h-[62px] items-center justify-end gap-x-4 border-b-[1px] border-[#DDE2E7] bg-white px-2 text-[#0A3150] sm:gap-x-10 sm:px-6 md:px-12 lg:bg-[#F0F2F4] lg:px-8">
      <Box gap={3} className="flex flex-grow lg:hidden">
        <Icon
          isStaticIcon
          className="lg:hidden"
          onClick={() => {
            setIsExpanded();
          }}
          name={isExpanded ? EIconName.TIME_CIRCLE : EIconName.BARS}
          size={EIconSize.LG}
        />
        <Image
          src="/images/logo-miva-sidebar.png"
          alt="logo-miva-sidebar"
          className="hidden w-[120px] object-contain sm:block md:w-[136px]"
        />
        <Image
          className="block w-[32px] object-contain sm:hidden"
          alt="Logo"
          src="/images/mini-logo-miva-sidebar.png"
        />
      </Box>
      <Menu>
        <MenuButton
          as={Button}
          position="relative"
          _hover={{
            bg: "transparent",
          }}
          _active={{
            bg: "transparent",
          }}
        >
          <div className="relative">
            <Image alt="Menu" src="/images/icons/notif.svg" />
            {notifications.some((notification) => !notification.is_read) && (
              <Box
                width={"6px"}
                height={"6px"}
                background={"#D3332D"}
                borderRadius={"100%"}
                position={"absolute"}
                top={"0"}
                right={"3px"}
              ></Box>
            )}
          </div>
        </MenuButton>
        <MenuList width={"350px"} shadow={"0px 5px 16px 0px #3014461A"}>
          <div className="flex items-center justify-between border-b border-[#3014461A] p-4">
            <Text fontSize={"20px"} color={"#0A3150"} fontWeight={700}>
              Notifications
            </Text>
            {/* {notifications.some((notification) => !notification.is_read) && ( */}
            <Button
              size="sm"
              variant="ghost"
              onClick={() => markAllAsRead()}
              isLoading={isMarkingAsRead}
            >
              <span className="text-xs text-[#E83831]">Mark all as read</span>
            </Button>
            {/* )} */}
          </div>

          {isLoading ? (
            <Spinner className="absolute left-[50%] top-[50%]" />
          ) : (
            notifications &&
            notifications.map((notification) => (
              <MenuItem
                key={notification.id}
                fontSize={"14px"}
                fontWeight={600}
                borderBottom={"1px solid #3014461A"}
                onClick={() => markAsRead(notification.id)}
              >
                <div className="relative flex w-full max-w-lg gap-[15px]">
                  {!notification.is_read ? (
                    <Image
                      alt="read-notification"
                      src="/images/icons/read.svg"
                      width={"30px"}
                      marginBottom={"16px"}
                    />
                  ) : (
                    <Image
                      alt="unread-notification"
                      src="/images/icons/unread.svg"
                      width={"30px"}
                      marginBottom={"30px"}
                    />
                  )}

                  <div className="flex flex-col gap-2 py-4">
                    <Text
                      fontSize={"14px"}
                      fontWeight={500}
                      color={"#0A3150"}
                      width={"300px"}
                      overflow={"hidden"}
                    >
                      {notification.title}
                    </Text>

                    <Text fontSize={"12px"} fontWeight={500} color={"#8EA0AF"}>
                      {notification.body}
                    </Text>
                  </div>
                </div>
              </MenuItem>
            ))
          )}
        </MenuList>
      </Menu>
      <Menu>
        <MenuButton
          as={Button}
          _hover={{
            bg: "transparent",
          }}
          _active={{
            bg: "transparent",
          }}
        >
          <div className="flex cursor-pointer items-center gap-3">
            <Image alt="Avatar" src="/images/icons/avatar.svg" />
            <span className="text-sm font-semibold">
              {user?.biography?.first_name} {user?.biography?.last_name}
            </span>
            <Image alt="chevron-down" src="/images/icons/chevron-down.svg" />
          </div>
        </MenuButton>
        <MenuList>
          <MenuItem
            fontSize={"14px"}
            fontWeight={600}
            onClick={() => goToProfile()}
          >
            Profile
          </MenuItem>
          <MenuItem fontSize={"14px"} fontWeight={600} onClick={handleLogout}>
            Logout
          </MenuItem>
        </MenuList>
      </Menu>
    </header>
  );
};

export default Header;
