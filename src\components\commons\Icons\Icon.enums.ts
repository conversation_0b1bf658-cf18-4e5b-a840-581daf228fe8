export enum EIconName {
  ENVELOPE = "uil-envelope",
  TIME_CIRCLE = "uil-times-circle",
  BARS = "uil-bars",
  PHONE = "uil-phone",
  DASHBOARD = "dashboard",
  PAYMENT = "payment",
  RESULT = "result",
  COURSES = "courses",
  DASHBOARDACTIVE = "dashboard-active",
  PAYMENTACTIVE = "payment-active",
  RESULTACTIVE = "result-active",
  COURSESACTIVE = "courses-active",
  DEFERMENT = "deferment",
  WHATSAPP = "whatsapp",
  QUESTION = "question",
  ANGLE_LEFT_B = "uil-angle-left-b",
  EXCLAMATION_CIRCLE = "uis-exclamation-circle",
  ANGLE_RIGHT_B = "uil-angle-right-b",
  ARROW_UP = "uil-arrow-up",
  ARROW_DOWN = "uil-arrow-down",
  CHECK_CIRCLE = "uil-check-circle",
  TUITION_FUND_ACTIVE = "tuition-fund-active",
  TUITION_FUND = "tuition-fund",
  MONEY_UP = "money-up",
  SETTINGS = "settings",
  COPY_ALT = "uil uil-copy-alt",
  INFO = "info",
}

export enum EIconSize {
  TINY = 10,
  EXTRA_SM = 12,
  SM_16 = 16,
  SM = 20,
  MD = 24,
  LG = 28,
  XL = 32,
  XXL = 36,
  XXL_72 = 72,
}

export enum TypeIcon {
  SVG = "svg",
  DEFAULT = "default",
  FILL = "fill",
  LINE = "line",
}
