/* eslint-disable @typescript-eslint/no-explicit-any */

import CommonDefermentInputs from "src/module/deferments/CreateDeferment/CommonDefermentInputs";
import React, { useMemo, useState, useEffect } from "react";
import { Checkbox, FormControl, Text } from "@chakra-ui/react";
import InputField from "src/components/commons/InputField/InputField";
import { BaseColor } from "src/constants/colors";
import DatePickerInput from "src/components/commons/InputField/DateInput";
import { ICreateDefermentForm } from "src/module/deferments/CreateDeferment/CreateDefermentForm";
import { InputError } from "src/components/commons/InputField/InputError";
import { useStudentDashboard } from "@/hooks/useStudent/useStudentDashboard";
import ExamLocationsList from "./ExamLocationList";
import { sortProgrammeLevelEnrollments } from "src/module/courses/utils/sortProgrammeEnrollments";

const ExamDefermentForm = ({
  values,
  handleChange,
  handleBlur,
  touched,
  errors,
  setFieldValue,
  isSubmitting,
  isSubmitEnabled,
}: ICreateDefermentForm) => {
  const [selectedCourses, setSelectedCourses] = useState<string[]>([]);

  const { data } = useStudentDashboard();

  const courses = useMemo(() => {
    const enrollments = sortProgrammeLevelEnrollments(
      data?.programme_level_enrollment,
    ).filter((enrollment) => !!enrollment.programme_level_id);

    const last_enrollment = enrollments.length - 1;
    const last_semester =
      (enrollments?.[last_enrollment]?.semesters?.length || 0) - 1;
    return (
      data?.programme_level_enrollment[last_enrollment]?.semesters[
        last_semester
      ].courses || []
    );
  }, [data]);

  const handleCheckboxChange = (value: string) => {
    setSelectedCourses((prevSelected) =>
      prevSelected.includes(value)
        ? prevSelected.filter((course) => course !== value)
        : [...prevSelected, value],
    );
  };

  useEffect(() => {
    setFieldValue("courses", selectedCourses);
  }, [selectedCourses]);

  return (
    <>
      <FormControl mb={6}>
        <Text marginBottom={4} color="#0A3150" fontWeight={600} fontSize={14}>
          Select the course(s) you would like to defer
          <span className="text-red-500">*</span>
        </Text>
        <div className="flex flex-col space-y-6 bg-[#F9FAFB] py-4 pl-4">
          {courses.map((course: any) => (
            <Checkbox
              key={course.course_id}
              isChecked={selectedCourses.includes(course.course_id)}
              onChange={() => handleCheckboxChange(course.course_id)}
            >
              {course.course_name}
            </Checkbox>
          ))}
        </div>
      </FormControl>
      <FormControl mb={6}>
        <Text marginBottom={4} color="#0A3150" fontWeight={600} fontSize={14}>
          Please share your preferred alternative examination date (This will be
          subject to approval)
          <span className="text-red-500">*</span>
        </Text>
        <div className="flex justify-between">
          <div className="w-[45%]">
            <DatePickerInput
              label="Exam Start Date"
              onChange={handleChange}
              type="date"
              min={new Date().toISOString().slice(0, 10)}
              name="start_date"
              value={values.start_date}
              isInvalid={!!errors.start_date && touched.start_date}
              placeholder="Enter start date"
            />
          </div>

          <div className="w-[45%]">
            <DatePickerInput
              label="Exam End Date"
              onChange={handleChange}
              type="date"
              name="end_date"
              min={values.start_date}
              value={values.end_date}
              isInvalid={!!errors.end_date && touched.end_date}
              placeholder="Enter end date"
            />
          </div>
        </div>
      </FormControl>
      <FormControl mb={6}>
        <Text marginBottom={3} color="#0A3150" fontWeight={600} fontSize={14}>
          Where is your location? (Abuja, Lagos, Yola, Adamawa)
          <span className="text-red-500">*</span>
        </Text>
        <InputField
          placeholder="Enter location"
          color="#0A3150"
          name="location"
          fontSize={14}
          maxLength={25}
          _placeholder={{
            color: BaseColor.PRIMARY_400,
            fontSize: "14px",
            fontWeight: 500,
          }}
          onChange={handleChange}
          onBlur={handleBlur}
          value={values.location}
        />
        <InputError error={errors.location} touched={touched.location} />
      </FormControl>
      <FormControl mb={6}>
        <Text marginBottom={3} color="#0A3150" fontWeight={600} fontSize={14}>
          Choose your exam centre below
          <span className="text-red-500">*</span>
        </Text>

        <div className="relative">
          <ExamLocationsList handleChange={handleChange} />
        </div>
      </FormControl>
      <CommonDefermentInputs
        handleChange={handleChange}
        values={values}
        handleBlur={handleBlur}
        touched={touched}
        errors={errors}
        setFieldValue={setFieldValue}
        isSubmitting={isSubmitting}
        isSubmitEnabled={isSubmitEnabled}
      />
    </>
  );
};

export default ExamDefermentForm;
