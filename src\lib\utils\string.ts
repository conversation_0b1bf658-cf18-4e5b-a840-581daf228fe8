export const undefinedToString = (
  s?: string | null,
  defaultString = "",
): string => {
  return s || defaultString;
};

export const beautify = (string?: string): string => {
  const result = undefinedToString(string).replaceAll("_", " ").trim();
  return result
    .toLowerCase()
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

export const capitalizeFirstLetter = (val: string): string => {
  return String(val).charAt(0).toUpperCase() + String(val).slice(1);
};

export const capitalizeAllFirstLetters = (val: string): string =>
  val.replace(
    /(^\w|\s\w)(\S*)/g,
    (_, m1, m2) => m1.toUpperCase() + m2.toLowerCase(),
  );
