import { FormikProps } from "formik";
import { useRouter } from "next/navigation";
import { Flex, Text, Stack, Box } from "@chakra-ui/react";
import { EyeOpenIcon } from "@radix-ui/react-icons";
import { useState } from "react";

import HeaderForm from "../components/HeaderForm";
import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import InputField from "@/components/commons/InputField/InputField";

import { ILoginValue } from "./Login.d";
import { InputError } from "@/components/commons/InputField/InputError";
import { useCASQueryParams } from "../CAS/useCASQueryParams";

const LoginForm = (props: FormikProps<ILoginValue>) => {
  const router = useRouter();
  const {
    touched,
    errors,
    handleSubmit,
    handleChange,
    handleBlur,
    values,
    isSubmitting,
  } = props;
  const { email: emailTouched, password: passwordTouched } = touched;
  const { email: emailError, password: passwordError } = errors;
  const { queryString } = useCASQueryParams();
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);

  const handleForgetPassword = () => {
    router.push(`forgot-password${queryString}`);
  };

  const togglePasswordView = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  return (
    <Flex
      m={{ base: "20px" }}
      flexDir="column"
      width={{ md: "400px", base: "320px" }}
    >
      <HeaderForm title="Log In" subTitle="Enter your account details" />
      <Stack mt="48px" spacing="16px">
        <Box>
          <InputField
            placeholder="Email Address"
            name="email"
            onChange={handleChange}
            onBlur={handleBlur}
            value={values.email}
            size="lg"
          />
          <InputError error={emailError} touched={emailTouched} />
        </Box>
        <Box>
          <div className="relative">
            <InputField
              placeholder="Password"
              type={isPasswordVisible ? "text" : "password"}
              name="password"
              onChange={handleChange}
              onBlur={handleBlur}
              value={values.password}
              size="lg"
            />
            <span className="absolute right-3 top-4 z-[9999] !cursor-pointer">
              <EyeOpenIcon
                className="h-4 w-4"
                onClick={() => togglePasswordView()}
              />
            </span>
          </div>
          <InputError error={passwordError} touched={passwordTouched} />
        </Box>
      </Stack>
      <Text
        cursor="pointer"
        onClick={handleForgetPassword}
        mt="12px"
        textAlign="right"
        fontSize="14px"
      >
        Forgot Password?
      </Text>
      <Stack mt="48px">
        <ButtonCTA
          isLoading={isSubmitting}
          onClick={() => {
            handleSubmit();
          }}
        >
          Login
        </ButtonCTA>
      </Stack>
    </Flex>
  );
};

export default LoginForm;
