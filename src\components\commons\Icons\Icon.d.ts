import { EColor, EFilterColor } from "@/constants/colors";
import { EIconName, EIconSize, TypeIcon } from "./Icon.enums";

export type TIconColor = {
  color?: EColor | EFilterColor;
  backgroundColor?: EColor;
};

export type TIcon = TIconColor & {
  name: EIconName;
};

export type TActiveIcon = Omit<TIcon, "backgroundColor">;

export type TIconProps = TIcon & {
  id?: string;
  className?: string;
  size?: EIconSize;
  hoverStyle?: TIconColor;
  activeIcon?: TActiveIcon;
  disabledIcon?: TIcon;
  selectedIcon?: TIcon;
  isActive?: boolean;
  isSelected?: boolean;
  isDisabled?: boolean;
  isStaticIcon?: boolean;
  onClick?: (e: MouseEvent<HTMLElement>) => void;
  type?: TypeIcon;
};
