"use client";
import { extractAxiosError } from "@/lib/utils/helpers";
import { useToast } from "@chakra-ui/react";
import axios from "axios";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { authApi } from "src/api/config/api";
import { APIResponse } from "src/api/config/api.d";
import { MODULE_ROUTE, Routes } from "src/api/config/routes";

export const useResendActivationEmail = () => {
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const toast = useToast();

  const searchParams = useSearchParams();
  const email = searchParams.get("email");
  useEffect(() => {
    if (!email) {
      toast({
        description: "Account activation link is invalid",
        status: "error",
      });
      router.replace("/login");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [email]);

  const handleSubmit = async () => {
    setLoading(true);
    try {
      await authApi.post<APIResponse<string>>(
        Routes[MODULE_ROUTE.AUTH].RESEND_MAIL,
        {
          email,
        },
      );
      toast({
        description: "Email sent successfully, please check your mail",
        status: "success",
      });
      setLoading(false);
      router.replace("/login");
    } catch (error) {
      if (axios.isAxiosError(error)) {
        setLoading(false);
        toast({
          description: extractAxiosError(error),
          status: "error",
        });
      }
    }
  };

  return {
    handleSubmit,
    loading,
  };
};
