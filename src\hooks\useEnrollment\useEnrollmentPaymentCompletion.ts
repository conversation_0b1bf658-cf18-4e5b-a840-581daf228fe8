import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { getEnrollmentPaymentCompletion } from "../../api/repository/Enrollment/enrollment";

export const useEnrollmentPaymentCompletion = ({
  student_id,
  programme_intake_id,
}: {
  student_id: string;
  programme_intake_id: string;
}) => {
  const query = useQuery({
    queryKey: [
      "getEnrollmentPaymentCompletion",
      { student_id, programme_intake_id },
    ],
    queryFn: () =>
      getEnrollmentPaymentCompletion({
        student_id,
        programme_intake_id,
      }),
    retry: false,
    enabled: !!student_id && !!programme_intake_id,
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
    select: (response) => {
      const data = response.data;

      return data;
    },
  });
  return query;
};
