import { FC, ReactNode } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
  SelectGroup,
} from "@/components/ui/select";
import { ICommonSelectProps } from "@/components/commons/CommonSelect/CommonSelect.d";
import CommonTooltip from "@/components/commons/CommonTooltip/CommonTooltip";
import { cn } from "@/lib/utils";

interface ComponentOption {
  value: string;
  label: string;
  component?: ReactNode;
  disabled?: boolean;
}

interface IComponentSelectProps extends Omit<ICommonSelectProps, "options"> {
  options: ComponentOption[];
}

const ComponentSelect: FC<IComponentSelectProps> = ({
  value,
  label,
  options,
  onChange,
  showIndicatorIcon = true,
  className,
  labelContent,
  onClick,
  openMenu,
  isDisabled,
  tooltipDes,
  placeholder,
}) => {
  return (
    <Select open={openMenu} onValueChange={onChange} value={String(value)}>
      <SelectTrigger
        className={cn(className)}
        showIndicatorIcon={showIndicatorIcon}
        onClick={onClick}
        disabled={isDisabled}
      >
        {label && <SelectLabel>{label}</SelectLabel>}
        {tooltipDes ? (
          <CommonTooltip descriptionTooltip={tooltipDes}>
            <SelectValue placeholder={placeholder}>
              {labelContent ||
                options.find((opt) => opt.value === value)?.label}
            </SelectValue>
          </CommonTooltip>
        ) : (
          <SelectValue placeholder={placeholder}>
            {labelContent || options.find((opt) => opt.value === value)?.label}
          </SelectValue>
        )}
      </SelectTrigger>
      <SelectContent className="bg-white">
        <SelectGroup>
          {options.map((option) => (
            <div key={option.value} className="relative">
              <SelectItem
                value={option.value}
                disabled={option.disabled}
                className={cn(
                  "flex cursor-pointer items-center px-3 py-2 text-sm hover:bg-gray-100",
                  option.disabled &&
                    "pointer-events-none cursor-not-allowed opacity-50",
                )}
              >
                {!option.component && option.label}
              </SelectItem>
              {option.component && (
                <div className="py-2 text-sm">
                  <div className="absolute left-3 top-1/2 flex -translate-y-1/2 items-center gap-2">
                    <p
                      className={cn(
                        option.disabled &&
                          "pointer-events-none cursor-not-allowed opacity-50",
                      )}
                    >
                      {option.label}
                    </p>
                    <div className="pointer-events-none">
                      {option.component}
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
};

export default ComponentSelect;
