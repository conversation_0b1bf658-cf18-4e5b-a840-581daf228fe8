name: <PERSON>uild and Lint

on:
  push:
    branches:
      - main
      - uat
      - staging
      - dev
  pull_request:
    branches:
      - main
      - uat
      - staging
      - dev

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      # Checkout the code from the repository
      - name: Checkout code
        uses: actions/checkout@v3

      # Set up Node.js
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "18"

      # Cache Node.js modules
      - name: Cache Node modules
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      # Install dependencies
      - name: Install dependencies
        run: yarn install

      # Run Lint
      - name: Run Linter
        run: yarn run lint

      # Run Build
      - name: Build Project
        run: yarn run build
