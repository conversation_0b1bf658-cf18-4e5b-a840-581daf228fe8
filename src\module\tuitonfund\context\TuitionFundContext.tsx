"use client";

import React, { createContext, useContext, useState, ReactNode } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import { useSendWalletOTP } from "src/hooks/useWallet/useSendWalletOTP";
import { useWalletSetup } from "src/hooks/useWallet/useWalletSetup";
import { useVerifyWalletOTP } from "src/hooks/useWallet/useVerifyWalletOTP";

export type TuitionFundFormData = {
  otp: string;
  acceptedTerms: boolean;
  pin: string;
  confirmPin: string;
};

interface TuitionFundContextType {
  currentStep: number;
  setCurrentStep: (step: number) => void;
  formData: TuitionFundFormData;
  otpError: string | null;
  isNextDisabled: () => boolean;
  handleNext: () => void;
  handlePrevious: () => void;
  updateFormData: (key: keyof TuitionFundFormData, value: any) => void;
  getActiveStep: () => number;
  sendOTP: () => void;
  resendOTP: () => void;
  verifyOTP: () => void;
  isOTPLoading: boolean;
  isOTPVerifying: boolean;
  isWalletSetupLoading: boolean;
}

const TuitionFundContext = createContext<TuitionFundContextType | undefined>(
  undefined,
);

interface TuitionFundProviderProps {
  children: ReactNode;
}

export const TuitionFundProvider: React.FC<TuitionFundProviderProps> = ({
  children,
}) => {
  const router = useRouter();
  const user = useAuthStore((state) => state.user);
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<TuitionFundFormData>({
    otp: "",
    acceptedTerms: false,
    pin: "",
    confirmPin: "",
  });
  const [otpError, setOtpError] = useState<string | null>(null);

  // API hooks
  const { sendOTP: sendOTPMutation, isLoading: isOTPLoading } =
    useSendWalletOTP();
  const { verifyOTP: verifyOTPMutation, isLoading: isOTPVerifying } =
    useVerifyWalletOTP({
      onSuccess: () => {
        // Move to PIN setup step when OTP is verified successfully
        setCurrentStep(3);
        setOtpError(null);
      },
      onError: (error) => {
        setOtpError(error);
      },
    });
  const { setupWallet: setupWalletMutation, isLoading: isWalletSetupLoading } =
    useWalletSetup({
      onSuccess: () => {
        setCurrentStep(4);
      },
    });

  const steps = [
    "Onboarding",
    "Terms & Conditions",
    "OTP Verification",
    "Setup PIN",
    "Success",
  ];

  const sendOTP = () => {
    if (user?.id) {
      sendOTPMutation({ user_id: user.id });
    }
  };

  const resendOTP = () => {
    sendOTP();
  };

  const verifyOTP = () => {
    if (user?.id && formData.otp) {
      verifyOTPMutation({
        user_id: user.id,
        verify_code: formData.otp,
      });
    }
  };

  const handleNext = () => {
    if (currentStep === 2) {
      if (!formData.otp) {
        setOtpError("Please enter the OTP code.");
        return;
      } else if (formData.otp.length !== 6) {
        setOtpError("OTP code must be 6 digits.");
        return;
      }
      verifyOTP();
      return;
    }

    if (currentStep === 3) {
      if (user?.id) {
        setupWalletMutation({
          user_id: user.id,
          wallet_pin: formData.pin,
          confirm_wallet_pin: formData.confirmPin,
        });
      }
      return;
    }

    if (currentStep < steps.length - 1) {
      setCurrentStep((prev) => prev + 1);
      setOtpError(null);
    } else {
      router.push("/tuition-fund");
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep((prev) => prev - 1);
      setOtpError(null);
    }
  };

  const updateFormData = (key: keyof TuitionFundFormData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const isNextDisabled = () => {
    switch (currentStep) {
      case 1:
        return !formData.acceptedTerms;
      case 2:
        return !formData.otp;
      case 3:
        return (
          !formData.pin ||
          formData.pin.length < 6 ||
          !formData.confirmPin ||
          formData.pin !== formData.confirmPin
        );
      default:
        return false;
    }
  };
  const getActiveStep = () => {
    if (currentStep <= 2) return 0;
    if (currentStep === 3) return 1;
    return 2;
  };

  const value = {
    currentStep,
    setCurrentStep,
    formData,
    otpError,
    isNextDisabled,
    handleNext,
    handlePrevious,
    updateFormData,
    getActiveStep,
    sendOTP,
    resendOTP,
    verifyOTP,
    isOTPLoading,
    isOTPVerifying,
    isWalletSetupLoading,
  };

  return (
    <TuitionFundContext.Provider value={value}>
      {children}
    </TuitionFundContext.Provider>
  );
};

export const useTuitionFund = (): TuitionFundContextType => {
  const context = useContext(TuitionFundContext);
  if (context === undefined) {
    throw new Error("useTuitionFund must be used within a TuitionFundProvider");
  }
  return context;
};
