import { FC, useEffect, useState } from "react";

import Icon from "@/components/commons/Icons/Icon";
import {
  EIconName,
  EIconSize,
  TypeIcon,
} from "@/components/commons/Icons/Icon.enums";
import { Button } from "@/components/ui/button";
import { TIME } from "@/constants/commons";

import { ICopyToClipboardProps } from "./CopyToClipboard.d";
import { EColor } from "@/constants/colors";

const CopyToClipboard: FC<ICopyToClipboardProps> = ({ elementId }) => {
  const [isCopied, setIsCopied] = useState<boolean>(false);

  useEffect(() => {
    const timeout = setTimeout(() => {
      setIsCopied(false);
    }, TIME.DEBOUNCE_COPY_TO_CLIPBOARD);
    return () => clearTimeout(timeout);
  }, [isCopied]);

  const handleCopyToClipboard = () => {
    if (isCopied) return; // Prevent repeated copying while in cooldown

    const element = document.getElementById(elementId);
    if (element) {
      const textToCopy = element.innerText || element.textContent || "";

      // Copy text using the modern clipboard API
      navigator.clipboard
        .writeText(textToCopy)
        .then(() => {
          setIsCopied(true); // Set the copied state if successful
        })
        .catch((error) => {
          console.error("Failed to copy text:", error);
        });
    }
  };

  return (
    <div>
      <Button
        onClick={handleCopyToClipboard}
        variant="ghost"
        className="h-8 w-8 p-0"
      >
        <Icon
          size={EIconSize.SM}
          isStaticIcon={true}
          name={isCopied ? EIconName.CHECK_CIRCLE : EIconName.COPY_ALT}
          color={isCopied ? EColor.FUN_GREEN : "white"}
          type={isCopied ? TypeIcon.LINE : TypeIcon.DEFAULT}
        />
      </Button>
    </div>
  );
};

export default CopyToClipboard;
