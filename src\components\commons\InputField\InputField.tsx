import { FC } from "react";
import { Input, Text, InputGroup } from "@chakra-ui/react";

import { IInputFieldProps } from "./InputField.d";

const InputField: FC<IInputFieldProps> = ({
  label,
  description,
  onChange,
  isInvalid = false,
  placeholder = "Input here",
  rightElement,
  ...props
}) => {
  return (
    <div>
      <div>
        {!!label && (
          <Text mb="8px" fontWeight="600" color="#0A3150" fontSize="14px">
            {label}
          </Text>
        )}
        {!!description && (
          <Text
            mb="8px"
            ml="5px"
            color="#475467"
            fontSize="12px"
            fontWeight="500"
          >
            {description}
          </Text>
        )}
      </div>
      <InputGroup>
        <Input
          errorBorderColor={isInvalid ? "crimson" : undefined}
          placeholder={placeholder}
          onChange={onChange}
          {...props}
        />
        {rightElement}
      </InputGroup>
    </div>
  );
};

export default InputField;
