import { getStudentInstallment } from "./../../api/repository/installments";
import { useToast } from "@chakra-ui/react";
import { useQuery, keepPreviousData } from "@tanstack/react-query";
import { useEffect } from "react";
import axios from "axios";
import { extractAxiosError } from "@/lib/utils/helpers";

export const useStudentInstallment = () => {
  const toast = useToast();

  const query = useQuery({
    queryKey: ["getStudentInstallment"],
    queryFn: () => getStudentInstallment(),
    retry: false,
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
    select: (response) => response.data,
  });

  useEffect(() => {
    if (query.error && axios.isAxiosError(query.error)) {
      toast({
        description: extractAxiosError(query.error),
        status: "error",
      });
    }
  }, [query.isError]);

  return { data: query.data, isLoading: query.isLoading };
};

export const useStudentTransactions = () => {
  const toast = useToast();
  const query = useQuery({
    queryKey: ["getStudentTransactions"],
    queryFn: () => getStudentInstallment(),
    retry: false,
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
    select: (response) => response.data,
  });

  useEffect(() => {
    if (query.error && axios.isAxiosError(query.error)) {
      toast({
        description: extractAxiosError(query.error),
        status: "error",
      });
    }
  }, [query.isError]);

  return { data: query, isLoading: query.isLoading };
};
