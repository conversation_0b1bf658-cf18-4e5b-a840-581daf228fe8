import { NextRequest } from "next/server";
import { GenerateServiceType } from "src/module/auth/CAS/cas";
import {
  createTicket,
  formatZodErrors,
  generateTicket,
  getFailureResponse,
  getUserByAccessToken,
  validateCasService,
} from "src/module/auth/CAS/cas.utils";
import { z, ZodError } from "zod";

const GenerateServiceSchema = z.object({
  service: z.string(),
  access_token: z.string(),
  refresh_token: z.string(),
});

export async function POST(req: NextRequest) {
  const json = await req.json();

  let request: GenerateServiceType;

  try {
    request = GenerateServiceSchema.parse(json); // Validates at runtime
  } catch (error) {
    return Response.json({ error: formatZodErrors(error as ZodError) });
  }

  const urlDecodedService = decodeURIComponent(request.service);
  const validService = await validateCasService(urlDecodedService);
  if (!validService) {
    return getFailureResponse(
      "INVALID_SERVICE",
      "Service is not authorised to the CAS",
      "JSON",
    );
  }

  try {
    const user = await getUserByAccessToken(request.access_token);
    if (user) {
      const ticket = generateTicket("ST");
      await createTicket(
        {
          cas_service_id: validService.id,
          refresh_token: request.refresh_token,
          ticket,
          type: "ST",
          user_id: user.id,
        },
        10,
      );
      return Response.json({ ticket });
    }
  } catch (e) {
    return getFailureResponse(
      "INVALID_ACCESS_TOKEN",
      "Access token is invalid",
      "JSON",
    );
  }

  return Response.json({ request });
}
