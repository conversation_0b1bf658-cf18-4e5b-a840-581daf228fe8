import React, { useMemo } from "react";

import { Text, Box, Stack, Grid, Image, useDisclosure } from "@chakra-ui/react";
import { BaseColor } from "@/constants/colors";
import { Download } from "lucide-react";
import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import UploadDocumentModal from "./UploadDocumentModal";
import { useStudentDocuments } from "@/hooks/useDocuments/useDocuments";
import { Document } from "src/api/repository/Documents/documents.d";
import { format } from "date-fns";
import Link from "next/link";
import CommonLoading from "@/components/commons/CommonLoading/CommonLoading";

const Documents = () => {
  const { data, refetch, isLoading } = useStudentDocuments();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const docs = useMemo(() => {
    if (!data?.data) {
      return;
    }
    return data?.data;
  }, [data?.data]);
  return (
    <Box className="flex min-h-screen flex-col">
      <Box
        justifyContent={"space-between"}
        alignContent={"center"}
        display={"flex"}
        alignItems={"center"}
      >
        <h4 className="text-lg font-bold">Documents</h4>
        <ButtonCTA
          onClick={onOpen}
          className="flex h-[42px] items-center justify-center gap-2 p-2 text-[14px] text-white"
        >
          <Download size="20" color="white" /> Upload Documents{" "}
        </ButtonCTA>
      </Box>
      <Stack paddingTop={"24px"}>
        {isLoading ? (
          <CommonLoading />
        ) : (
          <Grid
            templateColumns={{
              base: "1fr",
              md: "repeat(2, 1fr)",
              lg: "repeat(2, 1fr)",
            }}
            gap={6}
          >
            {data &&
              Array.isArray(docs) &&
              docs.map((document: Document, index: number) => (
                <Box
                  key={`${document?.document_type}_${index}`}
                  display={"flex"}
                  justifyContent={"space-between"}
                  alignItems={"center"}
                  gap={"8px"}
                  border={"1px solid #DDE2E7"}
                  padding={"23px"}
                  borderRadius={"8px"}
                  cursor={"pointer"}
                >
                  <Image
                    src={`/images/${document.document_file_type.includes("pdf") || document.document_file_type === "" ? "pdf.svg" : "jpg.svg"}`}
                    alt={`${document.document_type}_icon`}
                  />
                  <Box display={"flex"} flexDirection={"column"} gap={"2px"}>
                    <Text fontSize={12} fontWeight={400} color={"#5B758A"}>
                      {document.document_type}
                    </Text>
                    <Text fontSize={14} fontWeight={600} color={"#0A3150"}>
                      {document.document_file_type === ""
                        ? "N/A"
                        : document.document_file_type}
                    </Text>
                    <Text fontSize={12} color={BaseColor.PRIMARY_300}>
                      {`Updated  ${
                        document?.created_at
                          ? format(document?.created_at, "MMM dd, yyyy")
                          : "N/A"
                      }`}
                    </Text>
                  </Box>
                  <Link href={document?.path} target="_blank" className="">
                    <Download size="20" />
                  </Link>
                </Box>
              ))}
          </Grid>
        )}
      </Stack>
      <UploadDocumentModal open={isOpen} onClose={onClose} refetch={refetch} />
    </Box>
  );
};

export default Documents;
