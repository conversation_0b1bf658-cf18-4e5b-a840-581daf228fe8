"use client";

import CreateDefermentRequestFormContainer from "src/module/deferments/CreateDeferment/CreateDefermentRequestFormContainer";
import { useCreateDeferment } from "src/module/deferments/CreateDeferment/hooks/useCreateDeferment";

const CreateDefermentRequestWrapper = () => {
  const { handleCreateDeferment } = useCreateDeferment();

  return (
    <div className="mb-6 mt-12 flex w-full justify-center">
      <div className="w-4/5">
        <div className="rounded-t-xl bg-[#0A3150] pb-24 pt-16 text-center text-white">
          <h1 className="mb-2 text-5xl font-bold">DEFERMENT REQUEST</h1>
          <p className="font-medium text-blue-50">
            Every great journey begins with a single step.
          </p>
        </div>

        <div className="bg-gray-200 pb-8">
          <div className="flex w-full justify-center">
            <div className="-mt-16 w-3/4 rounded-xl bg-white p-6">
              <CreateDefermentRequestFormContainer
                isEdit={false}
                initialValues={{}}
                handleCreateDeferment={handleCreateDeferment}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateDefermentRequestWrapper;
