import { ButtonProps } from "@chakra-ui/react";
import { EButtonType } from "@/components/commons/ButtonCTA/ButtonCTA";
import { ReactNode } from "react";

export interface IButtonCTAProps extends ButtonProps {
  children: ReactNode;
  title?: string;
  customType?: EButtonType;
}

export enum EButtonType {
  PRIMARY = "primary",
  DEFAULT = "default",
  SECONDARY = "secondary",
  SUCCESS = "success",
}
