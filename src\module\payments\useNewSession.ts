import { useActiveEnrollment } from "@/hooks/useEnrollment/useActiveEnrollment";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import { isDefaultId } from "src/utils/common";
import { useRouter } from "next/navigation";
import { useMemo } from "react";
import { useEnrollmentPaymentCompletion } from "@/hooks/useEnrollment/useEnrollmentPaymentCompletion";
import { undefinedToString } from "@/lib/utils/string";
import { MIVA_SIS_PAYMENT_URL } from "@/constants/api";

export const useNewSession = () => {
  const router = useRouter();
  const student = useAuthStore((state) => state.student);
  const access_token = useAuthStore((state) => state.access_token);

  const { data: activeEnrollment, isFetching } = useActiveEnrollment();

  const { data: paymentCompletion, isFetching: paymentCompletionIsFetching } =
    useEnrollmentPaymentCompletion({
      student_id: undefinedToString(student?.student_profile?.student_id),
      programme_intake_id: !isDefaultId(
        activeEnrollment?.next_programme_intake_id,
      )
        ? undefinedToString(activeEnrollment?.next_programme_intake_id)
        : "",
    });

  const newSessionIsActive = useMemo(
    () =>
      activeEnrollment &&
      !isDefaultId(activeEnrollment.next_programme_intake_id) &&
      new Date(activeEnrollment.next_programme_intake_publish_date) <=
        new Date(),
    [activeEnrollment],
  );

  const handleNewLevelEnrolment = () => {
    if (paymentCompletion?.type == "payment_completed") {
      router.push(`/enrollment`);
    }
    if (paymentCompletion?.type == "make_payment") {
      const fullUrl = `${MIVA_SIS_PAYMENT_URL}/payment-v2?studentSisId=${student?.student_profile?.student_id}&userToken=${access_token}`;
      window.open(fullUrl, "_blank"); // Open payment in new tab
    }
  };

  return {
    newSessionIsActive,
    handleNewLevelEnrolment,
    activeEnrollment,
    isLoadingActiveEnrollment: isFetching,
    paymentCompletionIsFetching,
    paymentCompletion,
  };
};
