import React from "react";
import { Image, Box, Text, Flex } from "@chakra-ui/react";

const ApplicationDetails = () => {
  return (
    <Box borderRadius={"3.27px"}>
      <Image src="./images/application-img.svg" alt="application" />
      <Text
        color={"#0A3150"}
        fontWeight={700}
        fontSize={"24px"}
        paddingY={"20px"}
      >
        Having trouble with your application?
      </Text>
      <Text>
        Get in touch with an application specialist to get help with the
        application process.
      </Text>
      <Box paddingY={"24px"}>
        <Text fontSize={"16px"} fontWeight={600} marginBottom={"8px"}>
          Whatsapp
        </Text>
        <Flex alignItems={"center"}>
          <Image
            src="./images/icons/whatsapp.svg"
            alt="whatsapp-icon"
            width={"16px"}
            height={"16px"}
          />
          <Text ml={2} fontSize={"16px"} fontWeight={500}>
            +234 ************
          </Text>
        </Flex>
      </Box>
      <Box paddingBottom={"24px"}>
        <Text fontSize={"16px"} fontWeight={600} marginBottom={"8px"}>
          Phone Number
        </Text>
        <Flex alignItems={"center"}>
          <Image
            src="./images/icons/phone.svg"
            alt="phone-icon"
            width={"16px"}
            height={"16px"}
          />
          <Text ml={2} fontSize={"16px"} fontWeight={500}>
            +234 7000 444 000
          </Text>
        </Flex>
      </Box>
      <Box paddingBottom={"24px"}>
        <Text fontSize={"16px"} fontWeight={600} marginBottom={"8px"}>
          Email Address
        </Text>
        <Flex alignItems={"center"}>
          <Image
            src="./images/icons/email.svg"
            alt="email-icon"
            width={"16px"}
            height={"16px"}
          />
          <Text ml={2} fontSize={"16px"} fontWeight={500}>
            <EMAIL>
          </Text>
        </Flex>
      </Box>
    </Box>
  );
};

export default ApplicationDetails;
