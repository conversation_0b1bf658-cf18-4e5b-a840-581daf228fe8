"use client";

import { useState } from "react";
import { Box, VStack, useToast, Flex, useDisclosure } from "@chakra-ui/react";
import InputField from "@/components/commons/InputField/InputField";
import { Button } from "@/components/ui/button";
import { PencilLine } from "lucide-react";
import { PinVerificationModal } from "../components/PinVerificationModal";

const LinkedAccountSettings = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const {
    isOpen: showPinModal,
    onOpen: openPinModal,
    onClose: closePinModal,
  } = useDisclosure();
  const [pin, setPin] = useState("");
  const [isSuccess, setIsSuccess] = useState(false);
  const toast = useToast();

  const [localFormData, setLocalFormData] = useState({
    bankName: "",
    accountNumber: "",
    accountName: "",
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setLocalFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSave = () => {
    if (
      !localFormData.bankName?.trim() ||
      !localFormData.accountNumber?.trim() ||
      !localFormData.accountName?.trim()
    ) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      return;
    }
    openPinModal();
  };

  const handleConfirmPin = async () => {
    if (!pin.trim()) {
      toast({
        title: "Error",
        description: "Please enter your PIN",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    if (pin.length !== 6) {
      toast({
        title: "Error",
        description: "PIN must be 6 digits",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    try {
      setIsSubmitting(true);
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setIsSuccess(true);
      setTimeout(() => {
        closePinModal();
        setIsEditing(false);
        setPin("");
        setIsSuccess(false);
      }, 2000);
    } catch (error) {
      console.error("Error updating bank details:", error);
      toast({
        title: "Error",
        description: "Failed to update bank details. Please try again.",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box
      p={6}
      bg="white"
      borderRadius="lg"
      boxShadow="sm"
      className="space-y-4"
      height="auto"
    >
      <div className="flex justify-between">
        <div className="flex flex-col text-[#0A3150]">
          <h2 className="text-base font-bold tracking-tight">
            Linked Bank Account
          </h2>
          <p className="text-xs">
            Your bank account details are used to make payments.
          </p>
        </div>
        {!isEditing && (
          <Button variant="outline" onClick={() => setIsEditing(true)}>
            <PencilLine size={16} className="mr-2" />
            <span className="hidden lg:block">Edit</span>
          </Button>
        )}
      </div>
      <VStack spacing={4} align="stretch">
        <InputField
          label="Bank Name"
          name="bankName"
          value={localFormData.bankName}
          onChange={handleInputChange}
          isDisabled={!isEditing}
          placeholder="Enter bank name"
          isRequired
        />

        <InputField
          label="Account Number"
          name="accountNumber"
          value={localFormData.accountNumber}
          onChange={handleInputChange}
          isDisabled={!isEditing}
          placeholder="Enter account number"
          type="number"
          isRequired
        />
        <InputField
          label="Account Name"
          name="accountName"
          value={localFormData.accountName}
          onChange={handleInputChange}
          isDisabled={!isEditing}
          placeholder="Enter account name"
          isRequired
        />

        <Box mt={4}>
          {isEditing && (
            <Flex gap={2} className="justify-end">
              <Button
                variant="outline"
                onClick={() => {
                  setIsEditing(false);
                  setPin("");
                }}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleSave}
                disabled={isSubmitting}
                className="bg-[#BB9E7F] px-6 text-white"
              >
                Save
              </Button>
            </Flex>
          )}
        </Box>
      </VStack>

      <PinVerificationModal
        isOpen={showPinModal}
        onClose={closePinModal}
        onConfirm={handleConfirmPin}
        pin={pin}
        setPin={setPin}
        isSubmitting={isSubmitting}
        isSuccess={isSuccess}
        successMessage="Bank details updated successfully!"
      />
    </Box>
  );
};

export default LinkedAccountSettings;
