interface IOptionsItem {
  label: string;
  value: string | number;
}

export const PAGING = {
  DEFAULT_PAGE_SIZE: 10,
  DEFAULT_PAGE_SIZE_20: 20,
  DEFAULT_PAGE_SIZE_50: 50,
  DEFAULT_PAGE_SIZE_100: 100,
  DEFAULT_CURRENT_PAGE: 1,
};
export const GROUP_TYPE_COURSE = {
  CORE: "CORE",
  ELECTIVE: "ELECTIVE",
};

export const TIME = {
  DEFAULT_DEBOUNCE_INPUT: 300,
  DEBOUNCE_COPY_TO_CLIPBOARD: 2000,
};
export const OPTION_PAGE_SIZE: IOptionsItem[] = [
  {
    label: String(PAGING.DEFAULT_PAGE_SIZE),
    value: PAGING.DEFAULT_PAGE_SIZE,
  },
  {
    label: String(PAGING.DEFAULT_PAGE_SIZE_20),
    value: PAGING.DEFAULT_PAGE_SIZE_20,
  },
  {
    label: String(PAGING.DEFAULT_PAGE_SIZE_50),
    value: PAGING.DEFAULT_PAGE_SIZE_50,
  },
  {
    label: String(PAGING.DEFAULT_PAGE_SIZE_100),
    value: PAGING.DEFAULT_PAGE_SIZE_100,
  },
];
