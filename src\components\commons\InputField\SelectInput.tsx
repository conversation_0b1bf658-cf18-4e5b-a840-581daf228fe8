import { Select, Stack, SelectProps, Text } from "@chakra-ui/react";

const SelectInput = (
  props: SelectProps & {
    options?: { value?: string; name: string }[];
    label?: string | undefined;
  },
) => {
  return (
    <Stack>
      {!!props.label && (
        <Text fontWeight="600" color="#0A3150" fontSize="14px">
          {props.label}
        </Text>
      )}
      <Select fontSize={"14px"} border={"1px solid #DDE2E7"} {...props}>
        <option value="">Select option</option>
        {props?.options?.map((option) => (
          <option key={option?.value} value={option?.value}>
            {option?.name}
          </option>
        ))}
      </Select>
    </Stack>
  );
};

export default SelectInput;
