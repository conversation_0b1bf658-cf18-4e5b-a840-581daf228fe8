import { FC } from "react";
import { Tbody, Tr, Td, Flex, Spinner } from "@chakra-ui/react";

import { TableBodyProps } from "./Table.d";

const TableBody: FC<TableBodyProps> = ({ data, pathList, isLoading }) => {
  return (
    <Tbody>
      {data.length > 0 ? (
        data.map((item, dataIndex) => (
          <Tr className={item.rowClassName} key={`row-table-${dataIndex}`}>
            {pathList.map((path, index) => (
              <Td key={`cell-table-${dataIndex}-${index}`}>
                {item[path] === "" ? "-" : item[path]}
              </Td>
            ))}
          </Tr>
        ))
      ) : (
        <Tr>
          <Td colSpan={pathList?.length} className="text-center">
            <Flex alignItems="center" justifyContent="center">
              {isLoading ? <Spinner size="md" /> : "There is no data"}
            </Flex>
          </Td>
        </Tr>
      )}
    </Tbody>
  );
};

export default TableBody;
