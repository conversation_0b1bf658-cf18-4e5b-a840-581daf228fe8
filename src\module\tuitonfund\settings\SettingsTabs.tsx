import React from "react";
import { TabList } from "./Settings";
import { Box, Text } from "@chakra-ui/react";
import { BaseColor } from "@/constants/colors";

export default function SettingsTabs({
  currentTab,
  handleEventFromChild,
}: {
  currentTab: number;
  handleEventFromChild: (item: number) => void;
}) {
  const tabs: TabList[] = [
    {
      id: 1,
      name: "Linked Account",
    },
    {
      id: 2,
      name: "Security",
    },
    {
      id: 3,
      name: "Request Withdrawal",
    },
  ];

  const activateTab = (id: number) => {
    handleEventFromChild(id);
  };

  return (
    <Box minHeight={{ base: "80vh" }}>
      {tabs &&
        tabs.map((tab) => (
          <Text
            key={tab.id}
            paddingY={"10px"}
            cursor={"pointer"}
            background={currentTab === tab.id ? "#E7EAEE" : "transparent"}
            onClick={() => activateTab(tab.id)}
            paddingX={"20px"}
            fontSize={"14px"}
            borderRadius={"8px"}
            marginBottom={"8px"}
            fontWeight={600}
            color={
              currentTab === tab.id ? BaseColor.PRIMARY : BaseColor.PRIMARY_300
            }
          >
            {tab.name}
          </Text>
        ))}
    </Box>
  );
}
