/* eslint-disable react-hooks/exhaustive-deps */
import { useMutation } from "@tanstack/react-query";
import { useToast } from "@chakra-ui/react";
import axios from "axios";
import { extractAxiosError } from "@/lib/utils/helpers";
import { withdrawalRequest } from "src/api/repository/Tuition-fund/withdrawal-request";

export const useRequestWithdrawal = () => {
  const toast = useToast();

  const mutation = useMutation({
    mutationFn: async (values: IWithdrawalFormValues) => {
      const response = await withdrawalRequest(values);
      return response.data;
    },
    onError: (error) => {
      if (axios.isAxiosError(error)) {
        toast({
          description: extractAxiosError(error),
          status: "error",
        });
      }
    },
  });

  return mutation;
};
