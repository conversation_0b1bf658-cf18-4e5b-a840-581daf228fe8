/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import CommonLoading from "@/components/commons/CommonLoading/CommonLoading";
import Pagination from "@/components/commons/CommonPagination/CommonPagination";
import { useStudentApplication } from "@/hooks/useStudent/useStudentApplications";
import { undefinedToString } from "@/lib/utils/string";
import { Suspense, useState, useEffect } from "react";
import { useAuthStore } from "../../store/AuthenticationStore/authentication";
import ApplicationContainer from "./ApplicationContainer";

const ApplicationWrapper = () => {
  const [param, setParams] = useState({
    page: 1,
    perPage: 10,
  });
  const student = useAuthStore((state) => state.student);
  const studentId = undefinedToString(student?.personal_details?.id);

  const {
    data: studentApps,
    pagination,
    setPagination,
    refetch,
    isLoading,
  } = useStudentApplication({
    id: studentId,
    ...param,
  });

  // Ensure data is refetched when student ID changes
  useEffect(() => {
    if (studentId) {
      refetch();
    }
  }, [studentId, refetch]);

  return (
    <div>
      <ApplicationContainer
        data={studentApps?.data || []}
        loading={isLoading}
        studentName={`${student?.personal_details.biography?.first_name}`}
        studentSisId={student?.student_profile?.student_id || ""}
      />
      <Pagination
        currentPage={pagination.pageIndex + 1}
        totalPages={studentApps?.meta?.totalPages || 0}
        itemsPerPage={pagination.pageSize}
        onPageChange={(newPage) =>
          setPagination((prev) => ({
            ...prev,
            pageIndex: newPage - 1,
          }))
        }
        onItemsPerPageChange={(newSize) =>
          setPagination({
            pageIndex: 0,
            pageSize: newSize,
          })
        }
      />
    </div>
  );
};

const ApplicationWrapperWithSuspense = () => (
  <Suspense fallback={<CommonLoading size="medium" />}>
    <ApplicationWrapper />
  </Suspense>
);

export default ApplicationWrapperWithSuspense;
