import React from "react";
import { <PERSON><PERSON>, <PERSON>lex, Text } from "@chakra-ui/react";
import CommonSelect from "../CommonSelect/CommonSelect";
import { OPTION_PAGE_SIZE } from "@/constants/commons";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
  onItemsPerPageChange: (items: number) => void;
}

const Pagination = ({
  currentPage,
  totalPages,
  itemsPerPage,
  onPageChange,
  onItemsPerPageChange,
}: PaginationProps) => {
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      onPageChange(page);
    }
  };

  const handleItemsPerPageChange = (size: number) => {
    if (size > 0) {
      onItemsPerPageChange(size);
    }
  };

  const renderPageNumbers = () => {
    const pages = [];

    if (totalPages <= 6) {
      // Show all pages if there are 6 or fewer
      for (let i = 1; i <= totalPages; i++) {
        pages.push(
          <Button
            key={i}
            onClick={() => handlePageChange(i)}
            colorScheme={i === currentPage ? "black" : "gray"}
            size="sm"
            className={i === currentPage ? "border-black" : "border-none"}
            variant={i === currentPage ? "outline" : "ghost"}
          >
            {i}
          </Button>,
        );
      }
    } else {
      // Show first two, ..., last two
      pages.push(
        <Button
          key={1}
          onClick={() => handlePageChange(1)}
          colorScheme={currentPage === 1 ? "blue" : "gray"}
          size="sm"
          display={{ base: "none", md: "block" }}
          variant={currentPage === 1 ? "solid" : "outline"}
        >
          1
        </Button>,
      );
      pages.push(
        <Button
          key={2}
          onClick={() => handlePageChange(2)}
          colorScheme={currentPage === 2 ? "blue" : "gray"}
          size="sm"
          variant={currentPage === 2 ? "solid" : "outline"}
        >
          2
        </Button>,
      );

      if (currentPage > 3 && currentPage < totalPages - 2) {
        pages.push(
          <Text key="ellipsis1" mx={2}>
            ...
          </Text>,
        );
        pages.push(
          <Button
            key={currentPage}
            onClick={() => handlePageChange(currentPage)}
            colorScheme="blue"
            size="sm"
            variant="solid"
          >
            {currentPage}
          </Button>,
        );
        pages.push(
          <Text key="ellipsis2" mx={2}>
            ...
          </Text>,
        );
      } else {
        pages.push(
          <Text key="ellipsis" mx={2}>
            ...
          </Text>,
        );
      }

      pages.push(
        <Button
          key={totalPages - 1}
          onClick={() => handlePageChange(totalPages - 1)}
          colorScheme={currentPage === totalPages - 1 ? "blue" : "gray"}
          size="sm"
          variant={currentPage === totalPages - 1 ? "solid" : "outline"}
        >
          {totalPages - 1}
        </Button>,
      );
      pages.push(
        <Button
          key={totalPages}
          onClick={() => handlePageChange(totalPages)}
          colorScheme={currentPage === totalPages ? "blue" : "gray"}
          size="sm"
          variant={currentPage === totalPages ? "solid" : "outline"}
        >
          {totalPages}
        </Button>,
      );
    }

    return pages;
  };

  return (
    <Flex align="center" justify="space-between" mt={4} w={"100%"}>
      {/* Navigation Arrows */}
      <div className="flex w-[50%] items-center md:w-full">
        <Flex gap={1}>
          <Button
            onClick={() => handlePageChange(1)}
            isDisabled={currentPage === 1}
            size="sm"
            display={{ base: "none", md: "block" }}
          >
            {"<<"}
          </Button>
          <Button
            onClick={() => handlePageChange(currentPage - 1)}
            isDisabled={currentPage === 1}
            size="sm"
          >
            {"<"}
          </Button>
        </Flex>

        {/* Page Numbers */}
        <Flex align="center" gap={2}>
          {renderPageNumbers()}
        </Flex>

        {/* Navigation Arrows */}
        <Flex gap={2}>
          <Button
            onClick={() => handlePageChange(currentPage + 1)}
            isDisabled={currentPage === totalPages}
            size="sm"
          >
            {">"}
          </Button>
          <Button
            onClick={() => handlePageChange(totalPages)}
            isDisabled={currentPage === totalPages}
            size="sm"
            display={{ base: "none", md: "block" }}
          >
            {">>"}
          </Button>
        </Flex>
      </div>
      {/* Items Per Page */}
      <Flex align="center" gap={2} ml={4} w={"full"} className="justify-end">
        <Text fontSize="sm" fontWeight={700}>
          Show:
        </Text>
        <CommonSelect
          className="w-fit max-w-[55px]"
          showIndicatorIcon={true}
          value={itemsPerPage}
          onChange={(pageSize) =>
            handleItemsPerPageChange &&
            handleItemsPerPageChange(Number(pageSize))
          }
          options={OPTION_PAGE_SIZE}
        />
        <Text fontSize="sm" fontWeight={700}>
          per page
        </Text>
      </Flex>
    </Flex>
  );
};

export default Pagination;
