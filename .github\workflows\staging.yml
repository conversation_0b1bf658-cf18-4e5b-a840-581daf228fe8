name: Staging Deploy to Amazon ECS
on:
  push:
    branches:
      - staging

permissions:
  id-token: write # This is required for requesting the JWT for OIDC
  contents: read

env:
  AWS_REGION: eu-west-2
  ECR_REPOSITORY: miva-sis-web
  ECS_SERVICE: staging-miva-sis-web
  ECS_CLUSTER: Staging-Cluster
  ECS_TASK_DEFINITION: task-definition.json
  SECRET_ID: "/staging/miva-sis-web"
  BRANCH: ${{ github.head_ref || github.ref_name }}

jobs:
  scan:
    name: SonarQube Scan
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
          fetch-depth: 0
      - uses: sonarsource/sonarqube-scan-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
  buildImage:
    name: Build
    runs-on: ubuntu-latest
    needs: scan
    steps:
      - name: Checkout
        uses: actions/checkout@v2
      - name: Login Amazon ECR
        id: login-ecr
        uses: ./.github/workflows/actions/config/login-oidc
        with:
          role-to-assume: ${{ secrets.MIVA_ROLE_TO_ASSUME }}
          region: ${{ env.AWS_REGION }}
      - name: Get Environment Variables
        run: |
          SECRET_VALUE=$(aws secretsmanager get-secret-value \
            --secret-id ${{ env.SECRET_ID }} \
            --region ${{ env.AWS_REGION }} \
            --query 'SecretString' \
            --output text)
          echo "$SECRET_VALUE" >> .env 
      - name: Build for Amazon ECS task
        id: build-image
        uses: ./.github/workflows/actions/build
        with:
          branch: ${{ env.BRANCH }}
          ecrRepo: ${{ env.ECR_REPOSITORY }}
          ecrRegistry: ${{ steps.login-ecr.outputs.registry }}
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    needs: [buildImage]
    steps:
      - name: Checkout
        uses: actions/checkout@v2
      - name: Login Amazon ECR
        id: login-ecr
        uses: ./.github/workflows/actions/config/login-oidc
        with:
          role-to-assume: ${{ secrets.MIVA_ROLE_TO_ASSUME }}
          region: ${{ env.AWS_REGION }}
      - name: Deploy Amazon ECS task
        id: deploy_image
        uses: ./.github/workflows/actions/deploy
        with:
          branch: ${{ env.BRANCH }}
          taskDefinition: ${{ env.ECS_TASK_DEFINITION }}
          service: ${{ env.ECS_SERVICE }}
          containerName: ${{ env.ECS_SERVICE }}
          ecrRepo: ${{ env.ECR_REPOSITORY }}
          cluster: ${{ env.ECS_CLUSTER }}
          ecrRegistry: ${{ steps.login-ecr.outputs.registry }}
      - name: Send Slack Notification
        id: send-notification
        uses: ./.github/workflows/actions/notification/successfulDeploy
        with:
          slack: ${{ secrets.SLACK_WEBHOOK_URL_DEPLOYMENT_CHNL }}

  run-if-failed:
    runs-on: ubuntu-latest
    needs: [scan, buildImage, deploy]
    if: always() && (needs.scan.result == 'failure' || needs.buildImage.result == 'failure' || needs.deploy.result == 'failure')
    steps:
      - name: Checkout
        uses: actions/checkout@v2
      - name: Send slack message on scan failure
        uses: ./.github/workflows/actions/notification/scanFailure
        with:
          scanResult: ${{ needs.scan.result }}
          buildImageResult: ${{ needs.buildImage.result }}
          deployResult: ${{ needs.deploy.result }}
          slack: ${{ secrets.SLACK_WEBHOOK_URL_DEPLOYMENT_CHNL }}
