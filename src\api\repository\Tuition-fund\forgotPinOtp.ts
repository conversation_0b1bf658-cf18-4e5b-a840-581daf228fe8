import { baseApi } from "../../config/api";
import { MODULE_ROUTE, Routes } from "../../config/routes";

export interface IPinOtp {
  user_id: string;
}

export async function ForgotPin(payload: IPinOtp) {
  try {
    const response = await baseApi.post(
      Routes[MODULE_ROUTE.WALLET].FORGOT_PIN_OTP,
      payload,
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching courses:", error);
    throw error;
  }
}
