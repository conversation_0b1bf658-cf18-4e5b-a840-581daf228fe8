import { Flex, Box, Stack } from "@chakra-ui/react";
import { FormikProps } from "formik";

import HeaderForm from "../components/HeaderForm";
import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import { IForgotPasswordValue } from "./ForgotPassword.d";
import InputField from "@/components/commons/InputField/InputField";
import { InputError } from "@/components/commons/InputField/InputError";

const ForgotPasswordForm = (props: FormikProps<IForgotPasswordValue>) => {
  const {
    touched,
    errors,
    handleSubmit,
    values,
    handleChange,
    handleBlur,
    isSubmitting,
  } = props;
  const { email: emailTouched } = touched;
  const { email: emailError } = errors;

  return (
    <Flex
      m={{ base: "20px" }}
      flexDir="column"
      width={{ md: "400px", base: "320px" }}
    >
      <HeaderForm
        title="Forgot Password?"
        subTitle="Don’t worry, we’ll send you reset instructions"
      />
      <Box mt="48px">
        <InputField
          placeholder="Email Address"
          name="email"
          onChange={handleChange}
          onBlur={handleBlur}
          value={values.email}
          size="lg"
        />
        <InputError touched={emailTouched} error={emailError} />
      </Box>
      <Stack mt="48px">
        <ButtonCTA
          size="lg"
          background="#BB9E7F"
          color="white"
          onClick={() => handleSubmit()}
          isLoading={isSubmitting}
        >
          Reset password
        </ButtonCTA>
      </Stack>
    </Flex>
  );
};

export default ForgotPasswordForm;
