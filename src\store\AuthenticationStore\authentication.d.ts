export interface IUserInfoData {
  user?: AuthUser;
  student?: AuthStudent;
}

export interface AuthenticationState {
  access_token: string | null;
  refresh_token: string | null;
  isHydrated: boolean;
  enrollStatus: string;
  user?: AuthUser;
  student?: AuthStudent;
  login: (access_token: string, refresh_token: string) => void;
  setUserInfo: (value: IUserInfoData) => void;
  logout: () => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  checkUserSession: (router: any) => void;
  setEnrollStatus: (status: string) => void;
  setHydrated: () => void;
}

export interface AuthUser {
  biography: {
    date_of_birth: string;
    employment_status: string;
    first_name: string;
    gender: string;
    last_name: string;
    marital_status: string;
    national_id: string;
    nationality: string;
    other_name: string;
    industry?: string;
    company_name?: string;
    title: string;
  };
  contact_information: {
    country: string;
    email: string;
    lga: string;
    next_of_kin: string;
    next_of_kin_phone_number: string;
    phone_number: string;
    residential_address: string;
    state: string;
    city: string;
  };
  application_status: string;
  created_at: Date;
  display_picture: string;
  email_verified: boolean;
  group: string;
  id: string;
  mfa_verified: boolean;
  role: string;
  updated_at: Date;
  student?: {
    courses?: Array<{
      is_active: boolean;
      course_id: string;
      intake_id: string;
      semester_atp_id: string;
      programme_id: string;
    }>;
  };
}

interface PersonalDetails {
  id: string;
  biography: {
    date_of_birth: string;
    employment_status: string;
    first_name: string;
    gender: string;
    last_name: string;
    marital_status: string;
    national_id: string;
    nationality: string;
    other_name: string;
    title: string;
  };
  contact_information: {
    Country: string;
    LGA: string;
    email: string;
    next_of_kin: string;
    next_of_kin_phone_number: string;
    phone_number: string;
    residential_address: string;
    state: string;
    city: string;
  };
  created_at: string;
  display_picture: string;
  email_verified: boolean;
  group: string;
  mfa_verified: boolean;
  role: string;
  updated_at: string;
}

interface AcademicProfile {
  cohort: string | null;
  department: string;
  faculty: string;
  matric_no: string;
  programme: string;
  programme_status: string;
  student_level: string;
  updated_at: string;
}

interface ProgrammeDetails {
  CreatedAt: string;
  Department: string;
  Name: string;
  UpdatedAt: string;
  id: string;
}

interface AuthStudent {
  personal_details: PersonalDetails;
  student_profile: StudentProfile;
  programme_details: ProgrammeDetails;
  application_details: ApplicationDetails;
  documents: Document[];
  semester_atp: SemesterATP[];
}

interface StudentData {
  personal_details: PersonalDetails;
  student_profile: StudentProfile;
  programme_details: ProgrammeDetails;
  application_details: ApplicationDetails;
  documents: Document[];
  semester_atp: SemesterATP[];
}

interface PersonalDetails {
  biography: Biography;
  contact_information: ContactInformation;
  created_at: string;
  display_picture: string;
  email_verified: boolean;
  group: string;
  id: string;
  meta_data: MetaData;
  mfa_verified: boolean;
  role: string;
  updated_at: string;
}

interface Biography {
  date_of_birth: string;
  employment_status: string;
  first_name: string;
  gender: string;
  last_name: string;
  marital_status: string;
  national_id: string;
  nationality: string;
  other_name: string;
  title: string;
}

interface ContactInformation {
  city: string;
  country: string;
  email: string;
  lga: string;
  next_of_kin: string;
  next_of_kin_phone_number: string;
  phone_number: string;
  residential_address: string;
  state: string;
}

interface MetaData {
  modified_by: string;
  modified: string;
}

interface StudentProfile {
  admission_status: string;
  created_at: string;
  matric_no: string;
  student_id: string;
  updated_at: string;
  user_id: number;
}

interface ProgrammeDetails {
  created_at: string;
  id: string;
  image: string;
  name: string;
  updated_at: string;
}

interface ApplicationDetails {
  application_status: string;
  application_type: string;
  atp_id: string;
  atp_type: string;
  created_at: string;
  id: string;
  level: string;
  level_id: string;
  programme_id: string;
  programme_intake_id: string;
  updated_at: string;
}

interface Document {
  created_at: string;
  document_description: string;
  document_file_type: string;
  document_type: string;
  id: string;
  path: string;
  student_id: string;
  updated_at: string;
}

interface SemesterATP {
  created_at: string;
  semester_application_end_date: string;
  semester_application_start_date: string;
  semester_atp_code: string;
  semester_atp_id: string;
  semester_end_date: string;
  semester_enrolment_end_date: string;
  semester_enrolment_start_date: string;
  semester_exam_deferment_end_date: string;
  semester_exam_deferment_start_date: string;
  semester_name: string;
  semester_start_date: string;
  semester_status: string;
  semester_switch_enrollment_cut_off_date: string;
  semester_type: string;
  type: string;
  updated_at: string;
}
