import { useToast } from "@chakra-ui/react";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import axios from "axios";
import { extractAxiosError } from "@/lib/utils/helpers";
import { getStaff, IStaff } from "../../../../api/repository/staff";

export const useSuccessAdvisor = (): {
  data: IStaff[];
  isLoading: boolean;
} => {
  const toast = useToast();

  const query = useQuery({
    queryKey: ["getStaff"],
    queryFn: () => getStaff({ role: "Success_Advisor" }),
    retry: false,
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
    select: (response) => response.data,
  });

  useEffect(() => {
    if (query.error && axios.isAxiosError(query.error)) {
      toast({
        description: extractAxiosError(query.error),
        status: "error",
      });
    }
  }, [query.isError]);

  return { data: query.data || [], isLoading: query.isLoading };
};
