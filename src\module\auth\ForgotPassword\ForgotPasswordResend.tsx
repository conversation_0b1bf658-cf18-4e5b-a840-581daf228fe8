import { Flex, Stack, Text } from "@chakra-ui/react";
import { FC } from "react";

import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import HeaderForm from "../components/HeaderForm";
import { IForgotPasswordResendProps } from "./ForgotPassword";
import { useRouter } from "next/navigation";
import { useCASQueryParams } from "../CAS/useCASQueryParams";

const ResetPasswordResend: FC<IForgotPasswordResendProps> = ({
  email,
  handleResend,
}) => {
  const router = useRouter();
  const { queryString } = useCASQueryParams();

  const handleReset = () => {
    router.push(
      `reset-password?email=${encodeURIComponent(email)}&${queryString.slice(1)}`,
    );
  };

  return (
    <Flex flexDir="column" px={{ base: "20px" }}>
      <HeaderForm title="Reset Password" />
      <Stack width={{ md: "400px", base: "320px" }} mt="48px" spacing="44px">
        <Text
          wordBreak="break-word"
          textAlign="center"
          fontSize="16px"
          fontWeight="Medium"
        >
          We&apos;ve sent an email to {email} with a code to reset your
          password.
        </Text>
        <Text
          wordBreak="break-word"
          textAlign="center"
          fontSize="16px"
          fontWeight="Medium"
        >
          If you have not received the email after a few minutes kindly check
          your spam folder.
        </Text>
      </Stack>
      <Stack mt="48px">
        <ButtonCTA onClick={handleReset}>Continue</ButtonCTA>
      </Stack>
      <div className="pt-4">
        <div className="flex items-center justify-center">
          <Text textAlign="center" fontSize="16px" fontWeight="Medium">
            Didn’t receive an email?
          </Text>
          <Text
            textAlign="center"
            color="#E83831"
            fontSize="16px"
            fontWeight="Medium"
            paddingLeft={"4px"}
            cursor={"pointer"}
            onClick={handleResend}
          >
            Click to resend
          </Text>
        </div>
      </div>
    </Flex>
  );
};

export default ResetPasswordResend;
