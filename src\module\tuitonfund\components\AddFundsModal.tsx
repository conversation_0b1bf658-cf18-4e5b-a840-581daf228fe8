import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { BaseColor, EColor } from "@/constants/colors";
import React, { ChangeEvent, SyntheticEvent, useState } from "react";
import { CommonModal } from "@/components/commons/CommonModal/CommonModal";
import { Box, HStack, Input, ModalBody, Text, VStack } from "@chakra-ui/react";

const AddFundsModal = ({
  open,
  onClose,
}: {
  open: boolean;
  onClose: VoidFunction;
}) => {
  const [target] = useState(1000);
  const [amount, setAmount] = useState(0);
  const [error, setError] = useState(false);

  const inputHandler = (e: ChangeEvent<HTMLInputElement>) => {
    setAmount(Number(e.target.value.replace(/[^\d.]/g, "").trim()));
  };
  const focusHandler = () => {
    setError(false);
  };

  const submitHandler = (e: SyntheticEvent) => {
    e.preventDefault();
    if (amount > target) {
      setError(true);
    }
  };
  return (
    <CommonModal
      title="Add money to your Tuition Fund"
      isOpen={open}
      onClose={onClose}
      size="xl"
    >
      <ModalBody className="space-y-7 !pb-5 !pt-0">
        <Text fontWeight="medium" color={BaseColor.PRIMARY} fontSize={14}>
          Keep building your fund — you&apos;re getting closer to covering your
          full school fees
        </Text>
        <form className="space-y-7" onSubmit={submitHandler}>
          <VStack alignItems="start" position="relative">
            <Text color={BaseColor.PRIMARY} fontWeight={600} fontSize={14}>
              Amount
            </Text>
            <HStack gap={2} className="w-full">
              <Box
                color={BaseColor.PRIMARY}
                fontWeight={500}
                fontSize={14}
                width="40px"
                height="40px"
                borderRadius={8}
                borderWidth={1}
                borderColor="#DDE2E7"
                display="flex"
                alignItems="center"
                justifyContent="center"
                flexShrink={0}
              >
                ₦
              </Box>
              <Input
                type="string"
                borderRadius={8}
                placeholder="0.0"
                value={amount.toLocaleString()}
                onChange={inputHandler}
                onFocus={focusHandler}
              />
            </HStack>
            {error && (
              <Text color={EColor.CINNABAR} fontWeight={500} fontSize={12}>
                The amount you entered goes over your tuition target. Please
                reduce the amount and try again.
              </Text>
            )}
          </VStack>
          <Button className="w-full bg-[#BB9E7F] text-white hover:bg-[#BB9E7F]/60">
            Continue
          </Button>
        </form>
        <Text
          fontWeight="medium"
          color={BaseColor.PRIMARY}
          fontSize={14}
          textAlign="center"
        >
          Have any issues? Contact support at{" "}
          <Link
            href="mailto:<EMAIL>"
            target="_blank"
            className="font-bold text-[]"
          >
            <EMAIL>
          </Link>
        </Text>
      </ModalBody>
    </CommonModal>
  );
};

export default AddFundsModal;
