import { cn } from "@/lib/utils";
import {
  Image,
  Input,
  InputGroup,
  InputLeftAddon,
  InputProps,
  Text,
} from "@chakra-ui/react";
import { ChangeEvent } from "react";

const DatePickerInput = ({
  onChange,
  label,
  type = "datetime-local",
  ...inputProps
}: InputProps & {
  onChange: (event: ChangeEvent<HTMLInputElement>) => void;
  label?: string | undefined;
}) => {
  return (
    <div>
      {!!label && (
        <Text mb="8px" fontWeight="600" color="#0A3150" fontSize="14px">
          {label}
        </Text>
      )}
      <InputGroup
        size={inputProps.size || "md"}
        className={cn(inputProps.isDisabled && "cursor-not-allowed opacity-40")}
      >
        <InputLeftAddon bg={"#fff"}>
          <Image
            src="./images/icons/calender.svg"
            alt="calender-icon"
            className=""
          />
        </InputLeftAddon>
        <Input
          _disabled={{ background: "#fff" }}
          fontSize={"14px"}
          type={type}
          placeholder=""
          onChange={onChange}
          size="md"
          {...inputProps}
        />
      </InputGroup>
    </div>
  );
};

export default DatePickerInput;
