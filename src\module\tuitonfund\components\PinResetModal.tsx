"use client";

//import { Input } from "@/components/ui/input";
import { useState, ChangeEvent } from "react";
import { Eye, EyeOff, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useResetPin } from "@/hooks/useResetPin/useResetPin";
import InputField from "@/components/commons/InputField/InputField";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import CommonLoading from "@/components/commons/CommonLoading/CommonLoading";
import { Box, IconButton, InputRightElement, useToast } from "@chakra-ui/react";

interface PinResetModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  otp: string;
}

export const PinResetModal = ({
  isOpen,
  onClose,
  title = "Set Password",
  otp,
}: PinResetModalProps) => {
  const toast = useToast();
  const user = useAuthStore((state) => state.user);
  const [pin, setPin] = useState({
    new_pin: "",
    confirm_pin: "",
  });
  const { mutateAsync: reset_pin, isPending: reset_pin_pending } =
    useResetPin();
  const [showNewPin, setShowNewPin] = useState(false);
  const [showConfirmPin, setShowConfirmPin] = useState(false);
  const inputChangeHandler = (e: ChangeEvent<HTMLInputElement>) => {
    setPin((prev) => ({
      ...prev,
      [e.target.name]: e.target.value.replace(/[^\d]/g, "").slice(0, 6),
    }));
  };

  const pinChangeHandler = async () => {
    const { new_pin, confirm_pin } = pin;
    if (new_pin.length !== 6) {
      toast({
        title: "Change PIN",
        description: "New pin must be 6 digits",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } else if (confirm_pin !== new_pin) {
      toast({
        title: "Change PIN",
        description: "Confirmation pin does not match new pin",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } else {
      await reset_pin({
        ...pin,
        verify_code: otp,
        user_id: user?.id as string,
      });
      onClose();
      setPin({
        new_pin: "",
        confirm_pin: "",
      });
    }
  };
  const closeModalHandler = () => {
    onClose();
    setPin({
      new_pin: "",
      confirm_pin: "",
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex h-screen items-center justify-center bg-black/70">
      <div className="relative w-[90%] max-w-xl rounded-md bg-white p-6 lg:w-[530px]">
        <button
          onClick={onClose}
          className="absolute right-4 top-4 text-gray-500 hover:text-gray-700 disabled:opacity-50"
        >
          <X size={18} />
        </button>

        <h2 className="mb-1 text-3xl font-bold text-[#0A3150]">{title}</h2>
        <InputField
          label="Create PIN"
          type={showNewPin ? "text" : "password"}
          value={pin.new_pin}
          maxLength={6}
          isRequired
          onChange={inputChangeHandler}
          name="new_pin"
          placeholder="Set a 6-Digit PIN"
          className="mb-6"
          rightElement={
            <InputRightElement>
              <IconButton
                aria-label={showNewPin ? "Hide pin" : "Show pin"}
                variant="ghost"
                onClick={() => setShowNewPin(!showNewPin)}
                icon={showNewPin ? <EyeOff size={16} /> : <Eye size={16} />}
              />
            </InputRightElement>
          }
        />
        <InputField
          label="Confirm PIN"
          type={showConfirmPin ? "text" : "password"}
          value={pin.confirm_pin}
          maxLength={6}
          isRequired
          onChange={inputChangeHandler}
          name="confirm_pin"
          placeholder="Set a 6-Digit PIN"
          className="mb-6"
          rightElement={
            <InputRightElement>
              <IconButton
                aria-label={showConfirmPin ? "Hide pin" : "Show pin"}
                variant="ghost"
                onClick={() => setShowConfirmPin(!showConfirmPin)}
                icon={showConfirmPin ? <EyeOff size={16} /> : <Eye size={16} />}
              />
            </InputRightElement>
          }
        />
        <Box mt={4} display="flex" justifyContent="end" gap={2}>
          <Button variant="outline" onClick={closeModalHandler}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={pinChangeHandler}
            className="bg-[#BB9E7F] px-6 text-white"
          >
            {reset_pin_pending ? <CommonLoading size="small" /> : "Reset PIN"}
          </Button>
        </Box>
      </div>
    </div>
  );
};
