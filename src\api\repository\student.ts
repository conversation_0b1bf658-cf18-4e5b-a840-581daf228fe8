/* eslint-disable @typescript-eslint/no-explicit-any */
import { baseApi } from "../config/api";
import { MODULE_ROUTE, Routes } from "../config/routes";
import { APIResponse, QueryDataPaging } from "src/api/config/api.d";

export interface CourseUnitSummary {
  completed_unit: number;
  discontinue_unit: number;
  enrolled_unit: number;
  total_course_unit: number;
  minimum_unit_required: number;
}

export interface Summary {
  degree: string;
  course_unit_summary: CourseUnitSummary;
  last_grade_point: number;
  overall_cgpa: number;
  minimum_unit_required: number;
  total_credit_units: number;
  total_grade_points: number;
}

export interface IStudent {
  address: string;
  bundle_name: string;
  dob: string;
  email: string;
  enrollment_end_date: string;
  enrollment_start_date: string;
  enrollment_status: string;
  faculty: string;
  full_name: string;
  gender: string;
  matric_no: string;
  profile: string;
  programme: string;
  programme_id: string;
  studentID: string;
  studentID2: string;
}

export interface IStudentTransactionParam extends QueryDataPaging {
  page: number;
  perPage: number;
}

export interface Course {
  course_name: string;
  course_code: string;
  course_image: string;
  credit_unit: number;
  status: string;
  score: number;
  symbol: string;
  result: string;
  created_at: string;
  updated_at: string;
  release_status: string;
  grade_point: number;
  course_id: string;
  group_type: string;
  course_offering_id: string;
  course_enrolment_id: string;
}

export interface Semester {
  semester_id: string;
  semester_name: string;
  semester_type: string;
  type: string;
  semester_period: string;
  gpa: number;
  total_grade_point: number;
  total_credit_unit: number;
  courses: Course[];
  total_scores: number;
  session: string;
  enrollment_date: string;
  enrollment_status: string;
  enrolled_units: number;
  credit_required: number;
  semester_start_date: string;
  semester_end_date: string;
  enrollment_end_date: string;
  semester_status: string;
}

export interface CourseStatusSummary {
  completed: number;
  discontinue: number;
  enrolled: number;
}

export interface ProgrammeLevelEnrollment {
  programme_level_id: string;
  total_grade_point: number;
  level: string;
  academic_time_period_name: string;
  academic_time_period_type: string;
  start_date: string;
  programme_level_enrollment_id: string;
  status: string;
  end_date: string;
  programme_intake_id: string;
  semesters: Semester[];
  course_status_summary: CourseStatusSummary;
}

export interface StudentDashboard {
  summary: Summary;
  student: IStudent;
  programme_level_enrollment: ProgrammeLevelEnrollment[];
}

interface Biography {
  date_of_birth: string;
  employment_status: string;
  first_name: string;
  gender: string;
  last_name: string;
  marital_status: string;
  national_id: string;
  company_name?: string;
  industry?: string;
  nationality: string;
  other_name: string;
  title: string;
}

interface ContactInformation {
  city: string;
  country: string;
  email: string;
  lga: string;
  next_of_kin: string;
  next_of_kin_phone_number: string;
  phone_number: string;
  residential_address: string;
  state: string;
}

interface MetaData {
  modified_by: string;
  modified: string;
}

interface PersonalDetails {
  biography: Biography;
  contact_information: ContactInformation;
  created_at: string;
  display_picture: string;
  email_verified: boolean;
  group: string;
  id: string;
  meta_data: MetaData;
  mfa_verified: boolean;
  role: string;
  updated_at: string;
}

interface StudentProfile {
  admission_status: string;
  created_at: string;
  matric_no: string;
  student_id: number;
  updated_at: string;
  user_id: number;
}

interface ProgrammeDetails {
  created_at: string;
  id: string;
  image: string;
  name: string;
  updated_at: string;
}

interface ApplicationDetails {
  application_status: string;
  application_type: string;
  atp_id: string;
  atp_type: string;
  created_at: string;
  id: string;
  level: string;
  level_id: string;
  programme_id: string;
  programme_intake_id: string;
  updated_at: string;
}

interface Document {
  created_at: string;
  document_description: string;
  document_file_type: string;
  id: string;
  path: string;
  student_id: string;
  updated_at: string;
}

interface SemesterAtp {
  application_end_date: string;
  application_start_date: string;
  atp_code: string;
  created_at: string;
  end_date: string;
  enrolment_end_date: string;
  enrolment_start_date: string;
  exam_deferment_end_date: string;
  exam_deferment_start_date: string;
  semester_exam_deferment_end_date?: string;
  semester_exam_deferment_start_date?: string;
  id: string;
  meta_data: MetaData;
  name: string;
  start_date: string;
  status: string;
  switch_enrollment_cut_off_date: string;
  type: string;
  updated_at: string;
}

interface StudentDetails {
  personal_details: PersonalDetails;
  student_profile: StudentProfile;
  programme_details: ProgrammeDetails;
  application_details: ApplicationDetails;
  documents: Document[];
  semester_atp: SemesterAtp[];
}

export interface NextInstallmentResponse {
  results: NextInstallment[];
}

export interface NextInstallment {
  amount: number;
  currency: string;
  due_date: string;
  fee_installment_id: string;
  payment_status: string;
  student_id: string;
  results: string;
}

// Interface for an individual owed record
interface OwedRecord {
  due_date: string;
  amount_in_naira: number;
  amount_in_dollar: number;
  installment_name: string;
  fee_installment_id: string;
  installment_plan_id: string;
  payment_status?: string;
  amount?: number;
  created_at?: string;
  programme?: string;
  currency?: string;
  date_paid?: string;
  description?: string;
  discount?: number;
  payment_external_id?: string;
  payment_id?: string;
  payment_method?: string;
  payment_type?: string;
  reference?: string;
  student_id?: string;
  updated_at?: string;
  receipt_id?: string;
  receipt_num?: string;
  programme_name?: string;
}

// Interface for an individual paid transaction
interface PaidTransaction {
  amount: number;
  amount_in_dollar: number;
  amount_in_naira: number;
  created_at: string;
  currency: string;
  date_paid: string;
  description: string;
  discount: number;
  due_date: string;
  fee_installment_id: string;
  installment_name: string;
  installment_type: string;
  intake_programme: string;
  level: string;
  payment_external_id: string;
  payment_id: string;
  payment_method: string;
  payment_type: string;
  product_id: string;
  programme: { String: string; Valid: boolean }; // Matches API response structure
  programme_level_id: string | null;
  receipt_id: string;
  receipt_num: string;
  reference: string;
  status: string;
  stdn_student_id: number;
  student_id: string;
  updated_at: string;
  payment_status?: string;
  programme_name?: string;
}

// Interface for student transactions response
interface StudentTransactions<T> {
  owed_records?: OwedRecord[];
  paid?: PaidTransaction[];
  currentPage: number;
  nextPage: number;
  perPage: number;
  prevPage: number;
  total: number;
  totalPages: number;
  data: T;
}

export type Transactions = PaidTransaction | OwedRecord;

export const getStudentDashboard = async (): Promise<
  APIResponse<StudentDashboard>
> => {
  try {
    const { data } = await baseApi.get<APIResponse<StudentDashboard>>(
      Routes[MODULE_ROUTE.STUDENT].DASHBOARD,
    );
    return data;
  } catch (error) {
    console.error("Error fetching student dashboard:", error);
    throw error;
  }
};

export const getStudentDetails = async (): Promise<
  APIResponse<StudentDetails>
> => {
  try {
    const { data } = await baseApi.get<APIResponse<StudentDetails>>(
      Routes[MODULE_ROUTE.STUDENT].GET,
    );
    return data;
  } catch (error) {
    console.error("Error fetching student details:", error);
    throw error;
  }
};

export async function getStudentTransactions({
  page,
  perPage,
}: IStudentTransactionParam): Promise<
  APIResponse<StudentTransactions<Transactions[]>>
> {
  try {
    const response = await baseApi.get<
      APIResponse<StudentTransactions<Transactions[]>>
    >(Routes[MODULE_ROUTE.STUDENT].TRANSACTIONS, {
      params: { page, perPage },
    });
    return response.data;
  } catch (error) {
    console.error("Error getting student transactions:", error);
    throw error;
  }
}

export const getStudentNextInstallment = async (): Promise<
  APIResponse<NextInstallmentResponse>
> => {
  try {
    const { data } = await baseApi.get<APIResponse<NextInstallmentResponse>>(
      Routes[MODULE_ROUTE.STUDENT].NEXT_INSTALLMENT,
    );
    return data;
  } catch (error) {
    console.error("Error fetching next installment:", error);
    throw error;
  }
};
