"use client";
// import Link from "next/link";
import { ReactNode, useEffect } from "react";

import { cn } from "@/lib/utils";
import { usePathname, useRouter } from "next/navigation";
import {
  NO_LAYOUT,
  NO_RESTRICT_APPLICATION_REDIRECT,
} from "@/constants/routers";
import SideBarWrapper from "@/components/templates/SideBar/SideBarWrapper";
import Header from "@/components/templates/Header/Header";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import { ENROLL_STATUS } from "../../../module/auth/Login/constants";
import { canVisitDashboard } from "src/utils/common";

interface MainLayoutProps {
  children: ReactNode;
  className?: string;
}

// This is the place responsible for wrapping your app.
// Add here components like Footer, Nav etc.
export const MainLayout = ({ children, className }: MainLayoutProps) => {
  const wrapperStyles = cn("flex min-h-screen w-screen flex-row", className);
  const pathname = usePathname();
  const router = useRouter();
  const logout = useAuthStore((state) => state.logout);
  const enrollStatus = useAuthStore((state) => state.enrollStatus);
  const access_token = useAuthStore((state) => state.access_token);

  useEffect(() => {
    if (!NO_LAYOUT.includes(pathname) && access_token === "") {
      logout();
      router.push("/login");
    }
    if (
      !NO_RESTRICT_APPLICATION_REDIRECT.includes(pathname) &&
      access_token &&
      !canVisitDashboard(enrollStatus)
    ) {
      router.replace("/dashboard/application");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [access_token, pathname, enrollStatus]);

  if (NO_LAYOUT.includes(pathname)) {
    return (
      <div className={wrapperStyles}>
        <main className="min-h-screen flex-1">{children}</main>
      </div>
    );
  }

  return (
    <div className={wrapperStyles}>
      <SideBarWrapper />
      <div className="flex w-full flex-1 flex-col md:w-[calc(100vw-250px)]">
        <Header />
        <main className="flex-grow bg-[#F0F2F4] px-6 pb-8 md:px-8 lg:px-12">
          {children}
        </main>
        {/* <footer className="flex items-center justify-center p-4">
          ©
          <Link
            href="https://backoffice.staging.miva.university/"
            className="pr-2"
          >
            Miva Development Team
          </Link>
          Copyright {new Date().getFullYear()}
        </footer> */}
      </div>
    </div>
  );
};
