import { beautify } from "@/lib/utils/string";
import { Box } from "@chakra-ui/react";
import { useRouter } from "next/navigation";
import { FC } from "react";
import Courses from "./Courses";
import { ISemestersProps } from "./Semesters.d";

const Semesters: FC<ISemestersProps> = ({ semesters = [], otherDetails }) => {
  const router = useRouter();

  const handleSemesterEnrolment = () => {
    router.push(`/enrollment`);
  };

  return (
    <Box>
      {semesters.map((semester, index: number) => (
        <div key={index}>
          <Courses
            goToClass
            semesterDetails={semester}
            otherDetails={otherDetails}
            courses={semester.courses}
            semester={beautify(semester.semester_type)}
            semesterStartDate={semester.semester_start_date}
            enrollmentEndDate={semester.enrollment_end_date}
            handleSemesterEnrolment={handleSemesterEnrolment}
          />
        </div>
      ))}
    </Box>
  );
};

export default Semesters;
