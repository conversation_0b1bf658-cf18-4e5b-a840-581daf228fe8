import { NextRequest } from "next/server";
import {
  createTicket,
  generateTicket,
  getCASTicket,
  getProxySuccessResponse,
  getFailureResponse,
  isTicketInvalid,
} from "src/module/auth/CAS/cas.utils";

export async function GET(req: NextRequest) {
  const reqUrl = req.url;
  const { searchParams } = new URL(reqUrl);
  const targetService = searchParams.get("targetService");
  const pgt = searchParams.get("pgt");
  const format = searchParams.get("format");

  if (!pgt || !targetService) {
    return getFailureResponse(
      "INVALID_REQUEST",
      "Ticket or service missing",
      format,
    );
  }
  const urlDecodedService = decodeURIComponent(targetService);

  try {
    const casTicket = await getCASTicket(pgt);
    if (await isTicketInvalid(casTicket, "PGT", false)) {
      return getFailureResponse("TICKET_EXPIRED", "Ticket is expired", format);
    }

    const ptTicket = generateTicket("PT");
    await createTicket(
      {
        cas_service_id: casTicket.cas_service_id,
        refresh_token: casTicket.refresh_token,
        ticket: ptTicket,
        type: "PT",
        user_id: casTicket.user_id,
        backend_service: urlDecodedService,
        proxy_granting_ticket: pgt,
      },
      10,
    );

    return getProxySuccessResponse(ptTicket, format);
  } catch (e) {
    let errorMessage = "";
    if (typeof e === "string") {
      errorMessage = e.toUpperCase(); // works, `e` narrowed to string
    } else if (e instanceof Error) {
      errorMessage = e.message; // works, `e` narrowed to Error
    }
    return getFailureResponse(
      "INVALID_TICKET",
      `Ticket is invalid error - ${errorMessage}`,
      format,
    );
  }
}
