import { extractAxiosError } from "@/lib/utils/helpers";
import { useToast } from "@chakra-ui/react";
import { useMutation } from "@tanstack/react-query";
import axios from "axios";
import {
  ForgotPin,
  IPinOtp,
} from "src/api/repository/Tuition-fund/forgotPinOtp";

export const useForgotPin = () => {
  const toast = useToast();
  return useMutation({
    mutationFn: async (values: IPinOtp) => {
      return await ForgotPin(values);
    },
    onError: (error: any) => {
      if (axios.isAxiosError(error)) {
        toast({
          description: extractAxiosError(error),
          status: "error",
        });
      }
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "OTP sent successfully",
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    },
  });
};
