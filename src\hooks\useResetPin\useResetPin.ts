import { useMutation } from "@tanstack/react-query";
import { useToast } from "@chakra-ui/react";
import { ResetPin, IPinReset } from "src/api/repository/Tuition-fund/resetPin";
import { extractAxiosError } from "@/lib/utils/helpers";
import axios from "axios";

export const useResetPin = () => {
  const toast = useToast();
  return useMutation({
    mutationFn: async (values: IPinReset) => {
      return await ResetPin(values);
    },
    onError: (error: any) => {
      if (axios.isAxiosError(error)) {
        toast({
          description: extractAxiosError(error),
          status: "error",
        });
      }
    },
    onSuccess: (data) => {
      toast({
        title: "Success",
        description: "Pin Reset successfully",
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    },
  });
};
