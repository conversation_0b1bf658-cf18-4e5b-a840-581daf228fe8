import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { AuthenticationState, AuthStudent, AuthUser } from "./authentication.d";
import { baseApi } from "src/api/config/api";
import { MODULE_ROUTE, Routes } from "src/api/config/routes";
import { APIResponse } from "src/api/config/api.d";

export const useAuthStore = create<AuthenticationState>()(
  devtools(
    persist(
      (set, get) => ({
        access_token: null,
        refresh_token: null,
        user: undefined,
        isHydrated: false,
        enrollStatus: "",
        login: (access_token, refresh_token) => {
          set({
            access_token,
            refresh_token,
          });
        },
        setUserInfo: (value) => {
          set({
            user: value.user,
            student: value.student,
          });
        },
        logout: () => {
          set({
            access_token: null,
            refresh_token: null,
            user: undefined,
            enrollStatus: "",
            student: undefined,
          });
        },
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        checkUserSession: async (router: any) => {
          const accessToken = get().access_token;
          if (accessToken) {
            try {
              const { user, student } = await getCurrentUserAndStudent();
              set({ user, student });
              router.push("/dashboard"); // Redirect to dashboard if there is the session for student
            } catch (error) {
              console.error("Failed to fetch user or student", error);
              set({ access_token: null, refresh_token: null, user: undefined });
            }
          }
        },
        setHydrated: () => set({ isHydrated: true }),
        setEnrollStatus: (status: string) => set({ enrollStatus: status }),
      }),
      {
        name: "auth-storage",
        onRehydrateStorage: () => (state) => {
          state?.setHydrated();
        },
      },
    ),
  ),
);
export const getCurrentUserAndStudent = async (): Promise<{
  user: AuthUser;
  student?: AuthStudent;
}> => {
  const user = (
    await baseApi.get<APIResponse<AuthUser>>(Routes[MODULE_ROUTE.AUTH].ME)
  ).data.data;
  let student: AuthStudent | undefined = undefined;
  try {
    student = (
      await baseApi.get<APIResponse<AuthStudent>>(
        Routes[MODULE_ROUTE.STUDENT].GET,
      )
    ).data.data;
  } catch (e) {
    console.error(e);
  }
  return { user, student };
};
