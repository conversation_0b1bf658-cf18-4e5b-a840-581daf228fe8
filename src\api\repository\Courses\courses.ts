/* eslint-disable @typescript-eslint/no-explicit-any */
import { APIResponse } from "src/api/config/api.d";
import { baseApi } from "src/api/config/api";
import { MODULE_ROUTE, Routes } from "src/api/config/routes";
import { StudentEnrollment } from "./courses.d";
import { sortEnrollments } from "src/module/courses/utils/sortProgrammeEnrollments";

export const fetchAllCourses = async ({ studentId }: { studentId: string }) => {
  try {
    const response = await baseApi.get<APIResponse<StudentEnrollment[]>>(
      Routes[MODULE_ROUTE.STUDENT].COURSE_LIST,
      {
        params: {
          student_id: studentId,
          filter: "all",
        },
      },
    );
    if (response?.data.data?.length > 0) {
      response.data.data = sortEnrollments(response?.data?.data || []);
    }
    return response.data;
  } catch (error) {
    console.error("Error fetching courses:", error);
    throw error;
  }
};
