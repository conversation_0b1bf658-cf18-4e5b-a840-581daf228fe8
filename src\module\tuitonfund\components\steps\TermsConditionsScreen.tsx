"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { useTuitionFund } from "../../context/TuitionFundContext";
import { ArrowLeftIcon, ArrowRightIcon } from "lucide-react";

const TermsConditionsScreen: React.FC = () => {
  const { handleNext, handlePrevious } = useTuitionFund();

  return (
    <div className="rounded-xl bg-white lg:w-[840px]">
      <div className="mb-6 rounded-se-xl rounded-ss-xl bg-red-600 py-4 text-center text-white">
        <h2 className="text-xl font-bold">Terms and Conditions</h2>
      </div>

      <div className="px-12 text-base text-[#0A3150]">
        <ul className="list-disc pl-6 lg:space-y-2">
          <li>
            Students are required to save only up to the total tuition amount
            for their academic year.
          </li>
          <li>
            Funds saved are strictly intended for the payment of tuition fees
            and other approved academic charges.
          </li>
          <li>
            Students are not permitted to withdraw any portion of their funds
            during the course of their studies.
          </li>
          <li>
            Withdrawal of funds is allowed only after the student has completed
            their final academic year.
          </li>
          <li>
            A formal written request must be submitted to initiate a withdrawal
            after completion of studies.
          </li>
          <li>
            All withdrawal requests will be subject to verification and approval
            in accordance with the institution&apos;s financial policies.
          </li>
        </ul>
      </div>

      <div className="flex justify-between p-6">
        <Button
          variant="outline"
          onClick={handlePrevious}
          className="px-6 text-[#0A3150]"
        >
          <ArrowLeftIcon className="mr-2" /> Go Back
        </Button>

        <Button
          variant="primary"
          onClick={handleNext}
          className="bg-[#0A3150] px-6 text-white"
        >
          Get Started <ArrowRightIcon className="ml-2" />
        </Button>
      </div>
    </div>
  );
};

export default TermsConditionsScreen;
