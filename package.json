{"name": "miva-sis-web", "version": "0.1.0", "description": "Miva SIS Portal for Student", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "prettier:format": "prettier --write \"src/**/*.{ts,tsx,json}\"", "prettier:check": "prettier --check \"src/**/*.{ts,tsx,json}\"", "lint": "next lint", "eslint:format": "eslint src --fix", "test": "jest test", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "test:ci": "jest --ci", "storybook": "storybook dev -p 6006", "storybook:build": "storybook build", "postinstall": "husky", "generate": "yarn plop --plopfile ./.plop/plopfile.js"}, "engines": {"node": ">=18.20.2"}, "packageManager": "yarn@1.22.22", "dependencies": {"@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-tooltip": "^1.1.4", "@t3-oss/env-nextjs": "0.10.1", "@tanstack/react-query": "^5.59.20", "@tanstack/react-query-devtools": "^5.55.0", "@tanstack/react-table": "^8.21.3", "@tanstack/table-core": "^8.21.3", "@types/yup": "^0.32.0", "axios": "^1.7.2", "class-variance-authority": "^0.7.1", "clsx": "2.1.1", "country-state-picker": "^1.1.5", "date-fns": "^3.6.0", "file-saver": "^2.0.5", "formik": "^2.4.6", "framer-motion": "^11.2.10", "lucide-react": "^0.460.0", "next": "14.2.3", "pretty-quick": "^4.0.0", "react": "18.3.1", "react-dom": "18.3.1", "react-infinite-scroll-component": "^6.1.0", "react-phone-input-2": "^2.15.1", "sass": "^1.77.6", "string-width": "^7.1.0", "tailwind-merge": "2.3.0", "use-debounce": "^10.0.4", "validator": "^13.12.0", "yup": "^1.6.1", "zod": "3.23.8", "zustand": "^4.5.5"}, "devDependencies": {"@storybook/addon-essentials": "^8.1.1", "@storybook/addon-interactions": "^8.1.4", "@storybook/addon-links": "^8.1.5", "@storybook/blocks": "^8.1.3", "@storybook/nextjs": "^8.1.5", "@storybook/react": "^8.1.1", "@storybook/testing-library": "^0.2.2", "@testing-library/jest-dom": "6.4.5", "@testing-library/react": "15.0.7", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "14.5.2", "@types/file-saver": "^2.0.7", "@types/jest": "29.5.12", "@types/node": "^20.14.5", "@types/react": "^18.3.3", "@types/react-dom": "18.3.0", "@types/validator": "^13.12.0", "@typescript-eslint/eslint-plugin": "7.0.0", "@typescript-eslint/parser": "6.21.0", "autoprefixer": "10.4.18", "babel-jest": "29.7.0", "eslint": "^8.57.0", "eslint-config-next": "14.2.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-helpers": "1.3.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "7.34.1", "eslint-plugin-react-hooks": "4.6.2", "eslint-plugin-storybook": "0.8.0", "eslint-plugin-testing-library": "6.2.2", "husky": "9.0.11", "identity-obj-proxy": "3.0.0", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "jest-watch-typeahead": "2.2.2", "lint-staged": "15.2.5", "pinst": "3.0.0", "plop": "4.0.1", "postcss": "8.4.38", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "0.6.0", "storybook": "^8.1.4", "tailwind-scrollbar": "3.1.0", "tailwindcss": "3.4.3", "typescript": "5.4.2"}, "lint-staged": {"src/**/*.{ts,tsx}": ["pretty-quick --staged", "eslint --fix", "prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}