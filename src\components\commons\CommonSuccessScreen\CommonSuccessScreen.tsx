import React, { <PERSON> } from "react";
import { Box, Image, Text } from "@chakra-ui/react";
import { BaseColor } from "@/constants/colors";
import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import { ICommonSuccessScreenProps } from "./CommonSuccessScreen.d";

const CommonSuccessScreen: FC<ICommonSuccessScreenProps> = ({
  buttonText,
  onClick,
  showLogo,
  subTitle,
  title,
  imageUrl,
}) => {
  return (
    <Box justifyContent={"center"} display={"flex"} flexDir={"column"}>
      {showLogo && (
        <Box
          my={"40px"}
          justifyContent={"center"}
          display={"flex"}
          className="left-[20px] top-[14px] hidden lg:static lg:flex"
        >
          <Image
            src="/images/logo-miva-sidebar.png"
            alt="logo-miva-sidebar"
            className="w-[120px] md:w-[136px]"
          />
        </Box>
      )}
      <Box
        width={"100%"}
        maxWidth={"598px"}
        display={"flex"}
        flexDirection={"column"}
        justifyContent={"center"}
        alignItems={"center"}
        height={{ base: "447px", md: "525px" }}
        borderRadius={"24px"}
        background={"#092D49"}
        mx={"auto"}
        padding={"15px"}
      >
        <Image
          src={imageUrl}
          alt="Success"
          width={"164.5px"}
          height={"147px"}
        />
        <Box maxWidth={{ base: "283px", md: "365px" }} mt={4}>
          <Text
            fontWeight={700}
            textAlign={"center"}
            mb="9px"
            fontSize={28}
            color={"white"}
          >
            {title}
          </Text>
          <Text
            fontWeight={600}
            textAlign={"center"}
            fontSize={14}
            color={"white"}
          >
            {subTitle}
          </Text>

          <Box justifyContent={"center"} display={"flex"}>
            <ButtonCTA
              background={"white"}
              onClick={onClick}
              mt={"25px"}
              fontSize={12}
              height="auto"
              py={3}
              px={7}
            >
              <Text fontSize={16} color={BaseColor.PRIMARY} fontWeight={700}>
                {buttonText}
              </Text>
            </ButtonCTA>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default CommonSuccessScreen;
