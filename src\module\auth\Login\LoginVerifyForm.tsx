import { FormikProps } from "formik";
import { useState } from "react";

// import { useRouter } from "next/navigation";
import { Flex, Text, Stack, Box, useToast } from "@chakra-ui/react";

import HeaderForm from "../components/HeaderForm";
import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import InputField from "@/components/commons/InputField/InputField";

import { ILoginVerifyValue } from "./Login";
import { InputError } from "@/components/commons/InputField/InputError";
import { requestResendCode } from "src/api/repository/Auth/auth";

const LoginVerifyForm = (props: FormikProps<ILoginVerifyValue>) => {
  // const router = useRouter();
  const {
    touched,
    errors,
    handleSubmit,
    handleChange,
    handleBlur,
    values,
    isSubmitting,
  } = props;
  const { code: codeTouched } = touched;
  const { code: codeError } = errors;
  const [isResendDisabled, setIsResendDisabled] = useState(false);
  const toast = useToast();

  const handleResendCode = async () => {
    setIsResendDisabled(true);
    try {
      await requestResendCode(values.email);
      toast({
        description: "OTP resent successfully",
        status: "success",
      });
    } catch (ex) {
      toast({
        description: "Failed to resend OTP. Please try again",
        status: "error",
      });
    }

    setTimeout(() => {
      setIsResendDisabled(false); // Re-enable the button after 30 seconds
    }, 10000);
  };

  return (
    <Flex
      m={{ base: "20px" }}
      flexDir="column"
      width={{ md: "400px", base: "320px" }}
    >
      <HeaderForm
        title="Verify Log In"
        subTitle="Enter your verification code"
      />
      <Stack mt="48px" spacing="16px">
        <Box>
          <InputField
            placeholder="Code"
            name="code"
            onChange={handleChange}
            onBlur={handleBlur}
            value={values.code}
          />
          <InputError error={codeError} touched={codeTouched} />
        </Box>
      </Stack>

      <Stack mt="48px">
        <Box
          display="flex"
          justifyContent="flex-end"
          gap="10px"
          paddingBottom={"10px"}
        >
          <Text>Did not receive code?</Text>
          <Text
            fontSize="sm"
            fontWeight={isResendDisabled ? "normal" : "bold"}
            as="button"
            color={isResendDisabled ? "grey.200" : "blue.500"}
            textDecoration={"underline"}
            onClick={() => {
              handleResendCode();
            }}
            cursor={isResendDisabled ? "not-allowed" : "pointer"}
            opacity={isResendDisabled ? 0.5 : 1}
          >
            Resend Code
          </Text>
        </Box>
        <ButtonCTA
          isLoading={isSubmitting}
          onClick={() => {
            handleSubmit();
          }}
        >
          Verify
        </ButtonCTA>
      </Stack>
    </Flex>
  );
};

export default LoginVerifyForm;
