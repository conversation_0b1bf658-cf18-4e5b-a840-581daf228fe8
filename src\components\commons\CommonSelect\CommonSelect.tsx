import { FC } from "react";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
  SelectGroup,
} from "@/components/ui/select";

import { ICommonSelectProps } from "./CommonSelect.d";
import { removeSpecialCharacters } from "src/utils/StringUtils";
import CommonTooltip from "@/components/commons/CommonTooltip/CommonTooltip";
import { cn } from "@/lib/utils";

const CommonSelect: FC<ICommonSelectProps> = ({
  value,
  label,
  options,
  onChange,
  showIndicatorIcon = true,
  className,
  labelContent,
  onClick,
  openMenu,
  isDisabled,
  tooltipDes,
  placeholder,
}) => {
  return (
    <Select open={openMenu} onValueChange={onChange} value={String(value)}>
      <SelectTrigger
        className={cn(className)}
        showIndicatorIcon={showIndicatorIcon}
        onClick={onClick}
        disabled={isDisabled}
      >
        {tooltipDes && isDisabled ? (
          <CommonTooltip descriptionTooltip={tooltipDes}>
            <div className="flex items-center justify-start gap-[8px]">
              {!!label && <span>{label}</span>}
              <SelectValue placeholder={placeholder} />
            </div>
          </CommonTooltip>
        ) : (
          <div className="flex items-center justify-start gap-[8px]">
            {!!label && <span>{label}</span>}
            <SelectValue placeholder={placeholder} />
          </div>
        )}
      </SelectTrigger>
      <SelectContent className="bg-white">
        <SelectGroup>
          {!!labelContent && <SelectLabel>{labelContent}</SelectLabel>}
          {options.length === 0 && (
            <div className="p-[16px] text-[14px] font-[600] text-[#5B758A]">
              There is no data!
            </div>
          )}
          {options.map((option, index) => {
            return (
              <SelectItem
                key={index}
                value={String(option.value)}
                className="hover:bg-gray-100"
              >
                {removeSpecialCharacters(option.label)}
              </SelectItem>
            );
          })}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
};

export default CommonSelect;
