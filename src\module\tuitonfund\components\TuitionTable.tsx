"use client";
import React, { useState } from "react";
import { format, parseISO } from "date-fns";
import NoTransactions from "./NoTransactions";
import { BaseColor } from "@/constants/colors";
import { ColumnDef } from "@tanstack/table-core";
import { Box, HStack, Text, VStack } from "@chakra-ui/react";
import CommonTable from "@/components/commons/CommonTable/CommonTable";
import { TRowsData } from "@/components/commons/CommonTable/CommonTable.d";
import { tuitionListHeader, TuitionTableKeys } from "../constants/TableHeader";

const data = [
  {
    type: "deposit",
    date: "2025-03-04",
    amount: "₦300,000",
    transactionReference: "74792347972",
  },
  {
    type: "deposit",
    date: "2025-03-04",
    amount: "₦300,000",
    transactionReference: "74792347972",
  },
  {
    type: "deposit",
    date: "2025-03-04",
    amount: "₦300,000",
    transactionReference: "74792347972",
  },
  {
    type: "deposit",
    date: "2025-03-04",
    amount: "₦300,000",
    transactionReference: "74792347972",
  },
  {
    type: "deposit",
    date: "2025-03-04",
    amount: "₦300,000",
    transactionReference: "74792347972",
  },
];

const TuitionTable = () => {
  const [pagination, setPagination] = useState<{
    pageIndex: number;
    pageSize: number;
  }>({
    pageIndex: 1,
    pageSize: 10,
  });
  const columns: ColumnDef<TRowsData, any>[] = tuitionListHeader.map((item) => {
    if (item.key === TuitionTableKeys.transaction) {
      return {
        accessorKey: item.key,
        header: () => (
          <div className="flex w-1/3 flex-row">
            <div className="text-center">{item.label}</div>
          </div>
        ),
        cell: ({ row }) => {
          return (
            <div className="w-1/3">
              <Text
                textTransform="capitalize"
                fontWeight={500}
                fontSize={14}
                color={BaseColor.PRIMARY}
              >
                {row.original.type}
              </Text>
              <Text
                textTransform="capitalize"
                fontWeight={500}
                fontSize={12}
                color={BaseColor.PRIMARY_300}
              >
                {row.original.date}
              </Text>
            </div>
          );
        },
      };
    }
    if (item.key === TuitionTableKeys.amount) {
      return {
        accessorKey: item.key,
        header: () => <div className="text-center">{item.label}</div>,
        cell: ({ row }) => {
          return (
            <Text
              fontSize={14}
              color={Number(row.id) % 2 === 0 ? "#009933" : "#D3332D"}
              fontWeight={500}
              textAlign="center"
            >
              {row.original.amount}
            </Text>
          );
        },
      };
    }
    return {
      accessorKey: item.key,
      header: () => <div className="text-end">{item.label}</div>,
      cell: ({ row }) => {
        return (
          <Text
            fontWeight={500}
            fontSize={14}
            color={BaseColor.PRIMARY}
            textAlign="end"
          >
            {row.original.transactionReference}
          </Text>
        );
      },
    };
  });

  return (
    <div className="w-full space-y-8 overflow-x-auto rounded-lg bg-transparent lg:bg-white lg:p-6">
      <Text color={BaseColor.PRIMARY} fontWeight={700} fontSize={20}>
        Tuition Fund Transactions
      </Text>
      {data.length > 0 ? (
        <>
          <div className="block space-y-4 lg:hidden">
            {" "}
            {data.map((transaction, index) => (
              <VStack
                px="20px"
                py="16px"
                backgroundColor="white"
                key={index}
                alignItems="start"
                gap={2}
                borderRadius="16px"
              >
                <HStack
                  justifyContent="space-between"
                  className="w-full"
                  alignItems="start"
                >
                  <Text
                    color={BaseColor.PRIMARY}
                    fontWeight={500}
                    fontSize={14}
                  >
                    {format(parseISO(transaction.date), "dd MMM yyyy")}
                  </Text>
                  <Box>
                    <Text color="#8EA0AF" fontWeight={500} fontSize={12}>
                      Transaction Ref.
                    </Text>
                    <Text
                      color={BaseColor.PRIMARY}
                      fontWeight={500}
                      fontSize={14}
                    >
                      {transaction.transactionReference}
                    </Text>
                  </Box>
                </HStack>
                <Box>
                  {" "}
                  <Text
                    color={BaseColor.PRIMARY}
                    fontWeight={600}
                    fontSize={14}
                  >
                    Deposit
                  </Text>
                  <Text
                    fontSize={32}
                    color={index % 2 === 0 ? "#009933" : "#D3332D"}
                    fontWeight={700}
                  >
                    {transaction.amount}
                  </Text>
                </Box>
              </VStack>
            ))}
          </div>
          <div className="hidden lg:block">
            <CommonTable
              pagination={pagination}
              setPagination={setPagination}
              dataTable={data}
              columnsTable={columns}
              totalNumOfPages={1}
              isLoading={false}
            />
          </div>
        </>
      ) : (
        <NoTransactions />
      )}
    </div>
  );
};

export default TuitionTable;
