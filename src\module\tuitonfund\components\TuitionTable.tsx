"use client";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import React, { useEffect, useRef, useState } from "react";
import { format, isValid, parseISO } from "date-fns";
import NoTransactions from "./NoTransactions";
import { BaseColor } from "@/constants/colors";
import { ColumnDef } from "@tanstack/table-core";
import { Box, HStack, Text, VStack, Stack, Skeleton } from "@chakra-ui/react";
import CommonTable from "@/components/commons/CommonTable/CommonTable";
import { TRowsData } from "@/components/commons/CommonTable/CommonTable.d";
import { tuitionListHeader, TuitionTableKeys } from "../constants/TableHeader";
import { useWalletTransactions } from "@/hooks/useWallet/useWalletTransaction";
import { formatCurrency } from "@/lib/utils/helpers";
import { cn } from "@/lib/utils";

const TuitionTable = () => {
  const triggerRef = useRef<HTMLButtonElement>(null);
  const [width, setWidth] = useState<number | undefined>(undefined);

  // Pagination state (0-based index for table, 1-based for API)
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  // Fetch data with pagination (convert to 1-based for API)
  const {
    data: walletData,
    loading,
    error,
    refetch,
  } = useWalletTransactions(pagination.pageIndex + 1, pagination.pageSize);

  useEffect(() => {
    if (triggerRef.current) {
      setWidth(triggerRef.current.offsetWidth);
    }
  }, [triggerRef.current]);

  const [openSort, setOpenSort] = useState(false);
  const [sort, setSort] = useState("Most Recent");

  // Custom date formatter for the API's date format
  const formatTransactionDate = (dateString: string) => {
    if (!dateString) return "-";

    // Extract just the date part (before first space)
    const datePart = dateString.split(" ")[0];
    const parsedDate = parseISO(datePart);

    if (!isValid(parsedDate)) return "-";

    return format(parsedDate, "dd MMM yyyy");
  };

  // Format the API data to match the table structure
  const formattedData =
    walletData?.data?.map((transaction) => ({
      id: transaction.Id,
      type: transaction.TransactionType.toLowerCase(),
      date: transaction.CreatedAt,
      formattedDate: formatTransactionDate(transaction.CreatedAt),
      amount: formatCurrency(
        Math.abs(transaction.Amount),
        transaction.Currency,
      ),
      transactionReference: transaction.ReferenceCode,
      isCredit: transaction.TransactionType === "CREDIT",
      rawAmount: transaction.Amount,
      currency: transaction.Currency,
    })) || [];

  // Handle sort change
  const handleSortChange = (sortType: string) => {
    setSort(sortType);
    // Pass true for "Most Recent", false otherwise
    refetch(sortType === "Most Recent");
  };

  const columns: ColumnDef<TRowsData, any>[] = tuitionListHeader.map((item) => {
    if (item.key === TuitionTableKeys.transaction) {
      return {
        accessorKey: item.key,
        header: () => (
          <div className="flex w-1/3 flex-row">
            <div className="text-center">{item.label}</div>
          </div>
        ),
        cell: ({ row }) => {
          return (
            <div className="w-1/3">
              <Text
                textTransform="capitalize"
                fontWeight={500}
                fontSize={14}
                color={BaseColor.PRIMARY}
              >
                {row.original.type}
              </Text>
              <Text
                textTransform="capitalize"
                fontWeight={500}
                fontSize={12}
                color={BaseColor.PRIMARY_300}
              >
                {row.original.formattedDate}
              </Text>
            </div>
          );
        },
      };
    }
    if (item.key === TuitionTableKeys.amount) {
      return {
        accessorKey: item.key,
        header: () => <div className="text-center">{item.label}</div>,
        cell: ({ row }) => {
          return (
            <Text
              fontSize={14}
              color={row.original.isCredit ? "#009933" : "#D3332D"}
              fontWeight={500}
              textAlign="center"
            >
              {row.original.amount}
            </Text>
          );
        },
      };
    }
    return {
      accessorKey: item.key,
      header: () => <div className="text-end">{item.label}</div>,
      cell: ({ row }) => {
        return (
          <Text
            fontWeight={500}
            fontSize={14}
            color={BaseColor.PRIMARY}
            textAlign="end"
          >
            {row.original.transactionReference}
          </Text>
        );
      },
    };
  });

  if (loading) {
    return (
      <div className="w-full space-y-8 overflow-x-auto rounded-lg bg-white p-6">
        {/* Header Skeleton */}
        <HStack alignItems="center" justifyContent="space-between" mb={6}>
          <Skeleton height="28px" width="200px" />
          <Skeleton height="40px" width="216px" />
        </HStack>

        {/* Desktop Table Skeleton */}
        <div className="hidden lg:block">
          <div className="mb-2 rounded-[8px] bg-[#0A3150] p-4">
            <HStack spacing={4}>
              <Skeleton height="20px" width="33%" />
              <Skeleton height="20px" width="33%" />
              <Skeleton height="20px" width="33%" />
            </HStack>
          </div>
          {[...Array(5)].map((_, i) => (
            <div
              key={i}
              className={cn(
                "mb-2 rounded-lg p-4",
                i % 2 === 0 ? "bg-white" : "bg-[#F0F2F4]",
              )}
            >
              <HStack spacing={4}>
                <Skeleton height="40px" width="33%" />
                <Skeleton height="40px" width="33%" />
                <Skeleton height="40px" width="33%" />
              </HStack>
            </div>
          ))}
        </div>

        {/* Mobile List Skeleton */}
        <div className="block space-y-4 lg:hidden">
          {[...Array(3)].map((_, i) => (
            <VStack
              key={i}
              px="20px"
              py="16px"
              backgroundColor="white"
              alignItems="start"
              gap={2}
              borderRadius="16px"
            >
              <HStack justifyContent="space-between" w="full">
                <Skeleton height="20px" width="100px" />
                <Box>
                  <Skeleton height="16px" width="100px" mb={1} />
                  <Skeleton height="16px" width="150px" />
                </Box>
              </HStack>
              <Box>
                <Skeleton height="20px" width="80px" mb={2} />
                <Skeleton height="40px" width="150px" />
              </Box>
            </VStack>
          ))}
        </div>
      </div>
    );
  }

  if (error) return <div>Error loading transactions: {error}</div>;

  return (
    <div className="w-full space-y-8 overflow-x-auto rounded-lg bg-transparent lg:bg-white lg:p-6">
      <HStack alignItems={"center"} justifyContent={"space-between"}>
        <Text color={BaseColor.PRIMARY} fontWeight={700} fontSize={20}>
          Tuition Fund Transactions
        </Text>
        <DropdownMenu open={openSort} onOpenChange={setOpenSort}>
          <DropdownMenuTrigger ref={triggerRef} className="">
            <button className="flex w-[216px] items-center justify-between gap-x-1 rounded-md border border-[#BDC2C9] px-5 py-3 text-sm font-medium text-[#0A3150]">
              <span className="">Sort by: {sort}</span>
              <svg
                width="10"
                height="7"
                viewBox="0 0 10 7"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className={`transition-all duration-150 ease-linear ${openSort ? "rotate-180" : "rotate-0"}`}
              >
                <path
                  d="M9.16671 1.14076C9.01057 0.985555 8.79936 0.898438 8.57921 0.898438C8.35905 0.898438 8.14784 0.985555 7.99171 1.14076L5.00004 4.09076L2.05004 1.14076C1.8939 0.985555 1.68269 0.898438 1.46254 0.898438C1.24238 0.898438 1.03117 0.985555 0.875039 1.14076C0.796932 1.21823 0.734936 1.3104 0.692629 1.41195C0.650322 1.5135 0.62854 1.62242 0.62854 1.73243C0.62854 1.84244 0.650322 1.95136 0.692629 2.05291C0.734936 2.15446 0.796932 2.24663 0.875039 2.3241L4.40837 5.85743C4.48584 5.93554 4.57801 5.99753 4.67956 6.03984C4.78111 6.08215 4.89003 6.10393 5.00004 6.10393C5.11005 6.10393 5.21897 6.08215 5.32052 6.03984C5.42207 5.99753 5.51424 5.93554 5.59171 5.85743L9.16671 2.3241C9.24481 2.24663 9.30681 2.15446 9.34912 2.05291C9.39142 1.95136 9.41321 1.84244 9.41321 1.73243C9.41321 1.62242 9.39142 1.5135 9.34912 1.41195C9.30681 1.3104 9.24481 1.21823 9.16671 1.14076Z"
                  fill="#0A3150"
                />
              </svg>
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="z-[1] bg-white p-0"
            align="end"
            style={{ width: width }}
          >
            <DropdownMenuItem
              className="px-5 py-2"
              onClick={() => handleSortChange("Most Recent")}
            >
              Most Recent
            </DropdownMenuItem>
            <DropdownMenuItem
              className="px-5 py-2"
              onClick={() => handleSortChange("Highest Amount")}
            >
              Highest Amount
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </HStack>

      {formattedData.length > 0 ? (
        <>
          <div className="block space-y-4 lg:hidden">
            {formattedData.map((transaction, index) => (
              <VStack
                px="20px"
                py="16px"
                backgroundColor="white"
                key={index}
                alignItems="start"
                gap={2}
                borderRadius="16px"
              >
                <HStack
                  justifyContent="space-between"
                  className="w-full"
                  alignItems="start"
                >
                  <Text
                    color={BaseColor.PRIMARY}
                    fontWeight={500}
                    fontSize={14}
                  >
                    {transaction.formattedDate}
                  </Text>
                  <Box>
                    <Text color="#8EA0AF" fontWeight={500} fontSize={12}>
                      Transaction Ref.
                    </Text>
                    <Text
                      color={BaseColor.PRIMARY}
                      fontWeight={500}
                      fontSize={14}
                    >
                      {transaction.transactionReference}
                    </Text>
                  </Box>
                </HStack>
                <Box>
                  <Text
                    color={BaseColor.PRIMARY}
                    fontWeight={600}
                    fontSize={14}
                  >
                    {transaction.type}
                  </Text>
                  <Text
                    fontSize={32}
                    color={transaction.isCredit ? "#009933" : "#D3332D"}
                    fontWeight={700}
                  >
                    {transaction.amount}
                  </Text>
                </Box>
              </VStack>
            ))}
          </div>
          <div className="hidden lg:block">
            <CommonTable
              pagination={pagination}
              setPagination={setPagination}
              dataTable={formattedData}
              columnsTable={columns}
              totalNumOfPages={walletData?.totalPages || 1}
              isLoading={loading}
            />
          </div>
        </>
      ) : (
        <NoTransactions />
      )}
    </div>
  );
};

export default TuitionTable;
