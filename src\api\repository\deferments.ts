/* eslint-disable @typescript-eslint/no-explicit-any */
import { MODULE_ROUTE, Routes } from "../config/routes";
import { baseApi } from "../config/api";

export const createDeferment = async (payload: any) => {
  try {
    const response = await baseApi.post(
      Routes[MODULE_ROUTE.DEFERMENT].CREATE,
      payload,
    );
    return response.data;
  } catch (error) {
    console.error("Error creating deferment request:", error);
    throw error;
  }
};
