import React, { FC } from "react";
import { Box } from "@chakra-ui/react";
import { DashboardCourseWrapper } from "./DashboardCourseWrapper";
import { useRouter } from "next/navigation";
import { ISemestersProps } from "./Semesters.d";
import { beautify, undefinedToString } from "@/lib/utils/string";
import { Semester } from "src/api/repository/student";

export const DashboardSemesterWrapper: FC<ISemestersProps> = ({
  semesters = [],
  otherDetails,
  activeLevel,
}) => {
  const router = useRouter();

  const handleSemesterEnrolment = () => {
    router.push(`/enrollment`);
  };

  const currentDate = new Date();
  let activeSemester: Semester | undefined = undefined;

  for (let i = semesters?.length - 1; i >= 0; i--) {
    const currentSemester = semesters[i];
    if (
      currentSemester &&
      new Date(currentSemester.semester_start_date || "") <= currentDate
    ) {
      activeSemester = currentSemester;
      break;
    }
  }
  if (!activeSemester) {
    return <></>;
  }

  return (
    <Box>
      <DashboardCourseWrapper
        goToClass
        activeLevel={activeLevel}
        courses={activeSemester?.courses || []}
        otherDetails={otherDetails}
        semester={beautify(activeSemester?.semester_type)}
        semesterStartDate={undefinedToString(
          activeSemester?.semester_start_date,
        )}
        handleSemesterEnrolment={handleSemesterEnrolment}
        semesterDetails={activeSemester}
      />
    </Box>
  );
};
