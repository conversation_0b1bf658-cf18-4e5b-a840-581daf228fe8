export type TabList = {
  id: number;
  name: string;
};

export type PersonalInformation = {
  title: string;
  lastName: string;
  firstName: string;
  otherNames: string;
  dob: string;
  gender: string;
  nationality: string;
  nationalId: string;
  employmentStatus: string;
  maritalStatus: string;
};

export type DocumentList = {
  title?: string;
  type: string;
  document_name: string;
  date: string;
};
export type DocumentUploads = {
  document_link: unknown;
  title?: string;
  file_name: string;
  description: string;
};
export type PersonalInformationProps = {
  handleEventFromChild: (item: number) => void;
  currentTab: number;
};

export interface IBasicDetails {
  user_id?: string;
  title: string;
  email: string;
  first_name: string;
  last_name: string;
  other_name: string;
  dob: string;
  gender: string;
  marital_status: string;
  nationality: string;
  home_address: string;
  city: string;
  state: string;
  country: string;
  phone_number: string;
  display_picture?: File;
  lga: string;
  next_of_kin: string;
  next_of_kin_phone_number: string;
  national_id: string;
  employment_status?: string;
  company_name?: string;
  industry?: string;
}

export interface IBasicDetailsContainerProps {
  handleProfileUpdate: (value: IBasicDetails) => Promise<void>;
}
