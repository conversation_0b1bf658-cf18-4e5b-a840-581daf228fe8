import React from "react";
import { Box, Text } from "@chakra-ui/react";
import { BaseColor } from "@/constants/colors";

const NoTransactions = () => {
  return (
    <Box
      backgroundColor="#F9FAFB"
      minHeight="60vh"
      display="flex"
      flexDirection="column"
      gap={3}
      alignItems="center"
      justifyContent="center"
      px="5px"
    >
      <svg
        width="146"
        height="153"
        viewBox="0 0 146 153"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <circle cx="68.6634" cy="79.5609" r="68.6634" fill="#E7EAEE" />
        <g filter="url(#filter0_d_5144_17529)">
          <rect
            x="26.0293"
            y="10"
            width="85.2683"
            height="109.502"
            rx="5.4351"
            fill="white"
          />
          <rect
            x="35.0049"
            y="22.5664"
            width="45.801"
            height="6.19825"
            rx="3.09913"
            fill="#EAEAEB"
          />
          <rect
            x="35.0049"
            y="33.9297"
            width="68.2143"
            height="6.19825"
            rx="2.68119"
            fill="#EAEAEB"
          />
          <rect
            x="35.0049"
            y="56.2842"
            width="45.801"
            height="6.19825"
            rx="3.09913"
            fill="#EAEAEB"
          />
          <rect
            x="35.0049"
            y="67.6475"
            width="68.2143"
            height="6.19825"
            rx="2.68119"
            fill="#EAEAEB"
          />
          <rect
            x="35.0049"
            y="90.002"
            width="45.801"
            height="6.19825"
            rx="3.09913"
            fill="#EAEAEB"
          />
          <rect
            x="35.0049"
            y="101.365"
            width="68.2143"
            height="6.19825"
            rx="2.68119"
            fill="#EAEAEB"
          />
        </g>
        <g filter="url(#filter1_d_5144_17529)">
          <rect
            x="81.4536"
            y="88.0879"
            width="56.5463"
            height="56.5463"
            rx="28.2732"
            fill="#3B5A73"
          />
          <path
            d="M112.86 116.81L118.504 111.166C119.309 110.361 119.309 109.055 118.504 108.25C117.699 107.445 116.393 107.445 115.588 108.25L109.944 113.894L104.3 108.25C103.495 107.445 102.19 107.445 101.385 108.25C100.579 109.055 100.579 110.361 101.385 111.166L107.029 116.81L101.385 122.454C100.579 123.259 100.579 124.564 101.385 125.369C101.787 125.772 102.315 125.973 102.842 125.973C103.37 125.973 103.898 125.772 104.3 125.369L109.944 119.725L115.588 125.369C115.991 125.772 116.518 125.973 117.046 125.973C117.574 125.973 118.101 125.772 118.504 125.369C119.309 124.564 119.309 123.259 118.504 122.454L112.86 116.81Z"
            fill="white"
          />
        </g>
        <defs>
          <filter
            id="filter0_d_5144_17529"
            x="15.1591"
            y="0.488569"
            width="107.008"
            height="131.242"
            filterUnits="userSpaceOnUse"
            color-interpolation-filters="sRGB"
          >
            <feFlood flood-opacity="0" result="BackgroundImageFix" />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dy="1.35878" />
            <feGaussianBlur stdDeviation="5.4351" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0.0392157 0 0 0 0 0.192157 0 0 0 0 0.313726 0 0 0 0.1 0"
            />
            <feBlend
              mode="normal"
              in2="BackgroundImageFix"
              result="effect1_dropShadow_5144_17529"
            />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="effect1_dropShadow_5144_17529"
              result="shape"
            />
          </filter>
          <filter
            id="filter1_d_5144_17529"
            x="74.245"
            y="81.7803"
            width="70.9637"
            height="70.9632"
            filterUnits="userSpaceOnUse"
            color-interpolation-filters="sRGB"
          >
            <feFlood flood-opacity="0" result="BackgroundImageFix" />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dy="0.901083" />
            <feGaussianBlur stdDeviation="3.60433" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0.0392157 0 0 0 0 0.192157 0 0 0 0 0.313726 0 0 0 0.1 0"
            />
            <feBlend
              mode="normal"
              in2="BackgroundImageFix"
              result="effect1_dropShadow_5144_17529"
            />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="effect1_dropShadow_5144_17529"
              result="shape"
            />
          </filter>
        </defs>
      </svg>
      <Text
        color={BaseColor.PRIMARY}
        fontWeight={700}
        fontSize={24}
        textAlign="center"
      >
        No Transaction Record
      </Text>{" "}
      <Text
        color={BaseColor.PRIMARY_400}
        fontWeight={500}
        fontSize={16}
        textAlign="center"
      >
        You can deposit, make payments and withdraw
      </Text>
    </Box>
  );
};

export default NoTransactions;
