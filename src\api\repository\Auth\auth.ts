import { APIResponse } from "src/api/config/api.d";
import { baseApi } from "src/api/config/api";
import { MODULE_ROUTE, Routes } from "src/api/config/routes";

export const requestResendCode = async (
  email: string,
): Promise<APIResponse<unknown>> => {
  try {
    const { data } = await baseApi.post<APIResponse<unknown>>(
      Routes[MODULE_ROUTE.AUTH].RESEND,
      {
        email,
      },
    );
    return data;
  } catch (error) {
    console.error("Error fetching student dashboard:", error);
    throw error;
  }
};

// Method: getCurrentUserAndStudent To be updated
