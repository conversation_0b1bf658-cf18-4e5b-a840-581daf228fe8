"use client";

import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import TuitionFundSetupWrapper from "../TuitionFundSetupWrapper";
import { useTuitionFund } from "../../context/TuitionFundContext";
import InputField from "@/components/commons/InputField/InputField";
import { ArrowRightIcon } from "lucide-react";

const OTPVerificationScreen: React.FC = () => {
  const {
    formData,
    updateFormData,
    otpError,
    handleNext,
    resendOTP,
    isOTPLoading,
    isOTPVerifying,
  } = useTuitionFund();

  return (
    <TuitionFundSetupWrapper>
      <div className="mb-6">
        <InputField
          id="otp"
          type="text"
          label="Verify Your Identity"
          value={formData.otp}
          onChange={(e) => updateFormData("otp", e.target.value)}
          className={`w-full ${otpError ? "border-[#D3332D]" : ""}`}
          placeholder="Enter the OTP sent to your school email"
          isDisabled={isOTPVerifying}
        />
        {otpError && <p className="mt-1 text-sm text-[#D3332D]">{otpError}</p>}
      </div>

      <div className="mb-6 text-right">
        <button
          type="button"
          className="text-sm font-semibold text-[#BB9E7F] underline disabled:opacity-50"
          onClick={resendOTP}
          disabled={isOTPLoading}
        >
          {isOTPLoading ? "Sending..." : "Resend OTP"}
        </button>
      </div>

      <div className="flex justify-end">
        <Button
          variant="primary"
          onClick={handleNext}
          disabled={!formData.otp || isOTPVerifying}
          className="bg-[#0A3150] px-6 text-white"
        >
          {isOTPVerifying ? "Verifying..." : "Next"}
          {!isOTPVerifying && <ArrowRightIcon className="ml-2" />}
        </Button>
      </div>
    </TuitionFundSetupWrapper>
  );
};

export default OTPVerificationScreen;
