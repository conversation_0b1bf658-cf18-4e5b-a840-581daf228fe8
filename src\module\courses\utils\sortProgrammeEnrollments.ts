import { StudentEnrollment } from "src/api/repository/Courses/courses.d";

export const sortProgrammeLevelEnrollments = <T extends { level: string }>(
  programme_enrollments?: T[],
) =>
  (programme_enrollments || []).sort((a, b) => {
    if (a.level < b.level) {
      return -1;
    }
    if (b.level < a.level) {
      return 1;
    }
    return 0;
  });

export const sortEnrollments = (enrollments: StudentEnrollment[]) => {
  const statusOrder = { ENROLLED: 1, COMPLETED: 2, OTHER: 3, DELETED: 4 };
  const getStatusOrder = (status: string) =>
    status == "ENROLLED"
      ? statusOrder.ENROLLED
      : status == "COMETED"
        ? statusOrder.COMPLETED
        : status == "DELETED"
          ? statusOrder.DELETED
          : statusOrder.OTHER;

  return enrollments.sort((a, b) => {
    return getStatusOrder(a.status) - getStatusOrder(b.status);
  });
};
