interface IWithdrawalFormValues {
  bank_name: string;
  account_number: string;
  account_name: string;
  reason: string;
  additional_info?: string;
  termsAccepted: boolean;
  requested_amount?: number;
}

interface IWithdrawalFormProps {
  handleRequestSubmission: (value: IWithdrawalFormValues) => Promise<void>;
}

interface IWithdrawalResponse {
  data: string;
  errors: string;
  message: string;
  status: string;
}
