import { useApplicationDetail } from "@/hooks/useApplication/useApplicationDetail";
import { useCreateEnrollment } from "@/hooks/useEnrollment/useCreateEnrollment";
import { useSemesterList } from "@/hooks/useEnrollment/useSemesterList";
import { undefinedToString } from "@/lib/utils/string";
import { useToast } from "@chakra-ui/react";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import { useAuthStore } from "../../../store/AuthenticationStore/authentication";
import { useNewSession } from "src/module/payments/useNewSession";
import { SEMESTERS } from "src/utils/semesters";
import { IProgrammeIntakeSemesterData } from "src/api/repository/Misc/programmeIntakeSemester.d";

export const useEnrollment = () => {
  const searchParams = useSearchParams();
  const applicationId = searchParams.get("applicationId") || "";

  const router = useRouter();
  const toast = useToast();

  const [courseChecked, setCourseChecked] = useState<string[]>([]);

  const student = useAuthStore((state) => state.student);
  const setEnrollStatus = useAuthStore((state) => state.setEnrollStatus);
  const user = useAuthStore((state) => state.user);

  const { application_details } = student || {};
  const { id: studentId } = user || {};

  const { data: dataApplication, isLoading: isLoadingApplication } =
    useApplicationDetail(
      undefinedToString(applicationId || application_details?.id),
    );

  const {
    activeEnrollment,
    newSessionIsActive,
    isLoadingActiveEnrollment,
    paymentCompletionIsFetching,
    paymentCompletion,
  } = useNewSession();

  const enrollmentData = useMemo(() => {
    const programmeName =
      activeEnrollment?.programme_short_name ||
      dataApplication?.data.programme?.short_name;
    const programmeId =
      activeEnrollment?.programme_id ||
      dataApplication?.data.programme?.programme_id;

    if (newSessionIsActive) {
      return {
        programmeIntakeId: activeEnrollment?.next_programme_intake_id,
        programmeName,
        programmeId,
        level: activeEnrollment?.next_programme_intake_level,
      };
    }

    return {
      programmeIntakeId:
        activeEnrollment?.programme_intake_id ||
        dataApplication?.data.programme_intake_id,
      programmeName,
      programmeId,
      level: activeEnrollment?.level || dataApplication?.data.level,
    };
  }, [dataApplication, activeEnrollment, newSessionIsActive]);

  const { data: dataSemester = [], isLoading: isLoadingSemester } =
    useSemesterList({
      programme_intake_id: enrollmentData.programmeIntakeId || "",
    });

  const { mutateAsync: createEnrollment } = useCreateEnrollment();

  const { activeSemester, semestersId } = useMemo(() => {
    const noOfSemesters = activeEnrollment?.programme_number_of_semesters || 2;
    if (Array.isArray(dataSemester)) {
      const currentDate = new Date();
      let activeSemester: IProgrammeIntakeSemesterData | undefined = undefined;

      for (let i = noOfSemesters - 1; i >= 0; i--) {
        const currentSemester = dataSemester.find(
          (item) => item.semester_type === SEMESTERS[i],
        );
        if (
          currentSemester &&
          new Date(currentSemester.semester_start_date || "") <= currentDate &&
          currentSemester.semester_status === "ACTIVE"
        ) {
          activeSemester = currentSemester;
          break;
        }
      }

      // Set default semester to first semester
      const defaultSemester = dataSemester.find(
        (item) => item.semester_type === SEMESTERS[0],
      );

      activeSemester = activeSemester || defaultSemester;

      return {
        activeSemester: activeSemester?.semester_type || SEMESTERS[0],
        semestersId: activeSemester?.semester_atp_id || "",
      };
    }
    return {
      activeSemester: SEMESTERS[0],
      semestersId: "",
    };
  }, [dataSemester, activeEnrollment?.programme_number_of_semesters]);

  const handleCreateEnrollment = async () => {
    if (courseChecked.length === 0) return;
    try {
      await createEnrollment({
        course_offering_ids: courseChecked,
        programme_id: enrollmentData.programmeId || "",
        semester_atp_id: semestersId,
        programme_intake_id: enrollmentData.programmeIntakeId || "",
        student_id: studentId || "",
        is_moodle: true,
        application_id: undefinedToString(
          applicationId || application_details?.id,
        ),
      });
      toast({
        description: "Enrollment created successfully!",
        status: "success",
      });
      setEnrollStatus("ENROLLED");
      router.push("/dashboard");
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (e: any) {
      // Check if the error has a response with a message
      const errorMessage = e.response
        ? e?.response?.data?.errors[0]
        : "Enrollment could not be created!";
      toast({
        description: errorMessage,
        status: "error",
      });
    }
  };

  useEffect(() => {
    if (
      newSessionIsActive &&
      paymentCompletion?.type !== "payment_completed" &&
      !paymentCompletionIsFetching
    ) {
      toast({
        description: "Payment has not been completed!",
        status: "error",
      });
      router.replace("/dashboard");
    }
  }, [newSessionIsActive, paymentCompletion, paymentCompletionIsFetching]);

  return {
    enrollmentData,
    activeSemester,
    isLoadingApplication,
    isLoadingSemester,
    isLoadingActiveEnrollment,
    semestersId,
    courseChecked,
    setCourseChecked,
    handleCreateEnrollment,
    paymentCompletionIsFetching,
  };
};
