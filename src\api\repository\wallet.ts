/* eslint-disable @typescript-eslint/no-explicit-any */
import { baseApi } from "../config/api";
import { MODULE_ROUTE, Routes } from "../config/routes";
import { APIResponse } from "src/api/config/api.d";
import {
  WalletSetupRequest,
  WalletSetupResponse,
  SendWalletOTPRequest,
  SendWalletOTPResponse,
  VerifyWalletOTPRequest,
  VerifyWalletOTPResponse,
} from "./wallet.d";

export const sendWalletOTP = async (
  payload: SendWalletOTPRequest,
): Promise<APIResponse<SendWalletOTPResponse>> => {
  try {
    const { data } = await baseApi.post<APIResponse<SendWalletOTPResponse>>(
      Routes[MODULE_ROUTE.WALLET].SEND_OTP,
      payload,
    );
    return data;
  } catch (error) {
    console.error("Error sending wallet OTP:", error);
    throw error;
  }
};

export const verifyWalletOTP = async (
  payload: VerifyWalletOTPRequest,
): Promise<APIResponse<VerifyWalletOTPResponse>> => {
  try {
    const { data } = await baseApi.post<APIResponse<VerifyWalletOTPResponse>>(
      Routes[MODULE_ROUTE.WALLET].VERIFY_OTP,
      payload,
    );
    return data;
  } catch (error) {
    console.error("Error verifying wallet OTP:", error);
    throw error;
  }
};

export const setupWallet = async (
  payload: WalletSetupRequest,
): Promise<APIResponse<WalletSetupResponse>> => {
  try {
    const { data } = await baseApi.post<APIResponse<WalletSetupResponse>>(
      Routes[MODULE_ROUTE.WALLET].SETUP,
      payload,
    );
    return data;
  } catch (error) {
    console.error("Error setting up wallet:", error);
    throw error;
  }
};
