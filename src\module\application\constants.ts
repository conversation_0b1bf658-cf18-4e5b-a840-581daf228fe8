import { E_STATUS_APPLICATION } from "@/constants/enums";
import { IAnyObject } from "@/constants/types";

export const WarningStatusApplication: (string | E_STATUS_APPLICATION)[] = [
  E_STATUS_APPLICATION.PENDING_REVIEW,
  E_STATUS_APPLICATION.INCOMPLETE_DOCUMENTS,
  E_STATUS_APPLICATION.INCORRECT_UPLOADS,
  E_STATUS_APPLICATION.MISSING_SUBJECT,
  E_STATUS_APPLICATION.IDENTITY_DISCREPANCIES,
  E_STATUS_APPLICATION.DO_NOT_QUALIFY,
];

export const mapStyleByStatus: { [key: string]: IAnyObject } = {
  [E_STATUS_APPLICATION.APPLICATION_SENT]: {
    background: "#E7EAEE",
    color: "#092D49",
  },
  [E_STATUS_APPLICATION.PENDING_REVIEW]: {
    background: "#F8F5F2",
    color: "#85705A",
  },
  [E_STATUS_APPLICATION.ENROLLMENT_PENDING]: {
    background: "#F8F5F2",
    color: "#85705A",
  },
  [E_STATUS_APPLICATION.APPLICATION_CANCELLED]: {
    background: "#E7EAEE",
    color: "#092D49",
  },
  [E_STATUS_APPLICATION.WAITING_LIST]: {
    background: "#F8F5F2",
    color: "#85705A",
  },
  [E_STATUS_APPLICATION.INCOMPLETE_DOCUMENTS]: {
    background: "#FDEBEA",
    color: "#D3332D",
  },
  [E_STATUS_APPLICATION.INCORRECT_UPLOADS]: {
    background: "#FDEBEA",
    color: "#D3332D",
  },
  [E_STATUS_APPLICATION.MISSING_SUBJECT]: {
    background: "#FDEBEA",
    color: "#D3332D",
  },
  [E_STATUS_APPLICATION.IDENTITY_DISCREPANCIES]: {
    background: "#FDEBEA",
    color: "#D3332D",
  },
  [E_STATUS_APPLICATION.DO_NOT_QUALIFY]: {
    background: "#FDEBEA",
    color: "#D3332D",
  },
  [E_STATUS_APPLICATION.ACCEPTED]: {
    background: "#E5F4EA",
    color: "#00802B",
  },
  [E_STATUS_APPLICATION.ENROLLED]: {
    background: "#E5F4EA",
    color: "#00802B",
  },
  [E_STATUS_APPLICATION.PENDING]: {
    background: "#E7EAEE",
    color: "#092D49",
  },
};

//[APPLICATION_SENT,PENDING_REVIEW,CANCELED,WAITING_LIST,INCOMPLETE_DOCUMENTS,
// INCORRECT_UPLOADS,IDENTITY_DISCREPANCIES,MISSING_SUBJECTS,DO_NOT_QUALIFY,ACCEPTED,ENROLLED,ENROLLMENT_PENDING]
export const mapTextStatusApplication: { [key: string]: string } = {
  [E_STATUS_APPLICATION.ENROLLED]: "Enrolled",
  [E_STATUS_APPLICATION.PENDING]: "Pending",
  [E_STATUS_APPLICATION.WAITING_LIST]: "Waiting list",
  [E_STATUS_APPLICATION.PENDING_REVIEW]: "Pending review",
  [E_STATUS_APPLICATION.ENROLLMENT_PENDING]: "Enrollment pending",
  [E_STATUS_APPLICATION.APPLICATION_CANCELLED]: "Application cancelled",
  [E_STATUS_APPLICATION.APPLICATION_SENT]: "Application sent",
  [E_STATUS_APPLICATION.ACCEPTED]: "Accepted",
  [E_STATUS_APPLICATION.DO_NOT_QUALIFY]: "Do not qualify",
  [E_STATUS_APPLICATION.IDENTITY_DISCREPANCIES]: "Identity discrepancy",
  [E_STATUS_APPLICATION.INCOMPLETE_DOCUMENTS]: "Incomplete document",
  [E_STATUS_APPLICATION.MISSING_SUBJECT]: "Missing subject",
  [E_STATUS_APPLICATION.REJECTED]: "Rejected",
  [E_STATUS_APPLICATION.INCORRECT_UPLOADS]: "Incorrect upload",
};

export const APPLICATION_TYPE: { [key: string]: string } = {
  DIRECT_ENTRY: "Direct Entry",
  "100_LEVEL": "100 Level",
  "200_LEVEL": "200 Level",
  "300_LEVEL": "300 Level",
  "400_LEVEL": "400 Level",
  "INTER-UNIVERSITY_TRANSFER": "Inter University Transfer",
  MBA: "MBA",
  MPA: "MPA",
};
