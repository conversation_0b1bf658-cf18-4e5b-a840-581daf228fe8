"use client";

import { TParamPageCommon } from "@/constants/types";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import LoginPage from "./login/page";

const HomePage = (props: TParamPageCommon) => {
  const router = useRouter();

  useEffect(() => {
    const { checkUserSession } = useAuthStore.getState();
    checkUserSession(router);
  }, []);
  return <LoginPage {...props} />;
};

export default HomePage;
