import { IProgrammeIntakeData } from "src/api/repository/programmeIntake";

const monthOrder = ["january", "may", "september"];
const monthToNumber: Record<string, number> = {
  january: 0,
  may: 4,
  september: 8,
};

const normalize = (str: string) => str.trim().replace(/\s+/g, " ");

const extractMonthYear = (intake: IProgrammeIntakeData) => {
  const normalized = normalize(intake.academic_time_period_name).toLowerCase();
  const parts = normalized.split(" ");

  const monthIndex = parts.findIndex((part) =>
    monthOrder.includes(part.toLowerCase()),
  );

  const month = parts[monthIndex];
  const year = parseInt(parts[monthIndex + 1]);

  return {
    month,
    year,
    raw: intake,
    date: new Date(year, monthToNumber[month]),
  };
};

export const sortAndFilterFutureIntakes = (intakes: IProgrammeIntakeData[]) => {
  const today = new Date();
  const startOfThisMonth = new Date(today.getFullYear(), today.getMonth(), 1);

  return intakes
    .map(extractMonthYear)
    .filter((item) => item.date > startOfThisMonth)
    .sort((a, b) => {
      if (a.year !== b.year) return a.year - b.year;
      return monthOrder.indexOf(a.month) - monthOrder.indexOf(b.month);
    })
    .map(({ raw }) => raw);
};
