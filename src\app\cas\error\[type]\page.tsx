"use client";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useParams } from "next/navigation";

const UnauthorizedServicePage = () => {
  const { type } = useParams<{ type: string }>();

  const errors = {
    unauthorized: {
      title: "Application Not Authorized to Use CAS",
      description:
        "The application you attempted to authenticate to is not authorized to use CAS. This usually indicates that the application is not registered with CAS, or its configuration prevents it from leveraging CAS functionality, or it's malformed and unrecognized by CAS. Contact your CAS administrator to learn how you might register and integrate your application with CAS.",
    },
  };

  return (
    <div className="m-8 flex justify-center">
      <Alert
        variant="destructive"
        className="mt-5 border border-[#E83831] bg-[#FDEBEA] p-5"
      >
        <AlertDescription className="flex flex-col items-start justify-between">
          <div className="flex flex-col justify-between lg:gap-y-5">
            <p className="text-lg font-bold">
              {errors[type as keyof typeof errors]?.title ||
                "CAS access denied"}
            </p>
          </div>
          <div className="mt-5 flex flex-col justify-between lg:mt-0 lg:gap-y-2">
            <p>
              {errors[type as keyof typeof errors]?.description ||
                "You are unable to access CAS please contact your CAS administrator"}
            </p>
          </div>
        </AlertDescription>
      </Alert>
    </div>
  );
};

export default UnauthorizedServicePage;
