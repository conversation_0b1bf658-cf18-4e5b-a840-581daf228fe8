import { NextRequest, NextResponse, userAgent } from "next/server";
import { EVIEW_PORT } from "./constants/enums";

export function middleware(request: NextRequest) {
  const url = request.nextUrl;
  const { device } = userAgent(request);
  const viewport =
    device.type === "mobile" ? EVIEW_PORT.MOBILE : EVIEW_PORT.DESKTOP;
  url.searchParams.set("viewport", viewport);
  return NextResponse.rewrite(url);
}
