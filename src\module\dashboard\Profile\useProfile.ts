import { extractAxiosError } from "@/lib/utils/helpers";
import { useToast } from "@chakra-ui/react";
import axios from "axios";
import { APIResponse } from "src/api/config/api.d";
import { baseApi } from "src/api/config/api";
import { MODULE_ROUTE, Routes } from "src/api/config/routes";
import {
  useAuthStore,
  getCurrentUserAndStudent,
} from "src/store/AuthenticationStore/authentication";
import { IBasicDetails } from "./Profile";
import React from "react";
import { useUpload } from "@/hooks/useUpload/useUpload";

export const useProfile = () => {
  const toast = useToast();
  const student = useAuthStore((state) => state.student);
  const user = useAuthStore((state) => state.user);
  const [displayPictureLoading, setDisplayPictureLoading] =
    React.useState(false);

  const { handleUpload } = useUpload();

  const handleProfileUpdate = async ({
    title,
    email,
    first_name,
    last_name,
    other_name,
    dob,
    gender,
    marital_status,
    nationality,
    home_address,
    city,
    state,
    country,
    phone_number,
    company_name,
    display_picture,
    industry,
    lga,
    next_of_kin,
    next_of_kin_phone_number,
    national_id,
    employment_status,
  }: Partial<IBasicDetails>) => {
    let uploaded_file: string | undefined = undefined;
    if (display_picture) {
      uploaded_file = await handleDisplayPictureUpdate(display_picture);
    }
    try {
      await baseApi.patch<APIResponse<string>>(
        Routes[MODULE_ROUTE.STUDENT].UPDATE_PROFILE,
        {
          title,
          email,
          first_name,
          last_name,
          other_name,
          company_name,
          industry,
          dob,
          gender,
          marital_status,
          nationality,
          home_address,
          city,
          state,
          country,
          phone_number,
          display_picture: uploaded_file,
          lga,
          next_of_kin,
          next_of_kin_phone_number,
          national_id,
          employment_status,
          user_id: user?.id,
        },
      );
      const currentUserAndStudent = await getCurrentUserAndStudent();
      useAuthStore.setState((prev) => ({ ...prev, ...currentUserAndStudent }));
      toast({
        description: "Profile updated successfully",
        status: "success",
      });
    } catch (error) {
      if (axios.isAxiosError(error)) {
        toast({
          description: extractAxiosError(error),
          status: "error",
        });
      }
    }
    setDisplayPictureLoading(false);
  };

  const handleDisplayPictureUpdate = async (display_picture: File) => {
    setDisplayPictureLoading(true);
    const uploaded_file = await handleUpload(display_picture, "true");
    return uploaded_file;
  };

  return {
    student,
    user,
    handleProfileUpdate,
    displayPictureLoading,
  };
};
