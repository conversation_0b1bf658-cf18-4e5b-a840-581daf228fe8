import get from "lodash/get";
import { baseApi } from "../config/api";
import { MODULE_ROUTE, Routes } from "../config/routes";
import { APIResponse, QueryDataPaging } from "../config/api.d";
import { IAnyObject } from "@/constants/types";

export interface IProgrammeIntakeParam extends QueryDataPaging {
  search: string;
  atp_id?: string;
  programme_id?: string;
  programme_level_id?: string;
  status?: string;
}
export interface INextProgrammeIntakeParam {
  current_programme_intake_id: string;
}

export interface IProgrammeIntakeMetadata {
  modified: string;
  modified_by: string;
  no_of_enrollment: number;
  no_of_application: number;
  intake: string;
  level: string;
}

export interface IProgrammeIntakeData {
  academic_time_period_end_date: string;
  academic_time_period_id: string;
  academic_time_period_name: string;
  academic_time_period_start_date: string;
  academic_time_period_status: string;
  academic_time_period_type: string;
  bundle_id: string;
  bundle_name: string;
  created_at: string;
  faculty_id: string;
  faculty_name: string;
  grade_scale_id: string;
  grade_scale_name: string;
  level_name: string;
  duration: string;
  duration_unit: string;
  maximum_number_of_student: number;
  minimum_number_of_student: number;
  product_id: string;
  product_name: string;
  programme_code: string;
  programme_id: string;
  programme_intake_id: string;
  programme_intake_status: string;
  programme_level_id: string;
  programme_name: string;
  programme_type: string;
  required_application: boolean;
  required_manual_approval: boolean;
  restrict_course_offering: boolean;
  result_template_id: string;
  result_template_name: string;
  updated_at: string;
  meta_data: IProgrammeIntakeMetadata;
  course_offerings: IAnyObject[];
  enrolment_status?: string;
}

export async function getNextProgrammeIntakeList({
  current_programme_intake_id,
}: INextProgrammeIntakeParam): Promise<APIResponse<IProgrammeIntakeData[]>> {
  try {
    const response = await baseApi.get(
      Routes[MODULE_ROUTE.PROGRAMME_INTAKE].NEXT,
      {
        params: {
          current_programme_intake_id,
        },
      },
    );
    return response.data;
  } catch (error) {
    console.error("Error get list programmes intake:", error);
    throw get(error, "response.data");
  }
}

export const getProgrammeIntakByLevel = async ({
  programmeLevelId,
}: {
  programmeLevelId: string;
}): Promise<APIResponse<IProgrammeIntakeData[]>> => {
  try {
    const { data } = await baseApi.get<APIResponse<IProgrammeIntakeData[]>>(
      Routes[MODULE_ROUTE.MISC].INTAKE_BY_LEVEL,
      {
        params: {
          programme_level_id: programmeLevelId,
        },
      },
    );
    return data;
  } catch (error) {
    console.error("Error getting program intakes:", error);
    throw error;
  }
};
