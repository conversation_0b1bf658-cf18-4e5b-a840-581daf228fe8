"use client";
import { FormikBag, withFormik } from "formik";
import validator from "validator";
import ForgotPasswordForm from "./ForgotPasswordForm";

import {
  IForgotPasswordContainerProps,
  IForgotPasswordValue,
} from "./ForgotPassword.d";
import { checkValueError, validateRequired } from "@/lib/utils/validation";
import { IValidations } from "@/lib/utils/validation.d";

const validateFields: IValidations<IForgotPasswordValue> = {
  email: [
    {
      validator: validateRequired,
      code: "This field is required",
    },
    {
      validator: validator.isEmail,
      code: "Email is invalid",
    },
  ],
};

export const onSubmit = async (
  values: IForgotPasswordValue,
  {
    setErrors,
    props,
    setSubmitting,
  }: FormikBag<IForgotPasswordContainerProps, IForgotPasswordValue>,
) => {
  setSubmitting(true);
  try {
    await props.handleInitiateReset(values);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (e: any) {
    setErrors(e);
  }
};

const ForgotPasswordContainer = withFormik<
  IForgotPasswordContainerProps,
  IForgotPasswordValue
>({
  mapPropsToValues: () => {
    return { email: "" };
  },
  validate: checkValueError(validateFields),
  handleSubmit: onSubmit,
  validateOnChange: true,
})(ForgotPasswordForm);

export default ForgotPasswordContainer;
