/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useRef } from "react";
import { Image, Text, Box, Divider, Spinner } from "@chakra-ui/react";
import { BaseColor } from "@/constants/colors";
import { IBasicDetails, TabList } from "../Profile";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import { undefinedToString } from "@/lib/utils/string";

const PersonalInformation = ({
  handleEventFromChild,
  currentTab,
  handleProfileUpdate,
  loading,
}: {
  handleProfileUpdate: (detail: Partial<IBasicDetails>) => Promise<void>;
  currentTab: number;
  handleEventFromChild: (item: number) => void;
  loading: boolean;
}) => {
  const user = useAuthStore((state) => state.user);
  const student = useAuthStore((state) => state.student);
  const inputRef = useRef<HTMLInputElement>(null);

  const tabs: TabList[] = [
    {
      id: 1,
      name: "Basic Details",
    },
    {
      id: 2,
      name: "Education",
    },
    {
      id: 3,
      name: "Documents",
    },
  ];

  const activateTab = (id: number) => {
    handleEventFromChild(id);
  };

  return (
    <div className="h-[724px]">
      <input
        type="file"
        accept="image/*"
        ref={inputRef}
        onChange={(e) =>
          handleProfileUpdate({ display_picture: e.target.files?.[0] })
        }
        style={{ display: "none" }}
      />
      <Box
        position="relative"
        display="inline-block"
        cursor="pointer"
        rounded={15}
        overflow="hidden"
        onClick={() => inputRef.current?.click()}
        _hover={{
          "&>div": { display: "flex" },
        }}
        __css={{
          "&>div": { display: loading ? "flex" : undefined },
        }}
        width="100%"
      >
        <Image
          src={
            student?.personal_details?.display_picture || "/images/avatar.png"
          }
          alt="person_image"
          fallbackSrc="/images/avatar.png"
        />
        <Box
          position="absolute"
          bottom={0}
          right={0}
          bg="gray.800"
          color="white"
          display="none"
          alignItems="center"
          justifyContent="center"
          opacity={0.8}
          width="100%"
          height="100%"
        >
          <Text as="span" fontSize={60}>
            {loading ? (
              <Spinner size="md" />
            ) : (
              <i className="uil-camera-change"></i>
            )}
          </Text>
        </Box>
      </Box>
      <Text
        as={"h1"}
        color={BaseColor.PRIMARY}
        fontSize={"24px"}
        fontWeight={700}
        paddingTop={"24px"}
      >
        {user?.biography?.first_name} {user?.biography?.last_name}
      </Text>
      <Text fontSize={"18px"} color={BaseColor.PRIMARY} fontWeight={500}>
        {undefinedToString(student?.student_profile?.matric_no, "-")}
      </Text>
      <Text fontSize={"18px"} color={BaseColor.PRIMARY} fontWeight={500}>
        {undefinedToString(student?.student_profile?.student_id, "-")}
      </Text>
      <Box
        display={"flex"}
        alignContent={"center"}
        gap={"5px"}
        paddingTop={"16px"}
      >
        <Image src="/images/icons/email.svg" alt="email_img"></Image>
        <Text color={BaseColor.PRIMARY} fontWeight={500}>
          {undefinedToString(user?.contact_information.email, "-")}
        </Text>
      </Box>
      <Box
        display={"flex"}
        alignContent={"center"}
        gap={"5px"}
        paddingTop={"16px"}
      >
        <Image src="/images/icons/phone.svg" alt="phone_img"></Image>
        <Text color={BaseColor.PRIMARY} fontWeight={500}>
          {undefinedToString(user?.contact_information.phone_number, "-")}
        </Text>
      </Box>
      <Divider paddingY={"18px"} />
      <Box paddingY={"24px"}>
        {tabs &&
          tabs.map((tab) => (
            <Text
              key={tab.id}
              paddingY={"10px"}
              cursor={"pointer"}
              background={currentTab === tab.id ? "#E7EAEE" : "transparent"}
              onClick={() => activateTab(tab.id)}
              paddingX={"20px"}
              fontSize={"14px"}
              borderRadius={"8px"}
              fontWeight={600}
              color={
                currentTab === tab.id
                  ? BaseColor.PRIMARY
                  : BaseColor.PRIMARY_300
              }
            >
              {tab.name}
            </Text>
          ))}
      </Box>
    </div>
  );
};

export default PersonalInformation;
