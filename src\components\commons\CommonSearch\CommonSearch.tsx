/* eslint-disable react-hooks/exhaustive-deps */
import { FC, useState, useEffect, ChangeEvent } from "react";
import { Input } from "@/components/ui/input";
import { CommonSearchProps } from "./CommonSearch.d";
import { TIME } from "@/constants/commons";
import { cn } from "@/lib/utils";
import { Search } from "lucide-react";

const CommonSearch: FC<CommonSearchProps> = ({
  onSearch,
  timeDebounced = TIME.DEFAULT_DEBOUNCE_INPUT,
  placeholder = "Search...",
  className,
  maxLength,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");

  const handleSearch = (e: ChangeEvent<HTMLInputElement>) => {
    const keyWord = e?.target.value;
    setSearchTerm(keyWord);
  };

  // Update the debounced search term after a delay
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, timeDebounced); // 'timeDebounced' ms debounce time

    // Cleanup the timeout if searchTerm changes (before the 'timeDebounced' ms timeout finishes)
    return () => {
      clearTimeout(handler);
    };
  }, [searchTerm]);

  // Effect to handle the search logic whenever debouncedSearchTerm changes
  useEffect(() => {
    onSearch && onSearch(debouncedSearchTerm);
  }, [debouncedSearchTerm]);

  return (
    <div className="relative">
      <span className="pointer-events-none absolute inset-y-0 left-3 flex items-center">
        <Search className="h-5 w-5 text-gray-400" />
      </span>
      <Input
        className={cn(className, "pl-10")}
        type="search"
        placeholder={placeholder}
        value={searchTerm}
        onChange={handleSearch}
        {...(maxLength && { maxLength })}
      />
    </div>
  );
};

export default CommonSearch;
