"use client";

import React, { FC } from "react";

import { Box, Flex, Text } from "@chakra-ui/react";
import Table from "@/components/commons/Table/Table";
import { ITableResultProps } from "./TableResult.d";

const TableResult: FC<ITableResultProps> = ({
  title,
  data,
  totalCredits,
  gpa,
  cgpa,
}) => {
  const titleList = [
    "Course Code",
    "Course Title",
    "Unit",
    "Score",
    "Grade",
    "Grade Point",
  ];

  const pathList = [
    "course_code",
    "course_name",
    "credit_unit",
    "score",
    "symbol",
    "grade_point",
  ];
  const updatedData = data?.map((item) => ({
    ...item,
    score: item.status.toLowerCase() === "enrolled" ? "-" : item.score,
    grade_point:
      item.status.toLowerCase() === "enrolled" ? "-" : item.grade_point,
  }));
  return (
    <Box mt="32px" p="23px" borderRadius="16px" background="white">
      <Text
        mb="24px"
        fontWeight="bold"
        fontSize="18px"
        color="#0A3150"
        textTransform="capitalize"
      >
        {title}
      </Text>
      <Table
        classHeader="table-header-default"
        classFooter="table-footer-default"
        variant={"unstyled"}
        titleList={titleList}
        data={updatedData}
        pathList={pathList}
        footer={
          <Flex
            flexDir={{ md: "row", base: "column" }}
            justifyContent={{ md: "flex-end", base: "flex-start" }}
            gap={{ md: "48px", base: "16px" }}
            background="#E7EAEE"
            pr="48px"
          >
            <Flex gap="4px">
              <Text fontSize="16px" fontWeight="bold" color="#0A3150">
                TOTAL CREDITS:{" "}
              </Text>
              <Text fontSize="16px" fontWeight="bold" color="#0A3150">
                {totalCredits === 0 ? "-" : totalCredits}
              </Text>
            </Flex>
            <Flex gap="4px">
              <Text fontSize="16px" fontWeight="bold" color="#0A3150">
                GPA:{" "}
              </Text>
              <Text fontSize="16px" fontWeight="bold" color="#0A3150">
                {totalCredits === 0 ? "-" : gpa.toFixed(2)}
              </Text>
            </Flex>
            <Flex gap="4px">
              <Text fontSize="16px" fontWeight="bold" color="#0A3150">
                TOTAL GRADE POINTS:{" "}
              </Text>
              <Text fontSize="16px" fontWeight="bold" color="#0A3150">
                {totalCredits === 0 ? "-" : cgpa}
              </Text>
            </Flex>
          </Flex>
        }
      />
    </Box>
  );
};

export default TableResult;
