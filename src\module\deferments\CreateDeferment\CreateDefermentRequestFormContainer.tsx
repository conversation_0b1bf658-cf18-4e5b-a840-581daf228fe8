/* eslint-disable @typescript-eslint/no-explicit-any */

import CreateDefermentRequestForm from "src/module/deferments/CreateDeferment/CreateDefermentRequestForm";
import { checkValueError, validateRequired } from "src/lib/utils/validation";
import { IValidations } from "src/lib/utils/validation.d";
import { FormikBag, withFormik } from "formik";
import {
  ICreateDeferment,
  ICreateDefermentContainerProps,
} from "src/module/deferments/CreateDeferment/CreateDefermentForm";

const validateFields: IValidations<any> = {
  advisor_id: [
    {
      validator: validateRequired,
      code: "Success Advisor is required",
    },
  ],
  deferment_type: [
    {
      validator: validateRequired,
      code: "Deferment Type is required",
    },
  ],
  reason: [
    {
      validator: validateRequired,
      code: "Reason is required",
    },
  ],
};

const onSubmit = async (
  values: any,
  {
    setErrors,
    props,
    setSubmitting,
  }: FormikBag<ICreateDefermentContainerProps, ICreateDeferment>,
) => {
  setSubmitting(true);
  try {
    const payload = {
      academic_time_period_id: values.academic_time_period_id,
      advisor_id: values.advisor_id,
      courses: values.courses,
      deferment_type: values.deferment_type,
      documents: values.documents,
      end_date: values.end_date,
      exam_center: values.exam_center,
      location: values.location,
      programme_intake: values.programme_intake,
      reason: values.reason,
      start_date: values.start_date,
      user_id: values.user_id,
    };

    await props.handleCreateDeferment({ ...payload });
  } catch (e: any) {
    setErrors(e);
  }
};

const CreateDefermentRequestFormContainer = withFormik<
  ICreateDefermentContainerProps,
  ICreateDeferment
>({
  mapPropsToValues: (props) => {
    return {
      academic_time_period_id:
        props.initialValues?.academic_time_period_id || "",
      advisor_id: props.initialValues?.advisor_id || "",
      courses: props.initialValues?.courses || [],
      deferment_type: props.initialValues?.deferment_type || "",
      documents: props.initialValues?.documents || [],
      end_date: props.initialValues?.end_date || "",
      exam_center: props.initialValues?.exam_center || "",
      location: props.initialValues?.location || "",
      programme_intake: props.initialValues?.programme_intake || "",
      reason: props.initialValues?.reason || "",
      start_date: props.initialValues?.start_date || "",
      user_id: props.initialValues?.user_id || "",
    };
  },
  validate: checkValueError(validateFields),
  handleSubmit: onSubmit,
  validateOnChange: true,
  enableReinitialize: true,
})(CreateDefermentRequestForm);

export default CreateDefermentRequestFormContainer;
