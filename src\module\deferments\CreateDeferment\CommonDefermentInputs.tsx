import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import { InputError } from "@/components/commons/InputField/InputError";
import { useActiveEnrollment } from "@/hooks/useEnrollment/useActiveEnrollment";
import { useStudentDashboard } from "@/hooks/useStudent/useStudentDashboard";
import {
  Box,
  Checkbox,
  FormControl,
  Input,
  Stack,
  Text,
  Textarea,
} from "@chakra-ui/react";
import { Upload } from "lucide-react";
import React, { useEffect, useMemo, useState } from "react";
import { ICreateDefermentForm } from "src/module/deferments/CreateDeferment/CreateDefermentForm";
import { useNextProgrammeIntakesByLevelId } from "./hooks/useNextProgrammeIntakesByLevelId";
import CommonLoading from "@/components/commons/CommonLoading/CommonLoading";

const CommonDefermentInputs = ({
  values,
  handleChange,
  handleBlur,
  touched,
  errors,
  setFieldValue,
  isSubmitting,
  isSubmitEnabled,
}: ICreateDefermentForm) => {
  const { data } = useStudentDashboard();
  const { data: activeEnrollment } = useActiveEnrollment();
  const { intakes, isFetching } = useNextProgrammeIntakesByLevelId(
    activeEnrollment?.programme_level_id,
  );

  const [selectedCohort, setSelectedCohort] = useState("");
  const cohorts = useMemo(() => {
    return (
      data?.programme_level_enrollment.map((cohort) => {
        return {
          label: cohort.academic_time_period_name,
          value: cohort.programme_intake_id,
        };
      }) || []
    );
  }, [data]);

  const [acknowledged, setAcknowledged] = useState(false);
  const [files, setFiles] = useState<File[]>([]);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      setFiles((prevFiles) => [...prevFiles, ...newFiles].slice(0, 5));
    }
  };

  useEffect(() => {
    setFieldValue("documents", files);
  }, [files]);

  const removeFile = (index: number) => {
    setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
  };
  const handleRadioChange = (value: string) => {
    setSelectedCohort(value);
    handleChange({ target: { name: "programme_intake", value } });
  };
  return (
    <>
      <FormControl mt={6}>
        <Text marginBottom={4} color="#0A3150" fontWeight={600} fontSize={14}>
          Select the cohort to which you would like to defer to
          <span className="text-red-500">*</span>
        </Text>
        <Stack direction="column" spacing={6}>
          {intakes?.map((intake) => (
            <div
              key={intake.programme_intake_id}
              className="flex items-center gap-3"
            >
              <input
                type="radio"
                id={intake.programme_intake_id}
                name="programme_intake"
                value={intake.programme_intake_id}
                checked={values.programme_intake == intake.programme_intake_id}
                onChange={() => handleRadioChange(intake.programme_intake_id)}
                className="text-[#0A3150]"
              />
              <label
                htmlFor={intake.programme_intake_id}
                className="cursor-pointer select-none font-medium"
              >
                {intake.meta_data.intake}
              </label>
            </div>
          ))}
          {intakes.length == 0 && !isFetching && (
            <Text marginBottom={2} color="#0A3150" fontSize={14}>
              No future intakes found
            </Text>
          )}
          {isFetching && <CommonLoading size="small" />}
        </Stack>
      </FormControl>

      <FormControl mt={6}>
        <Text
          marginBottom={2}
          color="#0A31
            50"
          fontWeight={600}
          fontSize={14}
        >
          Please state your reason(s) below
          <span className="text-red-500">*</span>
        </Text>
        <Textarea
          value={values.reason}
          name="reason"
          size="sm"
          resize="vertical"
          onChange={handleChange}
          onBlur={handleBlur}
          rounded={8}
          required
        />
        <InputError error={errors.reason} touched={touched.reason} />
      </FormControl>

      <FormControl mt={8}>
        <Text marginBottom={2} color="#0A3150" fontWeight={600} fontSize={14}>
          Any supporting document(s)? Please attach here
        </Text>
        <p className="mb-4 text-xs text-[#5B758A]">
          Upload up to 5 supported files: PDF, document or image Max 10MB per
          file
        </p>

        <Box mb={4}>
          <label>
            <Input
              type="file"
              onChange={handleFileChange}
              multiple
              accept=".pdf,.doc,.docx,.png,.jpg,.jpeg"
              display="none"
            />
            <Box
              as="span"
              display="inline-flex"
              alignItems="center"
              px={4}
              py={2}
              borderWidth={1}
              borderRadius="md"
              backgroundColor="white"
              cursor="pointer"
              _hover={{ bg: "gray.50" }}
            >
              <Upload className="mr-2" size={16} />
              <span className="text-sm">Add file</span>
            </Box>
          </label>
        </Box>

        {files.length > 0 && (
          <Stack spacing={2}>
            {files.map((file, index) => (
              <Box
                key={index}
                p={2}
                border="1px solid"
                borderColor="gray.200"
                borderRadius="md"
                display="flex"
                justifyContent="space-between"
                alignItems="center"
              >
                <Text fontSize="sm">{file.name}</Text>
                <Box display="flex" alignItems="center">
                  <Text fontSize="xs" color="gray.500" mr={3}>
                    {(file.size / (1024 * 1024)).toFixed(2)} MB
                  </Text>
                  <Text
                    as="span"
                    color="red.500"
                    cursor="pointer"
                    fontWeight="bold"
                    onClick={() => removeFile(index)}
                  >
                    ×
                  </Text>
                </Box>
              </Box>
            ))}
          </Stack>
        )}
      </FormControl>

      <Box mt={6}>
        <Text marginBottom={4} color="#0A3150" fontWeight={600} fontSize={14}>
          I have checked that all the information given above is true to the
          best of my knowledge.<span className="text-red-500">*</span>
        </Text>
        <Checkbox
          isChecked={acknowledged}
          onChange={(e) => setAcknowledged(e.target.checked)}
          size="md"
          rounded={8}
        >
          <Text fontSize="sm">Yes</Text>
        </Checkbox>
      </Box>

      <div className="flex justify-end">
        <ButtonCTA
          className="w-36"
          isDisabled={!(acknowledged && isSubmitEnabled)}
          isLoading={isSubmitting}
          type="submit"
        >
          Submit Request
        </ButtonCTA>
      </div>
    </>
  );
};

export default CommonDefermentInputs;
