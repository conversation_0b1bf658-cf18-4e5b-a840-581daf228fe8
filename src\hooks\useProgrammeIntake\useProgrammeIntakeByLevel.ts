import { undefinedToString } from "@/lib/utils/string";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { getProgrammeIntakByLevel } from "../../api/repository/programmeIntake";

export const useProgrammeIntakeByLevel = ({
  programmeLevelId,
}: {
  programmeLevelId?: string;
}) => {
  const query = useQuery({
    enabled: !!programmeLevelId,
    queryKey: ["getProgrammeIntakByLevel", { programmeLevelId }],
    queryFn: () =>
      getProgrammeIntakByLevel({
        programmeLevelId: undefinedToString(programmeLevelId),
      }),
    retry: false,
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
    select: (response) => {
      const { data } = response;
      return {
        data,
      };
    },
  });

  return { ...query, data: (programmeLevelId && query.data?.data) || [] };
};
