"use client";

import { Flex } from "@chakra-ui/react";

import { EVIEW_PORT } from "@/constants/enums";
import { TParamPageCommon } from "@/constants/types";
import ResetPasswordContainer from "../ResetPassword/ResetPasswordContainer";
import RightImageBlock from "../components/RightImageBlock";
import { useResetPassword } from "./useResetPassword";

const ResetPasswordWrapper = ({ searchParams }: TParamPageCommon) => {
  const { handleReset } = useResetPassword();
  return (
    <Flex alignItems="center" gap={{ xl: "172px", md: "40px" }}>
      <ResetPasswordContainer handleReset={handleReset} />
      {searchParams?.viewport !== EVIEW_PORT.MOBILE && <RightImageBlock />}
    </Flex>
  );
};

export default ResetPasswordWrapper;
