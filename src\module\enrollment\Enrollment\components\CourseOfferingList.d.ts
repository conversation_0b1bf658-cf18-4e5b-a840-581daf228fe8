export interface ICourseOfferingListParams {
  programme_id: string;
  programme_intake_id: string;
  atp_id: string;
  level: string;
  courseChecked: string[];
  setCourseChecked: (value: string[]) => void;
  loading?: boolean;
  semester: string;
}

export interface ICourseItem {
  course_code: string;
  course_id: string;
  course_name: string;
  course_offering_id: string;
  credit_unit: number;
}

export interface IGroupCourseItem {
  courses: ICourseItem[];
  group_type: string;
  group_name: string;
}
