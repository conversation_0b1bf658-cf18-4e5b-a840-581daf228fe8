import { isProduction } from "src/utils/envUtils";
import { SidebarMenuItemProps } from "./SideBar.d";
import { EIconName } from "@/components/commons/Icons/Icon.enums";

export const unEnrolledMenus: SidebarMenuItemProps[] = [
  {
    title: "Dashboard",
    to: "/dashboard",
    icon: EIconName.DASHBOARD,
    activeIcon: EIconName.DASHBOARDACTIVE,
  },
];

export const enrolledMenus: SidebarMenuItemProps[] = [
  {
    title: "Dashboard",
    to: "/dashboard",
    icon: EIconName.DASHBOARD,
    activeIcon: EIconName.DASHBOARDACTIVE,
  },
  {
    title: "Courses",
    to: "/courses",
    icon: EIconName.COURSES,
    activeIcon: EIconName.COURSESACTIVE,
  },
  {
    title: "Result",
    to: "/result",
    icon: EIconName.RESULT,
    activeIcon: EIconName.RESULTACTIVE,
  },
  {
    title: "Payment",
    to: "/payment",
    icon: EIconName.PAYMENT,
    activeIcon: EIconName.PAYMENTACTIVE,
  },
  {
    title: "Deferments",
    to: "/deferments",
    icon: EIconName.DEFERMENT,
    activeIcon: EIconName.DEFERMENT,
  },
  ...(!isProduction()
    ? [
        {
          title: "Tuition Fund",
          to: "/tuition-fund",
          icon: EIconName.TUITION_FUND,
          activeIcon: EIconName.TUITION_FUND_ACTIVE,
        },
      ]
    : []),
];
