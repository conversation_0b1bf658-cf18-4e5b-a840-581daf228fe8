import { FC, useMemo } from "react";
import { <PERSON><PERSON>, Spinner } from "@chakra-ui/react";

import { EButtonType, IButtonCTAProps } from "./ButtonCTA.d";
import { BaseColor, EColor } from "@/constants/colors";

const ButtonCTA: FC<IButtonCTAProps> = ({
  children,
  isLoading,
  customType = EButtonType.DEFAULT,
  loadingText,
  variant,
  ...rest
}) => {
  const style = useMemo(() => {
    if (customType === EButtonType.PRIMARY) {
      return {
        bg: BaseColor.PRIMARY,
        colorScheme: BaseColor.PRIMARY,
        color: EColor.WHITE,
      };
    }
    if (customType === EButtonType.SUCCESS) {
      return {
        bg: BaseColor.SUCCESS,
        colorScheme: BaseColor.SUCCESS,
        color: EColor.WHITE,
      };
    }
    if (customType === EButtonType.SECONDARY) {
      return {
        bg: BaseColor.SECONDARY,
        colorScheme: BaseColor.SECONDARY,
        color: EColor.WHITE,
      };
    }
    return {
      bg: BaseColor.DEFAULT,
      colorScheme: BaseColor.DEFAULT,
      color: EColor.WHITE,
    };
  }, [customType]);

  return (
    <Button
      variant={variant}
      fontSize="16px"
      height="48px"
      isLoading={isLoading}
      loadingText={loadingText}
      fontWeight="600"
      spinner={
        <Spinner
          thickness="4px"
          speed="0.65s"
          emptyColor="gray.200"
          color="blue.500"
          size="md"
        />
      }
      _disabled={{
        bg: "#F9FAFB",
        color: "#0A3150",
        opacity: 0.5,
      }}
      {...style}
      bg={variant == "outline" ? "white" : style.bg}
      color={variant == "outline" ? style.bg : "white"}
      borderColor={variant == "outline" ? style.bg : undefined}
      {...rest}
    >
      {children}
    </Button>
  );
};

export default ButtonCTA;
