import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Download, Facebook, Twitter, Youtube, Instagram } from "lucide-react";
import Image from "next/image";
import Logo from "public/images/logo-miva-sidebar.png";
import React from "react";
import { useDownloadReceipt } from "@/hooks/useStudent/useReceiptActions";
import { formatCurrency } from "@/lib/utils/helpers";
import { Spinner } from "@chakra-ui/react";

export interface ReceiptData {
  receipt_number: string;
  first_name: string | undefined | null;
  last_name: string | undefined | null;
  programmename: string;
  student_id_2: string | undefined | null;
  created_at: string;
  amount: number;
  currency: string | undefined | null;
  payment_method: string;
  description: string | undefined | null;
  status: string;
  receipt_id: string;
}

const ReceiptModal: React.FC<ReceiptData> = ({
  receipt_number,
  first_name,
  last_name,
  created_at,
  amount,
  currency,
  payment_method,
  description,
  status,
  student_id_2,
  programmename,
  receipt_id,
}) => {
  const { downloadReceipt, isLoading } = useDownloadReceipt();
  const studentName = `${first_name} ${last_name}`;
  const formattedDate = new Date(created_at).toLocaleDateString("en-GB", {
    day: "2-digit",
    month: "short",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  });

  const handleDownloadReceipt = () => {
    downloadReceipt({
      receipt_id,
      fileName: `receipt-${receipt_number}.pdf`,
      triggerDownload: true,
    });
  };
  return (
    <>
      <Card className="w-full border border-gray-200 p-6">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-8">
          <div className="relative h-8 w-24">
            <Image
              src={Logo}
              alt="MIVA University Logo"
              fill
              className="object-contain"
            />
          </div>

          <a
            href="https://miva.university"
            className="text-xl font-bold text-[#0A3150] hover:underline"
            target="_blank"
            rel="noopener noreferrer"
          >
            www.miva.university
          </a>
        </CardHeader>
        <CardContent className="space-y-8">
          <div className="flex flex-row items-start justify-between">
            <div
              className="text-primary text-base capitalize text-[#0A3150]"
              title={receipt_number}
            >
              {receipt_number}
            </div>
            <div className="text-right">
              <div className="text-[32px] font-bold text-green-600">
                {formatCurrency(amount, currency!)}
              </div>
              <div className="text-xl font-bold text-gray-500">
                {status.toLowerCase() === "success"
                  ? "Approved"
                  : status.replace(/\b\w/g, (l) => l.toUpperCase())}
              </div>
            </div>
          </div>
          <div className="space-y-4">
            <div className="text-primary grid grid-cols-2 text-[#0A3150]">
              <div className="text-sm">Student name</div>
              <div className="text-right text-sm font-semibold">
                {studentName.replace(/\b\w/g, (l) => l.toUpperCase())}
              </div>
              <div className="text-sm">Student ID</div>
              <div className="text-right text-sm font-semibold">
                {student_id_2}
              </div>
              <div className="text-sm">Intake programme</div>
              <div className="text-right text-sm font-semibold">
                {programmename}
              </div>
            </div>
            <hr></hr>
            <div className="text-primary grid grid-cols-2 text-[#0A3150]">
              <div className="text-sm">Date & Time:</div>
              <div className="text-right text-sm font-semibold">
                {formattedDate}
              </div>
              <div className="text-sm">Payment method</div>
              <div className="text-right text-sm font-semibold">
                {payment_method.replace(/\b\w/g, (l) => l.toUpperCase())}
              </div>
              <div className="text-sm">Description</div>
              <div className="text-right text-sm font-semibold">
                {description?.replace(/\b\w/g, (l) => l.toUpperCase())}
              </div>
            </div>
          </div>

          <div className="text-primary space-y-2 text-sm font-semibold text-[#0A3150]">
            <p>Address: Plot 1059, O.P Fingesi Street, Utako, FCT, Abuja.</p>
            <p>WhatsApp: +234 9132 300 000</p>
            <p>Email: <EMAIL></p>
          </div>

          <div className="space-y-4">
            <div className="text-bold text-primary text-xl text-[#0A3150]">
              follow us
            </div>
            <div className="flex space-x-4">
              <a
                href="#"
                className="text-red-600 hover:text-red-500"
                aria-label="YouTube"
              >
                <Youtube size={20} />
              </a>
              <a
                href="#"
                className="text-red-600 hover:text-blue-500"
                aria-label="Twitter"
              >
                <Twitter size={20} />
              </a>
              <a
                href="#"
                className="text-red-600 hover:text-blue-600"
                aria-label="Facebook"
              >
                <Facebook size={20} />
              </a>
              <a
                href="#"
                className="text-red-600 hover:text-pink-500"
                aria-label="Instagram"
              >
                <Instagram size={20} />
              </a>
              {/* <a href="#" className="text-gray-400 hover:text-black" aria-label="TikTok">
              <TikTok size={20} />
            </a> */}
            </div>
            <div className="space-y-2 text-sm font-semibold text-[#0A3150]">
              © 2023 All rights reserved
            </div>
          </div>
        </CardContent>
      </Card>
      <div className="m-2 flex justify-end pt-4">
        <Button
          disabled={isLoading}
          className="hovery:bg-primary/90 w-[181px] bg-[#0A3150] text-white"
          onClick={handleDownloadReceipt}
        >
          {isLoading ? (
            <Spinner />
          ) : (
            <>
              <Download className="mr-2 h-4 w-4" />
              Download Receipt
            </>
          )}
        </Button>
      </div>
    </>
  );
};

export default ReceiptModal;
