import { extractAxiosError } from "@/lib/utils/helpers";
import { useToast } from "@chakra-ui/react";
import axios from "axios";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import { authApi } from "src/api/config/api";
import { APIResponse } from "src/api/config/api.d";
import { MODULE_ROUTE, Routes } from "src/api/config/routes";
import { IActivateAccountValue } from "./ActivateAccount.d";
import { useCASValidation } from "../CAS/useCASValidation";

export const useActivateAccount = () => {
  const [showLoader, setShowLoader] = useState(true);

  const { service } = useCASValidation(setShowLoader);
  const pathname = usePathname();

  const isCas = useMemo(() => pathname.includes("cas/"), [pathname]);

  const router = useRouter();
  const toast = useToast();

  const searchParams = useSearchParams();
  const user = searchParams.get("user");
  const code = searchParams.get("code");

  useEffect(() => {
    if (!code || !user) {
      toast({
        description: "Account activation link is invalid",
        status: "error",
      });
      if (isCas) {
        router.replace(`/cas/login?service=${service}`);
      } else {
        router.replace("/login");
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, code]);

  const handleSubmit = async ({
    password,
    confirm_password,
  }: IActivateAccountValue) => {
    try {
      await authApi.patch<APIResponse<string>>(
        Routes[MODULE_ROUTE.AUTH].ACTIVATE,
        {
          user_id: user,
          password,
          confirm_password,
          verify_code: code,
        },
      );
      toast({
        description: "Account activated successfully, please login",
        status: "success",
      });
      if (isCas) {
        router.replace(`/cas/login?service=${service}`);
      } else {
        router.replace("/login");
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        toast({
          description: extractAxiosError(error),
          status: "error",
        });
      }
    }
  };

  return {
    handleSubmit,
  };
};
