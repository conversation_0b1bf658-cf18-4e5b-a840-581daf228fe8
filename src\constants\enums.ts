export enum EVIEW_PORT {
  MOBILE = "mobile",
  DESKTOP = "desktop",
}

export enum E_STATUS_APPLICATION {
  PENDING = "PENDING",
  APPLICATION_SENT = "APPLICATION_SENT",
  PENDING_REVIEW = "PENDING_REVIEW",
  APPLICATION_CANCELLED = "CANCELED",
  WAITING_LIST = "WAITING_LIST",
  INCOMPLETE_DOCUMENTS = "INCOMPLETE_DOCUMENTS",
  INCORRECT_UPLOADS = "INCORRECT_UPLOADS",
  IDENTITY_DISCREPANCIES = "IDENTITY_DISCREPANCIES",
  MISSING_SUBJECT = "MISSING_SUBJECTS",
  DO_NOT_QUALIFY = "DO_NOT_QUALIFY",
  ACCEPTED = "ACCEPTED",
  REJECTED = "REJECTED",
  ENROLLED = "ENROLLED",
  ENROLLMENT_PENDING = "ENROLLMENT_PENDING",
  PAYMENT_COMPLETED = "PAYMENT_COMPLETED",
}
