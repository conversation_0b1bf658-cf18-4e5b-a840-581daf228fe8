import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import { BaseColor } from "@/constants/colors";
import { Flex, SimpleGrid, Text, Box, useToast } from "@chakra-ui/react";
import CourseSection from "./CourseSection";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import { courseEnrollment } from "src/api/repository/Enrollment/enrollment";
import { ProgrammeLevel } from "src/api/repository/Courses/courses.d";
import { Course, Semester } from "src/api/repository/student";

export const DashboardCourseWrapper = ({
  courses,
  semester,
  goToClass,
  semesterStartDate,
  handleSemesterEnrolment,
  otherDetails,
  semesterDetails,
}: {
  courses: Course[];
  semester: string;
  semesterStartDate: string;
  goToClass: boolean;
  handleSemesterEnrolment: () => void;
  activeLevel: ProgrammeLevel;
  otherDetails: {
    student_id: string;
    status: string;
    programme_id: string;
    intake_id?: string;
  };
  enrollmentEndDate?: string;
  semesterDetails: Semester;
}) => {
  const toast = useToast();
  const handleGoToClass = () => {
    window.open("https://class.miva.university/", "_blank");
  };

  const user = useAuthStore((state) => state.user);

  const studentId: string = user?.id ?? "";
  const handleCourseEnrollment = async (course: Course) => {
    if (!course.course_offering_id) return;

    const payload = {
      course_offering_id: course.course_offering_id,
      student_id: studentId || "",
      intake_id: otherDetails?.intake_id || "",
      semester_atp_id: semesterDetails?.semester_id || "",
      programme_id: otherDetails?.programme_id || "",
      is_moodle: true,
    };

    try {
      const response = await courseEnrollment(payload);
      if (response.status === 200) {
        toast({
          description: "Course enrolled successfully",
          status: "success",
        });
      }
    } catch (error) {
      console.error("Failed to enroll in course:", error);
      toast({
        description: "Failed to enroll in course",
        status: "error",
      });
    }
  };

  return (
    <>
      {new Date() > new Date(semesterStartDate) &&
      semesterDetails.enrollment_status == "" ? (
        <Box mt={4}>
          <Box>
            <div className="flex h-full flex-col justify-center bg-cover bg-center bg-no-repeat md:h-[300px] md:bg-[url('https://res.cloudinary.com/www-pluslitedesigns-org/image/upload/v1740522094/Banner_1_syrpsd.png')] md:pl-[50%] md:pr-10 xl:pr-14">
              <Text
                color="#E83831"
                fontSize="12px"
                fontWeight={800}
                textTransform="uppercase"
                letterSpacing={2.5}
              >
                Courses available
              </Text>
              <Text color="#072339" fontSize="24px" fontWeight={700} mt={2}>
                Your {semester} courses are ready for enrolment
              </Text>
              <Box>
                <ButtonCTA
                  onClick={handleSemesterEnrolment}
                  fontSize={12}
                  height="auto"
                  py={4}
                  px={6}
                  bg="#E83831"
                  color="white"
                  mt={5}
                >
                  Enrol now
                </ButtonCTA>
              </Box>
            </div>
          </Box>
        </Box>
      ) : (
        <div>
          <Flex
            justifyContent="space-between"
            py={4}
            className={`mt-4 ${new Date(semesterStartDate) < new Date() ? "border-b border-b-[#E7EAEE]" : ""}`}
          >
            <Text
              fontWeight="semibold"
              fontSize="18px"
              color={BaseColor.PRIMARY}
              textTransform="capitalize"
            >
              {semester}
            </Text>
            {goToClass && semesterStartDate && (
              <ButtonCTA
                onClick={handleGoToClass}
                fontSize={12}
                height="auto"
                py={3}
                px={7}
              >
                Go to Class
              </ButtonCTA>
            )}
          </Flex>
          <SimpleGrid
            pt={6}
            columns={{ base: 1, sm: 2, lg: 3, xl: 4 }}
            spacing="20px"
          >
            {courses
              ?.filter((course) => course.status?.toLowerCase() === "enrolled")
              .map((course, index) => (
                <CourseSection
                  key={index}
                  course={course}
                  onEnroll={() => handleCourseEnrollment(course)}
                  semesterEnrollmentEndDate={
                    semesterDetails.enrollment_end_date
                  }
                  otherDetails={otherDetails}
                />
              ))}
          </SimpleGrid>
        </div>
      )}
    </>
  );
};
