import { APIResponse, Paginated } from "../../config/api.d";
import { baseApi } from "../../config/api";
import { NotificationData } from "./notification.d";
import { MODULE_ROUTE, Routes } from "../../config/routes";

export async function getNotifications(): Promise<
  APIResponse<Paginated<NotificationData[]>>
> {
  try {
    const response = await baseApi.get(Routes[MODULE_ROUTE.NOTIFICATION].GET, {
      params: {
        page: 1,
        perPage: 5,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching notifications:", error);
    throw error;
  }
}

export async function markAsRead(notificationId: string) {
  try {
    await baseApi.post(
      Routes[MODULE_ROUTE.NOTIFICATION].MARK_AS_READ(notificationId),
    );
  } catch (error) {
    console.error("Error marking notification as read:", error);
    throw error;
  }
}

export async function markAllAsRead() {
  try {
    await baseApi.post(Routes[MODULE_ROUTE.NOTIFICATION].MARK_ALL);
  } catch (error) {
    console.error("Error marking notifications as read:", error);
    throw error;
  }
}
