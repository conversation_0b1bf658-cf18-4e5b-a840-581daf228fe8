import { NextRequest, NextResponse } from "next/server";

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const pgtId = searchParams.get("pgtId");
  const pgtIoU = searchParams.get("pgtIou");

  if (!pgtId || !pgtIoU) {
    return NextResponse.json(
      { error: "Missing pgtId or pgtIou" },
      { status: 400 },
    );
  }

  return NextResponse.json({ success: true });
}
