import { baseApi } from "../../config/api";
import { IPinOtp } from "./forgotPinOtp";
import { MODULE_ROUTE, Routes } from "../../config/routes";

export async function ChangePinOTP(payload: IPinOtp) {
  try {
    const response = await baseApi.post(
      Routes[MODULE_ROUTE.WALLET].CHANGE_PIN_OTP,
      payload,
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching courses:", error);
    throw error;
  }
}
