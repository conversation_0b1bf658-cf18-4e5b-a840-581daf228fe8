import { FC, useMemo } from "react";
import { Flex } from "@chakra-ui/react";

import Icon from "@/components/commons/Icons/Icon";
import { EIconName } from "@/components/commons/Icons/Icon.enums";

import "./Pagination.scss";

interface IPaginationProps {
  totalPages: number;
  currentPage: number;
  handleOnChange: (page: number) => void;
  maxPagesToShow?: number;
}

const Pagination: FC<IPaginationProps> = ({
  totalPages,
  handleOnChange,
  currentPage,
  maxPagesToShow = 3,
}) => {
  const getPageNumbers = useMemo(() => {
    const pages: (number | string)[] = [];

    if (totalPages <= maxPagesToShow) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
      let endPage = startPage + maxPagesToShow - 1;

      if (endPage > totalPages) {
        endPage = totalPages;
        startPage = endPage - maxPagesToShow + 1;
      }

      if (startPage > 1) {
        pages.push(1);
        if (startPage > 2) {
          pages.push("...");
        }
      }

      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }

      if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
          pages.push("...");
        }
        pages.push(totalPages);
      }
    }

    return pages;
  }, [totalPages, currentPage, maxPagesToShow]);

  const handleNext = () => {
    if (currentPage < totalPages) {
      handleOnChange(currentPage + 1);
    }
  };

  const handlePrev = () => {
    if (currentPage > 1) {
      handleOnChange(currentPage - 1);
    }
  };

  return (
    <nav>
      <ul className="miva-pagination">
        <li className={`page-item ${currentPage === 1 ? "disabled" : ""}`}>
          <Flex onClick={handlePrev} className="page-link">
            <Icon name={EIconName.ANGLE_LEFT_B} />
          </Flex>
        </li>
        {getPageNumbers.map((number, index) =>
          typeof number === "number" ? (
            <li
              key={index}
              className={`page-item ${currentPage === number ? "active" : ""}`}
            >
              <div onClick={() => handleOnChange(number)} className="page-link">
                {number}
              </div>
            </li>
          ) : (
            <li key={index} className="page-item ellipsis">
              <div className="page-link">...</div>
            </li>
          ),
        )}
        <li
          className={`page-item ${currentPage === totalPages ? "disabled" : ""}`}
        >
          <Flex onClick={handleNext} className="page-link">
            <Icon name={EIconName.ANGLE_RIGHT_B} />
          </Flex>
        </li>
      </ul>
    </nav>
  );
};

export default Pagination;
