"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>Icon, CircleAlert, Eye, EyeOff } from "lucide-react";
import TuitionFundSetupWrapper from "../TuitionFundSetupWrapper";
import { useTuitionFund } from "../../context/TuitionFundContext";
import InputField from "@/components/commons/InputField/InputField";

const PINSetupScreen: React.FC = () => {
  const {
    formData,
    updateFormData,
    handleNext,
    handlePrevious,
    isNextDisabled,
    isWalletSetupLoading,
  } = useTuitionFund();
  const [showPIN, setShowPIN] = useState(false);
  const [showConfirmPIN, setShowConfirmPIN] = useState(false);
  const [pinError, setPinError] = useState<string | null>(null);

  const handlePINChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Only allow digits and limit to 6 characters
    if (/^\d*$/.test(value) && value.length <= 6) {
      updateFormData("pin", value);

      // Clear error when length is 6
      if (value.length === 6) {
        setPinError(null);
      } else if (value.length > 0) {
        setPinError("PIN must be 6 digits");
      }
    }
  };

  const handleConfirmPINChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Only allow digits and limit to 6 characters
    if (/^\d*$/.test(value) && value.length <= 6) {
      updateFormData("confirmPin", value);

      // Check if PINs match when both have values
      if (value && formData.pin) {
        if (value !== formData.pin) {
          setPinError("PINs do not match");
        } else {
          setPinError(null);
        }
      }
    }
  };

  return (
    <TuitionFundSetupWrapper>
      <div className="mb-6">
        <div className="relative">
          <InputField
            id="pin"
            label="Create PIN"
            type={showPIN ? "text" : "password"}
            value={formData.pin}
            onChange={handlePINChange}
            className="w-full pr-10"
            placeholder="Set A 6 Digit PIN"
            isDisabled={isWalletSetupLoading}
          />
          <button
            type="button"
            className="absolute right-0 top-1/2 mt-1 flex items-center pr-3 text-[#0A3150] disabled:opacity-50"
            onClick={() => setShowPIN(!showPIN)}
            disabled={isWalletSetupLoading}
          >
            {showPIN ? (
              <EyeOff className="h-5 w-5" />
            ) : (
              <Eye className="h-5 w-5" />
            )}
          </button>
        </div>
        {pinError && formData.pin.length < 6 && (
          <p className="mt-1 text-sm text-[#D3332D]">{pinError}</p>
        )}
      </div>

      <div className="mb-6">
        <div className="relative">
          <InputField
            id="confirmPin"
            label="Confirm PIN"
            type={showConfirmPIN ? "text" : "password"}
            value={formData.confirmPin}
            onChange={handleConfirmPINChange}
            className={`w-full pr-10 ${formData.confirmPin !== formData.pin && formData.confirmPin ? "border-red-[#D3332D]" : ""}`}
            placeholder="Confirm PIN"
            maxLength={6}
            isDisabled={isWalletSetupLoading}
          />
          <button
            type="button"
            onClick={() => setShowConfirmPIN(!showConfirmPIN)}
            className="absolute right-3 top-2/3 -translate-y-1/2 transform text-[#0A3150] disabled:opacity-50"
            disabled={isWalletSetupLoading}
          >
            {showConfirmPIN ? (
              <EyeOff className="h-5 w-5" />
            ) : (
              <Eye className="h-5 w-5" />
            )}
          </button>
        </div>
        {formData.confirmPin && formData.confirmPin !== formData.pin && (
          <p className="mt-1 text-sm text-[#D3332D]">PINs do not match</p>
        )}
      </div>

      <div className="mb-6 flex items-center rounded-md bg-[#FFF7E5] p-4 text-xs text-[#AA7200]">
        <CircleAlert className="mr-2 h-5 w-5" />{" "}
        <span>
          This PIN is not for logging in—it’s required to authorize transactions
          from your school tuition fund
        </span>
      </div>

      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={handlePrevious}
          className="px-6 text-[#0A3150]"
          disabled={isWalletSetupLoading}
        >
          Previous
        </Button>
        <Button
          variant="primary"
          onClick={handleNext}
          disabled={isNextDisabled() || isWalletSetupLoading}
          className="bg-[#0A3150] px-6 text-white"
        >
          {isWalletSetupLoading ? "Setting up..." : "Done"}
          {!isWalletSetupLoading && <ArrowRightIcon className="ml-2" />}
        </Button>
      </div>
    </TuitionFundSetupWrapper>
  );
};

export default PINSetupScreen;
