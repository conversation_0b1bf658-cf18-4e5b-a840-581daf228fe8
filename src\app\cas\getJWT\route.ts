import { NextRequest } from "next/server";
import {
  getCASTicket,
  isTicketInvalid,
  refreshToken,
  validateCasService,
} from "src/module/auth/CAS/cas.utils";

export async function GET(req: NextRequest) {
  const reqUrl = req.url;
  const { searchParams } = new URL(reqUrl);
  const service = searchParams.get("service");
  const ticket = searchParams.get("ticket");

  if (!ticket || !service) {
    return Response.json({ error: "Ticket or service missing" });
  }
  const urlDecodedService = decodeURIComponent(service);
  const validService = await validateCasService(urlDecodedService);
  if (!validService) {
    return Response.json({ error: "Service invalid" });
  }

  try {
    const casTicket = await getCASTicket(ticket);
    if (await isTicketInvalid(casTicket, "ST")) {
      return Response.json({ error: "Ticket invalid" });
    }
    const tokenDetails = await refreshToken(casTicket.refresh_token);
    return Response.json(tokenDetails);
  } catch (e) {
    console.error(e);
  }

  return Response.json({ error: "Validation unsuccessful" });
}
