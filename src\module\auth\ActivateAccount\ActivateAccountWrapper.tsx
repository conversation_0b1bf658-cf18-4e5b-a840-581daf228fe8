"use client";

import { Flex } from "@chakra-ui/react";

import { EVIEW_PORT } from "@/constants/enums";
import { TParamPageCommon } from "@/constants/types";
import ActivateAccountContainer from "./ActivateAccountContainer";
import RightImageBlock from "../components/RightImageBlock";
import { useActivateAccount } from "./useActivateAccount";

const ActivateAccountWrapper = ({ searchParams }: TParamPageCommon) => {
  const { handleSubmit } = useActivateAccount();

  return (
    <Flex alignItems="center" gap={{ xl: "172px", md: "40px" }}>
      <ActivateAccountContainer handleSubmit={handleSubmit} />
      {searchParams?.viewport !== EVIEW_PORT.MOBILE && <RightImageBlock />}
    </Flex>
  );
};

export default ActivateAccountWrapper;
