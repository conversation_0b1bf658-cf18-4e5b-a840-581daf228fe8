export type CASService = {
  id: string;
  created_at: string;
  name: string;
  service_url: string;
  updated_at: string;
};

export type CASTicket = {
  backend_service: string;
  cas_service_id: string;
  created_at: Date;
  expires_at: Date;
  id: string;
  proxy_granting_ticket: string;
  refresh_token: string;
  ticket: string;
  type: "ST" | "PT" | "PGT";
  user_id: string;
};

export type GenerateServiceType = {
  service: string;
  access_token: string;
  refresh_token: string;
};
