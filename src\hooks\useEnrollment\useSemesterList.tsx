/* eslint-disable @typescript-eslint/no-explicit-any */
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { IProgrammeIntakeSemesterParams } from "../../api/repository/Misc/programmeIntakeSemester.d";
import { getSemesterByProgrammeIntake } from "../../api/repository/Misc/programmeIntakeSemester";

export const useSemesterList = ({
  programme_intake_id,
}: IProgrammeIntakeSemesterParams) => {
  const query = useQuery({
    queryKey: ["getSemesterByProgrammeIntake", { programme_intake_id }],
    queryFn: () => getSemesterByProgrammeIntake({ programme_intake_id }),
    retry: false,
    enabled: !!programme_intake_id,
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
    select: (response) => {
      const data = response.data;
      return data;
    },
  });
  return query;
};
