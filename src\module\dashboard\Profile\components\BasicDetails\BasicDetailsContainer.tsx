"use client";
import { FormikBag, withForm<PERSON> } from "formik";
import validator from "validator";
import { BasicDetails } from "./BasicDetails";
import { checkValueError, validateRequired } from "@/lib/utils/validation";
import { IValidations } from "@/lib/utils/validation.d";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import { IBasicDetails, IBasicDetailsContainerProps } from "../../Profile";
import { undefinedToString } from "@/lib/utils/string";

const requiredValidator = {
  validator: validateRequired,
  code: "This field is required",
};

const validateFields: IValidations<IBasicDetails> = {
  email: [
    requiredValidator,
    {
      validator: validator.isEmail,
      code: "Email is invalid",
    },
  ],
  title: [],
  first_name: [requiredValidator],
  last_name: [requiredValidator],
  dob: [],
  gender: [requiredValidator],
  marital_status: [],
  nationality: [requiredValidator],
  home_address: [requiredValidator],
  city: [],
  state: [requiredValidator],
  country: [requiredValidator],
  phone_number: [requiredValidator],
  lga: [],
  next_of_kin: [],
  next_of_kin_phone_number: [],
  national_id: [],
  other_name: [],
  employment_status: [requiredValidator],
  company_name: [],
  industry: [],
};

export const onSubmit = async (
  values: IBasicDetails,
  {
    setErrors,
    props,
    setSubmitting,
  }: FormikBag<IBasicDetailsContainerProps, IBasicDetails>,
) => {
  setSubmitting(true);
  try {
    await props.handleProfileUpdate(values);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (e: any) {
    setErrors(e);
  }
};

const BasicDetailsContainer = withFormik<
  IBasicDetailsContainerProps,
  IBasicDetails
>({
  mapPropsToValues: () => {
    const user = useAuthStore.getState().user;
    return {
      email: undefinedToString(user?.contact_information.email),
      title: undefinedToString(user?.biography.title),
      first_name: undefinedToString(user?.biography?.first_name),
      last_name: undefinedToString(user?.biography.last_name),
      other_name: undefinedToString(user?.biography.other_name),
      dob: undefinedToString(user?.biography.date_of_birth),
      gender: undefinedToString(user?.biography.gender),
      marital_status: undefinedToString(user?.biography.marital_status),
      nationality: undefinedToString(user?.biography.nationality),
      home_address: undefinedToString(
        user?.contact_information.residential_address,
      ),
      state: undefinedToString(user?.contact_information.state),
      country: undefinedToString(user?.contact_information.country),
      phone_number: undefinedToString(user?.contact_information.phone_number),
      lga: undefinedToString(user?.contact_information.lga),
      city: undefinedToString(user?.contact_information.city),
      next_of_kin: undefinedToString(user?.contact_information.next_of_kin),
      next_of_kin_phone_number: undefinedToString(
        user?.contact_information.next_of_kin_phone_number,
      ),
      national_id: undefinedToString(user?.biography.national_id),
      industry: undefinedToString(user?.biography.industry),
      employment_status: undefinedToString(user?.biography.employment_status),
      company_name: undefinedToString(user?.biography.company_name),
    };
  },
  validate: checkValueError(validateFields),
  handleSubmit: onSubmit,
  validateOnChange: true,
})(BasicDetails);

export default BasicDetailsContainer;
