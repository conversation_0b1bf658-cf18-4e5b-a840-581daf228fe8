"use client";
import { TParamPageCommon } from "../../constants/types";
import CommonLoading from "@/components/commons/CommonLoading/CommonLoading";
import { Suspense } from "react";
import ResendActivationWrapper from "src/module/auth/Activate/ResendActivationWrapper";

const ResendActivation = (props: TParamPageCommon) => {
  return (
    <Suspense fallback={<CommonLoading size="medium" />}>
      <div className="mt-4 flex justify-center">
        <ResendActivationWrapper {...props} />
      </div>
    </Suspense>
  );
};

export default ResendActivation;
