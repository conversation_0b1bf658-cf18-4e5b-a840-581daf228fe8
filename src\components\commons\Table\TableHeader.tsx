import { FC } from "react";

import { Thead, Tr, Th } from "@chakra-ui/react";
import { cn } from "@/lib/utils";

import { TableHeaderProps } from "./Table.d";

const TableHeader: FC<TableHeaderProps> = ({ titleList, classHeader }) => {
  return (
    <Thead className={cn("fw-bolder text-muted", classHeader)}>
      <Tr>
        {titleList.map((titleElm, index: number) => {
          return (
            <Th key={`title-header-${index}`} textAlign="center">
              {titleElm}
            </Th>
          );
        })}
      </Tr>
    </Thead>
  );
};

export default TableHeader;
