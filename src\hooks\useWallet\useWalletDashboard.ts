// src/hooks/useWalletDashboard.ts
import { useEffect, useState } from "react";
import { getWalletDashboard } from "src/api/repository/wallet";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";

export const useWalletDashboard = () => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Get student ID from auth store
  const student = useAuthStore((state) => state.student);
  const studentId = student?.personal_details?.id;

  useEffect(() => {
    const fetchData = async () => {
      try {
        if (!studentId) {
          throw new Error("Student ID not available");
        }

        setLoading(true);
        const response = await getWalletDashboard({
          id: studentId, // Pass the student ID from auth store
          perPage: 10,
          page: 1,
        });
        setData(response.data);
      } catch (err) {
        setError(
          err instanceof Error
            ? err.message
            : "Failed to fetch wallet dashboard",
        );
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [studentId]);

  return { data, loading, error };
};
