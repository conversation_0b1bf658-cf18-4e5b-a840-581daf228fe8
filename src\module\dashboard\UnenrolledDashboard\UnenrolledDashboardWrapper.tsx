"use client";

import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import { EButtonType } from "@/components/commons/ButtonCTA/ButtonCTA.d";
import PageTitle from "@/components/commons/PageTitle/PageTitle";
import { BaseColor } from "@/constants/colors";
import {
  Box,
  Card,
  CardBody,
  CardFooter,
  Flex,
  Heading,
  Image,
  Stack,
  Tag,
  Text,
} from "@chakra-ui/react";
import Link from "next/link";
import DashboardSection from "../components/DashboardSection";
import DashboardTitle from "../components/DashboardTitle";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";

const InfoSection = ({ label, value }: { label: string; value: string }) => (
  <Stack width="270px">
    <Text
      textTransform="uppercase"
      fontWeight="semibold"
      color="#8498A7"
      fontSize="12px"
    >
      {label}
    </Text>
    <Text fontWeight="semibold" color={BaseColor.PRIMARY} fontSize="16px">
      {value}
    </Text>
  </Stack>
);

const UnenrolledDashboardWrapper = () => {
  const user = useAuthStore((state) => state.user);
  return (
    <Box>
      <PageTitle> Welcome, {user?.biography?.first_name}</PageTitle>
      <DashboardSection>
        <DashboardTitle title="Application Status" tag="Application Sent" />
        <Card
          mt="40px"
          position="inherit"
          direction={{ base: "column", lg: "row" }}
          overflow="hidden"
          variant="unstyled"
          gap="32px"
        >
          <Image
            objectFit="cover"
            maxW={{ base: "100%", sm: "340px" }}
            height={{ base: "100%", sm: "320px" }}
            src="/images/dashboard-1.png"
            alt="card-image"
          />
          <Stack>
            <CardBody>
              <Heading
                fontSize="24px"
                fontWeight="semibold"
                color={BaseColor.PRIMARY}
                size="md"
              >
                BSC. in Computing and Cybersecurity
              </Heading>
              <Text
                fontSize="16px"
                fontWeight="medium"
                color={BaseColor.PRIMARY}
                py="2"
              >
                This programme is designed to prepare individuals for careers in
                the field of cybersecurity, which focuses on protecting computer
                systems, networks, and data from unauthorised access, attacks,
                and threats.
              </Text>
              <Stack mt="40px" spacing="40px">
                <Flex flexDir={{ xl: "row", base: "column" }} gap="32px">
                  <InfoSection
                    label="Study Level"
                    value="Accredited Bachelor's degree"
                  />
                  <InfoSection label="Course units" value="120 Units" />
                </Flex>
                <Flex flexDir={{ xl: "row", base: "column" }} gap="32px">
                  <InfoSection label="Start date" value="1 May 2024" />
                  <InfoSection label="Tuition per semester" value="₦300,000" />
                </Flex>
                <Flex flexDir={{ xl: "row", base: "column" }} gap="32px">
                  <InfoSection label="Study duration" value="36-72 months" />
                  <Stack width="270px">
                    <Text
                      textTransform="uppercase"
                      fontWeight="semibold"
                      color="#8498A7"
                      fontSize="12px"
                    >
                      Tuition per session
                    </Text>
                    <Flex gap="5px">
                      <Text
                        fontWeight="semibold"
                        color="#6c737f"
                        textDecoration="line-through"
                      >
                        ₦350,000
                      </Text>
                      <Text fontWeight="semibold">₦320,000</Text>
                      <Tag background="#009933" color="white" fontSize={12}>
                        Save
                        <Text textDecoration="line-through" ml="1">
                          ₦30,000
                        </Text>
                      </Tag>
                    </Flex>
                  </Stack>
                </Flex>
              </Stack>
            </CardBody>
            <CardFooter>
              <Link href="/enrollment" className="mt-10 block w-full">
                <ButtonCTA customType={EButtonType.SUCCESS} width="100%">
                  Start Enrollment
                </ButtonCTA>
              </Link>
            </CardFooter>
          </Stack>
        </Card>
      </DashboardSection>
    </Box>
  );
};

export default UnenrolledDashboardWrapper;
