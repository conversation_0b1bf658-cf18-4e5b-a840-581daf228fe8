import { NextRequest } from "next/server";
import {
  createTicket,
  generateTicket,
  getCASTicket,
  getFailureResponse,
  getServiceValidationSuccessResponse,
  getUserByAccessToken,
  isTicketInvalid,
  refreshToken,
  sendPGTToCallback,
  validateCasService,
} from "src/module/auth/CAS/cas.utils";

export async function GET(req: NextRequest) {
  const reqUrl = req.url;
  const { searchParams } = new URL(reqUrl);
  const service = searchParams.get("service");
  const ticket = searchParams.get("ticket");
  const format = searchParams.get("format");
  const pgtUrl = searchParams.get("pgtUrl");

  if (!ticket || !service) {
    return getFailureResponse(
      "INVALID_REQUEST",
      "Ticket or service missing",
      format,
    );
  }
  const urlDecodedService = decodeURIComponent(service);
  const validService = await validateCasService(urlDecodedService);
  if (!validService) {
    return getFailureResponse(
      "INVALID_SERVICE",
      "Service is not authorised to the CAS",
      format,
    );
  }

  try {
    const casTicket = await getCASTicket(ticket);
    if (casTicket.cas_service_id !== validService.id) {
      return getFailureResponse(
        "TICKET_SERVICE_MISMATCH",
        `Ticket is invalid for this service {${casTicket.cas_service_id} !== ${validService.id}} urlDecodedService`,
        format,
      );
    }

    if (await isTicketInvalid(casTicket, "ST")) {
      return getFailureResponse("TICKET_EXPIRED", "Ticket is expired", format);
    }

    const tokenDetails = await refreshToken(casTicket.refresh_token);
    const user = await getUserByAccessToken(tokenDetails.access_token);

    let pgtTicket: undefined | string;

    if (pgtUrl) {
      pgtTicket = generateTicket("PGT");
      await createTicket(
        {
          cas_service_id: validService.id,
          refresh_token: casTicket.refresh_token,
          ticket: pgtTicket,
          type: "PGT",
          user_id: user.id,
        },
        240,
      );
    }

    if (user) {
      if (pgtUrl && pgtTicket) {
        await sendPGTToCallback(pgtUrl, pgtTicket, `PGTIOU-${casTicket.id}`);
      }
      return getServiceValidationSuccessResponse(
        user,
        pgtTicket,
        `PGTIOU-${casTicket.id}`,
        format,
      );
    }
  } catch (e) {
    let errorMessage = "";
    if (typeof e === "string") {
      errorMessage = e.toUpperCase(); // works, `e` narrowed to string
    } else if (e instanceof Error) {
      errorMessage = e.message; // works, `e` narrowed to Error
    }
    return getFailureResponse(
      "INVALID_TICKET",
      `Ticket is invalid error - ${errorMessage}`,
      format,
    );
  }

  return getFailureResponse(
    "INTERNAL_ERROR",
    "Ticket could not be validated",
    format,
  );
}
