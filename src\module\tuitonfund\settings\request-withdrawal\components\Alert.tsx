import { Card, Flex, Text } from "@chakra-ui/react";
import { <PERSON>Alert } from "lucide-react";

export default function WarningAlert({ text }: { text: string }) {
  return (
    <Card
      padding={"10px"}
      shadow={0}
      variant="destructive"
      style={{ background: "#FDEBEA" }}
    >
      <Flex gap={"8px"} alignItems={"center"}>
        <CircleAlert color="#D3332D" className="flex-shrink-0" />
        <Text fontWeight={600} fontSize={12} color={"#D3332D"}>
          {text}
        </Text>
      </Flex>
    </Card>
  );
}
