"use client";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { PlusIcon } from "lucide-react";
import { MoreVertical } from "lucide-react";
import { BaseColor } from "@/constants/colors";
import { useRouter } from "next/navigation";

const Actions = ({ onOpenConfirm }: { onOpenConfirm: VoidFunction }) => {
  const router = useRouter();
  return (
    <DropdownMenu>
      <DropdownMenuTrigger>
        <MoreVertical
          className="text-primary block h-4 w-4 lg:hidden"
          color="white"
        />
      </DropdownMenuTrigger>
      <DropdownMenuContent className="bg-white">
        <DropdownMenuItem
          onClick={() => router.push("tuition-fund/settings")}
          color={BaseColor.PRIMARY_400}
          className="cursor-pointer"
        >
          <div className="flex items-center gap-x-2 text-sm font-semibold text-[#3B5A73]">
            <svg
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M17.767 7.95866L16.192 7.43366L16.9337 5.95033C17.0088 5.79507 17.034 5.6203 17.0056 5.45014C16.9773 5.27998 16.8968 5.12281 16.7753 5.00033L15.0003 3.22533C14.8772 3.10207 14.7185 3.0205 14.5466 2.9921C14.3747 2.9637 14.1982 2.98989 14.042 3.067L12.5587 3.80866L12.0337 2.23366C11.9782 2.06949 11.873 1.92668 11.7326 1.82512C11.5922 1.72355 11.4236 1.66828 11.2503 1.66699H8.75033C8.57562 1.66654 8.4052 1.72101 8.26314 1.8227C8.12108 1.92438 8.01457 2.06814 7.95866 2.23366L7.43366 3.80866L5.95033 3.067C5.79507 2.99181 5.6203 2.96666 5.45014 2.99502C5.27998 3.02338 5.12281 3.10385 5.00033 3.22533L3.22533 5.00033C3.10207 5.12347 3.0205 5.28214 2.9921 5.45404C2.9637 5.62594 2.98989 5.80242 3.067 5.95866L3.80866 7.442L2.23366 7.967C2.06949 8.02244 1.92668 8.12769 1.82512 8.26808C1.72355 8.40848 1.66828 8.57705 1.66699 8.75033V11.2503C1.66654 11.425 1.72101 11.5955 1.8227 11.7375C1.92438 11.8796 2.06814 11.9861 2.23366 12.042L3.80866 12.567L3.067 14.0503C2.99181 14.2056 2.96666 14.3804 2.99502 14.5505C3.02338 14.7207 3.10385 14.8778 3.22533 15.0003L5.00033 16.7753C5.12347 16.8986 5.28214 16.9802 5.45404 17.0086C5.62594 17.037 5.80242 17.0108 5.95866 16.9337L7.442 16.192L7.967 17.767C8.0229 17.9325 8.12941 18.0763 8.27147 18.178C8.41353 18.2796 8.58396 18.3341 8.75866 18.3337H11.2587C11.4334 18.3341 11.6038 18.2796 11.7459 18.178C11.8879 18.0763 11.9944 17.9325 12.0503 17.767L12.5753 16.192L14.0587 16.9337C14.2129 17.0069 14.386 17.0311 14.5544 17.0027C14.7229 16.9744 14.8785 16.895 15.0003 16.7753L16.7753 15.0003C16.8986 14.8772 16.9802 14.7185 17.0086 14.5466C17.037 14.3747 17.0108 14.1982 16.9337 14.042L16.192 12.5587L17.767 12.0337C17.9312 11.9782 18.074 11.873 18.1755 11.7326C18.2771 11.5922 18.3324 11.4236 18.3337 11.2503V8.75033C18.3341 8.57562 18.2796 8.4052 18.178 8.26314C18.0763 8.12108 17.9325 8.01457 17.767 7.95866ZM16.667 10.6503L15.667 10.9837C15.437 11.0583 15.2261 11.182 15.0487 11.3462C14.8713 11.5105 14.7318 11.7114 14.6399 11.935C14.5479 12.1586 14.5057 12.3995 14.5161 12.641C14.5266 12.8825 14.5894 13.1189 14.7003 13.3337L15.1753 14.2837L14.2587 15.2003L13.3337 14.7003C13.12 14.5939 12.8859 14.5345 12.6473 14.5264C12.4086 14.5183 12.1711 14.5615 11.9506 14.6532C11.7301 14.7448 11.5319 14.8828 11.3694 15.0577C11.2069 15.2326 11.0839 15.4404 11.0087 15.667L10.6753 16.667H9.35033L9.01699 15.667C8.9424 15.437 8.81868 15.2261 8.65441 15.0487C8.49014 14.8713 8.28925 14.7318 8.06568 14.6399C7.84211 14.5479 7.6012 14.5057 7.35968 14.5161C7.11816 14.5266 6.8818 14.5894 6.667 14.7003L5.717 15.1753L4.80033 14.2587L5.30033 13.3337C5.41124 13.1189 5.4741 12.8825 5.48453 12.641C5.49497 12.3995 5.45274 12.1586 5.36078 11.935C5.26881 11.7114 5.12931 11.5105 4.95195 11.3462C4.77459 11.182 4.56361 11.0583 4.33366 10.9837L3.33366 10.6503V9.35033L4.33366 9.01699C4.56361 8.9424 4.77459 8.81868 4.95195 8.65441C5.12931 8.49014 5.26881 8.28925 5.36078 8.06568C5.45274 7.84211 5.49497 7.6012 5.48453 7.35968C5.4741 7.11816 5.41124 6.8818 5.30033 6.667L4.82533 5.74199L5.74199 4.82533L6.667 5.30033C6.8818 5.41124 7.11816 5.4741 7.35968 5.48453C7.6012 5.49497 7.84211 5.45274 8.06568 5.36078C8.28925 5.26881 8.49014 5.12931 8.65441 4.95195C8.81868 4.77459 8.9424 4.56361 9.01699 4.33366L9.35033 3.33366H10.6503L10.9837 4.33366C11.0583 4.56361 11.182 4.77459 11.3462 4.95195C11.5105 5.12931 11.7114 5.26881 11.935 5.36078C12.1586 5.45274 12.3995 5.49497 12.641 5.48453C12.8825 5.4741 13.1189 5.41124 13.3337 5.30033L14.2837 4.82533L15.2003 5.74199L14.7003 6.667C14.5939 6.8807 14.5345 7.11478 14.5264 7.3534C14.5183 7.59202 14.5615 7.8296 14.6532 8.05006C14.7448 8.27052 14.8828 8.46872 15.0577 8.63123C15.2326 8.79374 15.4404 8.91677 15.667 8.99199L16.667 9.32533V10.6503ZM10.0003 6.667C9.34106 6.667 8.69659 6.86249 8.14843 7.22876C7.60026 7.59503 7.17302 8.11563 6.92073 8.72472C6.66844 9.3338 6.60243 10.004 6.73104 10.6506C6.85966 11.2972 7.17713 11.8912 7.64331 12.3574C8.10948 12.8235 8.70342 13.141 9.35003 13.2696C9.99663 13.3982 10.6669 13.3322 11.2759 13.0799C11.885 12.8276 12.4056 12.4004 12.7719 11.8522C13.1382 11.3041 13.3337 10.6596 13.3337 10.0003C13.3337 9.11627 12.9825 8.26843 12.3574 7.64331C11.7322 7.01818 10.8844 6.667 10.0003 6.667ZM10.0003 11.667C9.67069 11.667 9.34846 11.5692 9.07438 11.3861C8.8003 11.203 8.58667 10.9427 8.46053 10.6381C8.33438 10.3336 8.30138 9.99848 8.36569 9.67518C8.42999 9.35188 8.58873 9.0549 8.82182 8.82182C9.0549 8.58873 9.35188 8.42999 9.67518 8.36569C9.99848 8.30138 10.3336 8.33438 10.6381 8.46053C10.9427 8.58667 11.203 8.8003 11.3861 9.07438C11.5692 9.34846 11.667 9.67069 11.667 10.0003C11.667 10.4424 11.4914 10.8663 11.1788 11.1788C10.8663 11.4914 10.4424 11.667 10.0003 11.667Z"
                fill="#0A3150"
              />
            </svg>
            Settings
          </div>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => {
            onOpenConfirm();
          }}
          color={BaseColor.PRIMARY_400}
          className="cursor-pointer"
        >
          <div className="flex items-center gap-x-2 text-sm font-semibold text-[#3B5A73]">
            <svg
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M8.71634 5.00008L9.16634 4.50841V7.50008C9.16634 7.72109 9.25414 7.93305 9.41042 8.08933C9.5667 8.24561 9.77866 8.33341 9.99967 8.33341C10.2207 8.33341 10.4326 8.24561 10.5889 8.08933C10.7452 7.93305 10.833 7.72109 10.833 7.50008V4.50841L11.283 4.96674C11.3587 5.05218 11.451 5.12117 11.5544 5.16944C11.6578 5.21771 11.77 5.24422 11.8841 5.24735C11.9982 5.25047 12.1117 5.23014 12.2176 5.1876C12.3234 5.14507 12.4195 5.08124 12.4997 5.00008C12.5778 4.92261 12.6398 4.83044 12.6821 4.72889C12.7244 4.62734 12.7462 4.51842 12.7462 4.40841C12.7462 4.2984 12.7244 4.18948 12.6821 4.08793C12.6398 3.98638 12.5778 3.89421 12.4997 3.81674L10.5913 1.90841C10.5121 1.83254 10.4186 1.77307 10.3163 1.73341C10.1135 1.65006 9.88589 1.65006 9.68301 1.73341C9.58071 1.77307 9.48726 1.83254 9.40801 1.90841L7.49967 3.78341C7.33833 3.94475 7.24769 4.16357 7.24769 4.39174C7.24769 4.61991 7.33833 4.83874 7.49967 5.00008C7.66101 5.16142 7.87984 5.25206 8.10801 5.25206C8.33618 5.25206 8.555 5.16142 8.71634 5.00008ZM9.99967 10.0001C9.50522 10.0001 9.02187 10.1467 8.61075 10.4214C8.19963 10.6961 7.87919 11.0866 7.68997 11.5434C7.50076 12.0002 7.45125 12.5028 7.54771 12.9878C7.64417 13.4728 7.88228 13.9182 8.23191 14.2678C8.58154 14.6175 9.027 14.8556 9.51195 14.952C9.9969 15.0485 10.4996 14.999 10.9564 14.8098C11.4132 14.6206 11.8036 14.3001 12.0783 13.889C12.3531 13.4779 12.4997 12.9945 12.4997 12.5001C12.4997 11.837 12.2363 11.2011 11.7674 10.7323C11.2986 10.2635 10.6627 10.0001 9.99967 10.0001ZM9.99967 13.3334C9.83486 13.3334 9.67374 13.2845 9.5367 13.193C9.39966 13.1014 9.29285 12.9713 9.22977 12.819C9.1667 12.6667 9.1502 12.4992 9.18235 12.3375C9.21451 12.1758 9.29387 12.0274 9.41042 11.9108C9.52696 11.7943 9.67545 11.7149 9.8371 11.6828C9.99875 11.6506 10.1663 11.6671 10.3186 11.7302C10.4708 11.7932 10.601 11.9001 10.6926 12.0371C10.7841 12.1741 10.833 12.3353 10.833 12.5001C10.833 12.7211 10.7452 12.9331 10.5889 13.0893C10.4326 13.2456 10.2207 13.3334 9.99967 13.3334ZM4.16634 12.5001C4.16634 12.6649 4.21521 12.826 4.30678 12.9631C4.39835 13.1001 4.5285 13.2069 4.68077 13.27C4.83304 13.333 5.0006 13.3496 5.16225 13.3174C5.3239 13.2852 5.47239 13.2059 5.58893 13.0893C5.70547 12.9728 5.78484 12.8243 5.817 12.6627C5.84915 12.501 5.83265 12.3334 5.76957 12.1812C5.7065 12.0289 5.59969 11.8988 5.46265 11.8072C5.32561 11.7156 5.16449 11.6667 4.99967 11.6667C4.77866 11.6667 4.5667 11.7545 4.41042 11.9108C4.25414 12.0671 4.16634 12.2791 4.16634 12.5001ZM15.833 12.5001C15.833 12.3353 15.7841 12.1741 15.6926 12.0371C15.601 11.9001 15.4708 11.7932 15.3186 11.7302C15.1663 11.6671 14.9987 11.6506 14.8371 11.6828C14.6754 11.7149 14.527 11.7943 14.4104 11.9108C14.2939 12.0274 14.2145 12.1758 14.1824 12.3375C14.1502 12.4992 14.1667 12.6667 14.2298 12.819C14.2928 12.9713 14.3997 13.1014 14.5367 13.193C14.6737 13.2845 14.8349 13.3334 14.9997 13.3334C15.2207 13.3334 15.4326 13.2456 15.5889 13.0893C15.7452 12.9331 15.833 12.7211 15.833 12.5001ZM16.6663 6.66674H13.333C13.112 6.66674 12.9 6.75454 12.7438 6.91082C12.5875 7.0671 12.4997 7.27906 12.4997 7.50008C12.4997 7.72109 12.5875 7.93305 12.7438 8.08933C12.9 8.24561 13.112 8.33341 13.333 8.33341H16.6663C16.8874 8.33341 17.0993 8.42121 17.2556 8.57749C17.4119 8.73377 17.4997 8.94573 17.4997 9.16674V15.8334C17.4997 16.0544 17.4119 16.2664 17.2556 16.4227C17.0993 16.5789 16.8874 16.6667 16.6663 16.6667H3.33301C3.11199 16.6667 2.90003 16.5789 2.74375 16.4227C2.58747 16.2664 2.49967 16.0544 2.49967 15.8334V9.16674C2.49967 8.94573 2.58747 8.73377 2.74375 8.57749C2.90003 8.42121 3.11199 8.33341 3.33301 8.33341H6.66634C6.88735 8.33341 7.09932 8.24561 7.2556 8.08933C7.41188 7.93305 7.49967 7.72109 7.49967 7.50008C7.49967 7.27906 7.41188 7.0671 7.2556 6.91082C7.09932 6.75454 6.88735 6.66674 6.66634 6.66674H3.33301C2.66997 6.66674 2.03408 6.93013 1.56524 7.39898C1.0964 7.86782 0.833008 8.5037 0.833008 9.16674V15.8334C0.833008 16.4965 1.0964 17.1323 1.56524 17.6012C2.03408 18.07 2.66997 18.3334 3.33301 18.3334H16.6663C17.3294 18.3334 17.9653 18.07 18.4341 17.6012C18.9029 17.1323 19.1663 16.4965 19.1663 15.8334V9.16674C19.1663 8.5037 18.9029 7.86782 18.4341 7.39898C17.9653 6.93013 17.3294 6.66674 16.6663 6.66674Z"
                fill="#0A3150"
              />
            </svg>
            Pay Tuition
          </div>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => {
            onOpenConfirm();
          }}
          color={BaseColor.PRIMARY}
          className="cursor-pointer"
        >
          <div className="flex gap-x-2 text-sm font-semibold text-[#0A3150]">
            <PlusIcon color="#0A3150" size={20} /> Add Funds
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default Actions;
