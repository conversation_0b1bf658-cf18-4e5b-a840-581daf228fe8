"use client";

import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import CommonSelect from "@/components/commons/CommonSelect/CommonSelect";
import { InputError } from "@/components/commons/InputField/InputError";
import { BaseColor } from "@/constants/colors";
import { Box, Checkbox, Text, Textarea } from "@chakra-ui/react";
import React, { useMemo, useState } from "react";

export default function RequestWithdrawalForm({
  onFormSubmit,
}: {
  onFormSubmit: () => void;
}) {
  const [reasonForWithdrawal, setReasonForWithdrawal] = useState("");
  const [tellUsMore, setTellUsMore] = useState("");

  const withdrawalOptions = useMemo(() => {
    return [
      { label: "End of Academic Session", value: "End of Academic Session" },
      { label: "Dropout", value: "Dropout" },
    ];
  }, []);

  const onSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    onFormSubmit();
  };

  return (
    <form onSubmit={onSubmit}>
      <Box mb={5}>
        <Text paddingBottom={2} color="#0A3150" fontWeight={600} fontSize={14}>
          Reason For Withdrawal
        </Text>
        <CommonSelect
          label=""
          value={reasonForWithdrawal}
          showIndicatorIcon={true}
          options={withdrawalOptions || []}
          onChange={(value) => {
            setReasonForWithdrawal(value);
          }}
          className="h-[47px]"
          placeholder="select a reason"
        />
        <InputError error={""} touched={false} />
      </Box>

      <Box mb={5}>
        <Text paddingBottom={2} color="#0A3150" fontWeight={600} fontSize={14}>
          Tell Us More
        </Text>
        <Textarea
          value={tellUsMore}
          height={"119px"}
          resize={"none"}
          onChange={(e) => {
            setTellUsMore(e.target.value);
          }}
        />
        <InputError error={""} touched={false} />
      </Box>

      <Box mb={5} px={"12px"}>
        <Checkbox size={"md"} borderColor={BaseColor.PRIMARY} gap={"12px"}>
          <Text
            fontSize={14}
            fontWeight={500}
            lineHeight={"20px"}
            color={BaseColor.PRIMARY}
          >
            I understand that my withdrawal request will only be approved if it
            complies with the tuition policy, and that this request may be
            declined if I haven’t completed my final academic year.
          </Text>
        </Checkbox>
      </Box>

      <Box justifyContent={"end"} display={"flex"}>
        <ButtonCTA
          type="submit"
          mt={"40px"}
          fontSize={12}
          height="auto"
          py={3}
          px={7}
        >
          <Text fontSize={14} fontWeight={600}>
            Send Request
          </Text>
        </ButtonCTA>
      </Box>
    </form>
  );
}
