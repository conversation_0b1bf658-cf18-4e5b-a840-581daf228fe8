"use client";

import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import CommonSelect from "@/components/commons/CommonSelect/CommonSelect";
import { InputError } from "@/components/commons/InputField/InputError";
import InputField from "@/components/commons/InputField/InputField";
import { BaseColor } from "@/constants/colors";
import { Box, Checkbox, Flex, HStack, Text, Textarea } from "@chakra-ui/react";
import { FormikProps } from "formik";
import React, { useMemo, useState } from "react";

export default function RequestWithdrawalForm(
  props: FormikProps<IWithdrawalFormValues>,
) {
  const {
    values,
    handleChange,
    handleSubmit,
    errors,
    touched,
    setFieldValue,
    isSubmitting,
    setFieldTouched,
  } = props;

  const {
    account_name: account_nameErr,
    account_number: account_numberErr,
    bank_name: bank_nameErr,
    reason: reasonErr,
    additional_info: additional_infoErr,
    termsAccepted: termsAcceptedErr,
  } = errors;

  const {
    bank_name: bank_nameTouched,
    account_name: account_nameTouched,
    account_number: account_numberTouched,
    reason: reasonTouched,
    additional_info: additional_infoTouched,
    termsAccepted: termsAcceptedTouched,
  } = touched;

  const withdrawalOptions = useMemo(() => {
    return [
      { label: "End of Academic Session", value: "End of Academic Session" },
      { label: "Dropout", value: "Dropout" },
    ];
  }, []);

  return (
    <>
      <Box mb={5}>
        <Flex gap={5} flexDirection={{ base: "column", md: "row" }}>
          <Box flex={1}>
            <InputField
              label="Bank Name"
              name="bank_name"
              placeholder="My Bank"
              onChange={handleChange}
              value={values.bank_name}
              size="md"
            />
            <InputError error={bank_nameErr} touched={bank_nameTouched} />
          </Box>
          <Box flex={1}>
            <InputField
              label="Account Number"
              name="account_number"
              value={values.account_number}
              onChange={handleChange}
              placeholder="**********"
              isRequired
            />
            <InputError
              error={account_numberErr}
              touched={account_numberTouched}
            />
          </Box>
        </Flex>
      </Box>

      <Box mb={5}>
        <InputField
          label="Account Name"
          name="account_name"
          value={values.account_name}
          onChange={handleChange}
          placeholder="Emmanuel Okorie"
          isRequired
        />
        <InputError error={account_nameErr} touched={account_nameTouched} />
      </Box>

      <Box mb={5}>
        <Text paddingBottom={2} color="#0A3150" fontWeight={600} fontSize={14}>
          Reason For Withdrawal
        </Text>
        <CommonSelect
          label=""
          value={values.reason}
          showIndicatorIcon={true}
          options={withdrawalOptions || []}
          onChange={(value) => {
            setFieldTouched("reason", true);
            setFieldValue("reason", value);
          }}
          className="placeholder:text-color-[#5B758A] h-[47px]"
          placeholder="Select a reason"
        />
        <InputError error={reasonErr} touched={reasonTouched} />
      </Box>

      <Box mb={5}>
        <Text paddingBottom={2} color="#0A3150" fontWeight={600} fontSize={14}>
          Tell Us More
        </Text>
        <Textarea
          value={values.additional_info}
          height={"119px"}
          resize={"none"}
          onChange={handleChange}
        />
        <InputError
          error={additional_infoErr}
          touched={additional_infoTouched}
        />
      </Box>

      <Box mb={5} px={"12px"}>
        <Checkbox
          size={"md"}
          name="termsAccepted"
          onChange={handleChange}
          borderColor={BaseColor.PRIMARY}
          isChecked={values.termsAccepted}
          gap={"12px"}
        >
          <Text
            fontSize={14}
            fontWeight={500}
            lineHeight={"20px"}
            color={BaseColor.PRIMARY}
          >
            I understand that my withdrawal request will only be approved if it
            complies with the tuition policy, and that this request may be
            declined if I haven’t completed my final academic year.
          </Text>
        </Checkbox>
        <InputError error={termsAcceptedErr} touched={termsAcceptedTouched} />
      </Box>

      <Box justifyContent={"end"} display={"flex"}>
        <ButtonCTA
          type="submit"
          mt={"40px"}
          fontSize={12}
          height="auto"
          py={3}
          px={7}
          isLoading={isSubmitting}
          onClick={() => {
            handleSubmit();
          }}
        >
          <Text fontSize={14} fontWeight={600}>
            Send Request
          </Text>
        </ButtonCTA>
      </Box>
    </>
  );
}
