import { BaseColor } from "@/constants/colors";

export const formatEnrolmentStatus = (status: string) => {
  switch (status) {
    case "ENROLLED":
      return "Enrolled";
    case "SUSPENDED":
      return "Suspended";
    case "DEFERRED":
      return "Deferred";
    case "DISCONTINUE":
      return "Discontinue";
    default:
      return "";
  }
};

export const getColorScheme = (status: string): string => {
  switch (status.toLowerCase()) {
    case "accepted":
      return "green";
    case "enrolled":
      return "green";
    case "received":
      return "gray";
    case "pending review":
      return "yellow";
    case "enrollment pending":
      return "yellow";
    case "waiting list":
      return "yellow";
    case "incomplete documents":
      return "red";
    case "canceled":
      return "red";
    default:
      return "gray";
  }
};

export const getEnrollmentBaseColor = (status: string): BaseColor => {
  switch (status.toLowerCase()) {
    case "completed":
    case "enrolled":
      return BaseColor.SUCCESS;
    case "suspended":
    case "discontinue":
      return BaseColor.DANGER;
    case "deferred":
      return BaseColor.PRIMARY;
    default:
      return BaseColor.DEFAULT;
  }
};
