import { Dispatch, SetStateAction } from "react";
import { baseApi } from "../config/api";
import { MODULE_ROUTE, Routes } from "../config/routes";

export interface Receipt {
  receipt_id: string;
  receipt_number: string;
  student_id: string;
  payment_id: string;
  amount: number;
  currency: string;
  payment_method: string;
  description: string;
  status: string;
  created_at: string;
  updated_at: string;
  first_name: string;
  last_name: string;
  email: string;
  matric_no: {
    String: string;
    Valid: boolean;
  };
  miva_email: {
    String: string;
    Valid: boolean;
  };
}

export async function downloadReceipt({
  receipt_id,
  setLoading,
}: {
  receipt_id: string;
  setLoading: Dispatch<SetStateAction<boolean>>;
}): Promise<Blob> {
  setLoading(true);
  try {
    const response = await baseApi.get(
      Routes[MODULE_ROUTE.STUDENT].RECEIPTS.DOWNLOAD,
      {
        params: { receipt_id },
        responseType: "blob",
      },
    );
    return response.data;
  } catch (error) {
    console.error("Error downloading receipt:", error);
    throw error;
  }
}
