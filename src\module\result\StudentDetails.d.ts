export interface ContactInformation {
  country: string;
  city: string;
  email: string;
  phone_number: string;
  residential_address: string;
  state: string;
}
interface Course {
  course_code: string;
  course_id: string;
  course_name: string;
  created_at: string;
  credit_unit: number;
  grade_point: number;
  release_status: string;
  result: string;
  score: number;
  status: string;
  symbol: string;
  updated_at: string;
  group_type: string;
}
interface CourseSummary {
  completed: number;
  discontinue: number;
  enrolled: number;
  total_course_unit: number;
}
interface ProgrammeLevelEnrollmentSemester {
  gpa: number;
  semester_id: string;
  semester_name: string;
  semester_period: string;
  semester_type: string;
  total_credit_unit: number;
  total_grade_point: number;
  total_scores: number;
  courses: Course[];
}
export interface Summary {
  degree: string;
  last_grade_point: number;
  overall_cgpa: number;
  course_unit_summary: {
    completed_unit: number;
    total_course_unit: number;
    discontinue_unit: number;
    enrolled_unit: number;
    minimum_unit_required: number;
  };
}
export interface StudentDetails {
  studentID2: string;
  address: string;
  bundle_name: string;
  dob: string;
  email: string;
  enrollment_start_date: string;
  enrollment_status: string;
  faculty: string;
  full_name: string;
  gender: string;
  matric_no: string;
  profile: string;
  programme: string;
  studentID: string;
  phone: string;
}
export interface ProgrammeLevelEnrollments {
  academic_time_period_name: string;
  academic_time_period_type: string;
  cgpa: number;
  end_date: string;
  level: string;
  programme_intake_id: string;
  programme_level_enrollment_id: string;
  programme_level_id: string;
  required_units: number;
  start_date: string;
  status: string;
  total_credit_unit: number;
  total_grade_point: number;
  semesters: ProgrammeLevelEnrollmentSemester[];
}
export interface StudentResultDetails {
  programme_level_enrollment: ProgrammeLevelEnrollments[];
  course_status_summary: CourseSummary;
  student: StudentDetails;
  summary: Summary;
}
