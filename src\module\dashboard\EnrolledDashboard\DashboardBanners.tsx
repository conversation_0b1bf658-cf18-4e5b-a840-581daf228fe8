import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import { MIVA_SIS_PAYMENT_URL } from "@/constants/api";
import { Box, Skeleton } from "@chakra-ui/react";
import { StudentEnrollment } from "src/api/repository/Courses/courses.d";
import { usePayments } from "src/module/payments/usePayments";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import { DashboardBanner } from "../components/DashboardBanner";

export const DashboardBanners = ({
  currentEnrollment,
}: {
  currentEnrollment?: StudentEnrollment;
}) => {
  const student = useAuthStore((state) => state.student);
  const access_token = useAuthStore((state) => state.access_token);

  const enrollmentStatus = currentEnrollment?.status.toLowerCase();

  const { isLoading, filteredOutstandingTransactions } = usePayments();

  if (isLoading) {
    return <Skeleton my={8} height="60px" />;
  }

  return (
    <>
      {enrollmentStatus === "suspended" && (
        <Box mt={8}>
          <DashboardBanner
            image="/images/suspended.png"
            title="SUSPENDED ENROLLMENT"
            titleColor="#D3332D"
            desc={
              filteredOutstandingTransactions.length == 0
                ? "Your tuition has been paid! Reach out to your success advisor to resume your enrolment"
                : "Your enrolment has been suspended. Complete your payment to continue your learning."
            }
            descColor="#0A3150"
            buttonCTA={
              filteredOutstandingTransactions.length > 0 && (
                <ButtonCTA
                  onClick={() => {
                    const fullUrl = `${MIVA_SIS_PAYMENT_URL}/payment-with-installment?studentSisId=${student?.student_profile.student_id}&userToken=${access_token}&installmentId=${filteredOutstandingTransactions[0].installment_plan_id}`;
                    window.open(fullUrl, "_blank");
                  }}
                  fontSize={12}
                  height="auto"
                  py={4}
                  px={6}
                  color="white"
                  mt={5}
                  bg={"#E83831"}
                >
                  Make payment
                </ButtonCTA>
              )
            }
          />
        </Box>
      )}
      {/* Deferred Enrollment */}
      {enrollmentStatus === "deferred" && (
        <Box mt={8}>
          <DashboardBanner
            image="/images/deferred.png"
            title="ENROLLMENT DEFERRED"
            titleColor="#E83831"
            desc="You have deferred your enrolment.  Reach out to your Success Advisor for next steps."
            descColor="#0A3150"
          />
        </Box>
      )}
      {/* Discontinued Enrollment */}
      {enrollmentStatus === "discontinue" && (
        <Box mt={8}>
          <DashboardBanner
            image="/images/discontinued.png"
            title="ENROLLMENT DISCONTINUED"
            titleColor="#E83831"
            desc="Your enrolment has been discontinued. Reach out to your Success Advisor for next steps."
            descColor="#0A3150"
          />
        </Box>
      )}
    </>
  );
};
