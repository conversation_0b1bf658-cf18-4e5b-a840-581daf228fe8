import { useSearchParams } from "next/navigation";
import { useMemo } from "react";

export const useCASQueryParams = () => {
  const searchParams = useSearchParams();
  const service = searchParams.get("service") || undefined;
  const renew = searchParams.get("renew") ?? undefined;
  const gateway = searchParams.get("gateway") || undefined;
  const method = searchParams.get("method") || undefined;
  // Special support for moodles nonsense
  const url = searchParams.get("url") || undefined;

  const query = useMemo(() => {
    return {
      service,
      renew,
      gateway,
      method,
      url,
    };
  }, [service, renew, gateway, method, url]);

  const queryString = useMemo(() => {
    return new URLSearchParams(
      Object.entries(query)
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        .filter(([_, value]) => value !== undefined) // Filter out undefined values
        .map(([key, value]) => [key, String(value)]), // Ensure all values are strings
    ).toString();
  }, [query]);

  return {
    query,
    queryString: queryString ? `?${queryString}` : "",
  };
};
