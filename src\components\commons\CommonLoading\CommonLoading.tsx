import React from "react";
import { cn } from "@/lib/utils";
import { CommonLoadingProps } from "./CommonLoading.d";

const CommonLoading: React.FC<CommonLoadingProps> = ({
  size = "medium",
  message,
  className,
}) => {
  const sizeClasses = {
    small: "h-4 w-4",
    medium: "h-8 w-8",
    large: "h-12 w-12",
  };

  return (
    <div className={cn("flex flex-col items-center justify-center", className)}>
      <div
        className={`animate-spin rounded-full border-b-2 border-t-2 border-gray-500 ${sizeClasses[size]}`}
      ></div>
      {message && <p className="mt-2 text-sm text-gray-700">{message}</p>}
    </div>
  );
};

export default CommonLoading;
