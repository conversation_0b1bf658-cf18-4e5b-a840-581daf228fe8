{"extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "prettier", "next/core-web-vitals", "next"], "plugins": ["react", "@typescript-eslint", "prettier"], "env": {"browser": true, "es6": true, "jest": true}, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 6, "sourceType": "module"}, "rules": {"@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-unused-vars": "warn", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/ban-types": "off", "prettier/prettier": ["error", {"endOfLine": "auto"}], "no-console": ["error", {"allow": ["error"]}]}}