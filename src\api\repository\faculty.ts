import { APIResponse } from "src/api/config/api.d";
import { baseApi } from "src/api/config/api";
import { MODULE_ROUTE, Routes } from "src/api/config/routes";

interface FacultyStaff {
  user_id: string;
  user_name: string;
  faculty_id: string;
  faculty_staff_id: string;
  position: string;
}

interface FacultyMeta {
  modified_by: string;
  modified: string;
}

export interface Faculty {
  image: string;
  created_at: string;
  description: string;
  id: string;
  name: string;
  short_name: string;
  meta_data: FacultyMeta;
}

export interface MainFaculty {
  faculty: Faculty;
  faculty_staff: FacultyStaff[];
}

export const getFacultyById = async ({
  facultyId,
}: {
  facultyId: string;
}): Promise<APIResponse<MainFaculty>> => {
  try {
    const { data } = await baseApi.get<APIResponse<MainFaculty>>(
      Routes[MODULE_ROUTE.FACULTY].BY_ID,
      {
        params: {
          id: facultyId,
        },
      },
    );
    return data;
  } catch (error) {
    console.error("Error getting faculty:", error);
    throw error;
  }
};
