import { baseApi } from "../config/api";
import { MODULE_ROUTE, Routes } from "../config/routes";

export async function UploadImage(form: FormData, skipSigning?: string) {
  try {
    const response = await baseApi.post(
      Routes[MODULE_ROUTE.MISC].UPLOAD,
      form,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
        params: {
          skipSigning,
        },
      },
    );
    return response;
  } catch (error) {
    console.error("Error uploading image:", error);
    return error;
  }
}
