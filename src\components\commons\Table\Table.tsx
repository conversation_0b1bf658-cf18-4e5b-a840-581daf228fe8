"use client";

import { TableContainer, Table, Tfoot, Tr, Td } from "@chakra-ui/react";

import { cn } from "@/lib/utils";
import TableHeader from "./TableHeader";
import TableBody from "./TableBody";

import { ITableProps } from "./Table.d";

const CustomTable = ({
  titleList,
  pathList,
  data,
  isLoading,
  footer,
  variant = "simple",
  classHeader,
  classFooter,
}: ITableProps) => {
  return (
    <TableContainer className="custom-table">
      <Table variant={variant}>
        <TableHeader classHeader={classHeader} titleList={titleList} />
        <TableBody isLoading={isLoading} pathList={pathList} data={data} />
        {!!footer && !!pathList.length && (
          <Tfoot className={cn(classFooter)}>
            <Tr>
              <Td colSpan={pathList?.length}>{footer}</Td>
            </Tr>
          </Tfoot>
        )}
      </Table>
    </TableContainer>
  );
};
export default CustomTable;
