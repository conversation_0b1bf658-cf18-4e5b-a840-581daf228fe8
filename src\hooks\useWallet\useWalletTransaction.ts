// src/hooks/useWalletDashboard.ts
import { useEffect, useState } from "react";
import { getWalletTransactions } from "src/api/repository/wallet";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";

interface WalletTransaction {
  Id: string;
  WalletId: string;
  Amount: number;
  Currency: string;
  TransactionType: "CREDIT" | "DEBIT";
  PaymentMethod: string;
  Description: string;
  Status: string;
  CreatedAt: string;
  UpdateAt: string;
  ReferenceCode: string;
}

interface WalletTransactionsResponse {
  currentPage: number;
  nextPage: number | null;
  perPage: number;
  data: WalletTransaction[];
  total: number;
  totalPages: number;
}

interface UseWalletTransactionsReturn {
  data: WalletTransactionsResponse | null;
  loading: boolean;
  error: string | null;
  refetch: (sortByDate?: boolean) => Promise<void>;
}

export const useWalletTransactions = (
  page: number = 1,
  perPage: number = 10,
): UseWalletTransactionsReturn => {
  const [data, setData] = useState<WalletTransactionsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const student = useAuthStore((state) => state.student);
  const studentId = student?.personal_details?.id;

  const fetchData = async (sortByDate: boolean = false) => {
    try {
      if (!studentId) {
        throw new Error("Student ID not available");
      }

      setLoading(true);
      setError(null);

      const response = await getWalletTransactions({
        wallet_id: studentId,
        page,
        perPage,
        sort_by_date: sortByDate,
      });

      if (!response?.data) {
        throw new Error("No data received from server");
      }

      setData(response.data);
    } catch (err) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : "Failed to fetch wallet transactions";
      setError(errorMessage);
      console.error("useWalletTransactions error:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [studentId, page, perPage]);

  return { data, loading, error, refetch: fetchData };
};
