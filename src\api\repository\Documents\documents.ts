import { APIResponse } from "../../config/api.d";
import { baseApi } from "../../config/api";
import { MODULE_ROUTE, Routes } from "../../config/routes";
import { Document } from "./documents.d";

export async function getDocuments(): Promise<APIResponse<Document>> {
  try {
    const response = await baseApi.get(
      Routes[MODULE_ROUTE.STUDENT].DOCUMENT,
      {},
    );
    return response.data;
  } catch (error) {
    console.error("Error creating application:", error);
    throw error;
  }
}
