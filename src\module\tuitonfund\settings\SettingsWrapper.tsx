"use client";

import React from "react";
import { ChevronLeft } from "lucide-react";
import { Box, Flex, Text, useBreakpointValue } from "@chakra-ui/react";
import RequestWithdrawalWrapper from "./request-withdrawal/RequestWithdrawalWrapper";
import SettingsTabs from "./SettingsTabs";
import LinkedAccountSettings from "./LinkedAccountSettings";
import SecuritySettings from "./SecuritySettings";
export default function SettingsWrapper() {
  const [currentTab, setCurrentTab] = React.useState(0);
  const isMobile = useBreakpointValue({ base: true, md: false });
  const handleEventFromChild = (eventMessage: number) => {
    setCurrentTab(eventMessage);
  };
  return (
    <div className="mx-auto mt-[16px]">
      <Flex alignItems={"center"} className="text-[#0A3150]">
        {isMobile && currentTab !== 0 && (
          <div onClick={() => handleEventFromChild(0)}>
            <ChevronLeft size={20} className="mr-4" />
          </div>
        )}
        <Text as={"h2"} fontWeight={700} className="text-[20px] lg:text-[24px]">
          Tuition Fund Settings
        </Text>
      </Flex>
      <Box
        display={"flex"}
        gap={"20px"}
        marginTop={"28px"}
        flexDirection={{ base: "column", md: "row" }}
      >
        {(isMobile && currentTab === 0) || !isMobile ? (
          <Box
            width={{ base: "100%", md: "40%", lg: "30%" }}
            padding={"32px"}
            overflow={"hidden"}
            wordBreak={"break-word"}
            background={"#ffffff"}
            borderRadius={"16px"}
          >
            <SettingsTabs
              currentTab={currentTab}
              handleEventFromChild={handleEventFromChild}
            />
          </Box>
        ) : null}

        {(isMobile && currentTab !== 0) || !isMobile ? (
          <Box
            width={{ base: "100%", md: "60%", lg: "70%" }}
            borderRadius={"16px"}
            alignSelf={{ md: "flex-start" }}
          >
            {currentTab === 1 && <LinkedAccountSettings />}
            {currentTab === 2 && <SecuritySettings />}
            {currentTab === 3 && <RequestWithdrawalWrapper />}
          </Box>
        ) : null}
      </Box>
    </div>
  );
}
