import { FormikProps } from "formik";
import { Flex, Stack, Box } from "@chakra-ui/react";

import { IActivateAccountValue } from "./ActivateAccount";
import HeaderForm from "../components/HeaderForm";
import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import InputField from "@/components/commons/InputField/InputField";
import { InputError } from "@/components/commons/InputField/InputError";

const ActivateAccountForm = (props: FormikProps<IActivateAccountValue>) => {
  const {
    touched,
    errors,
    handleSubmit,
    handleChange,
    handleBlur,
    values,
    isSubmitting,
  } = props;
  const {
    confirm_password: confirmPasswordTouched,
    password: passwordTouched,
  } = touched;
  const { confirm_password: confirmPasswordError, password: passwordError } =
    errors;
  return (
    <Flex
      m={{ base: "20px" }}
      flexDir="column"
      width={{ md: "400px", base: "320px" }}
    >
      <HeaderForm
        title="Activate your account"
        subTitle="Create a password to activate your account"
      />
      <Stack mt="48px" spacing="16px">
        <Box>
          <InputField
            placeholder="Create your password"
            type="password"
            name="password"
            onChange={handleChange}
            onBlur={handleBlur}
            value={values.password}
            size="lg"
          />
          <InputError error={passwordError} touched={passwordTouched} />
        </Box>
        <Box>
          <InputField
            placeholder="Confirm your password"
            type="password"
            name="confirm_password"
            onChange={handleChange}
            onBlur={handleBlur}
            value={values.confirm_password}
            size="lg"
          />
          <InputError
            error={confirmPasswordError}
            touched={confirmPasswordTouched}
          />
        </Box>
      </Stack>
      <Stack mt="48px">
        <ButtonCTA onClick={() => handleSubmit()} isLoading={isSubmitting}>
          Activate
        </ButtonCTA>
      </Stack>
    </Flex>
  );
};

export default ActivateAccountForm;
