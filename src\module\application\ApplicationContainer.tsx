"use client";
import React, { FC, useMemo, useState } from "react";
import ApplicationCard from "./components/ApplicationCard";
import { Box, Text } from "@chakra-ui/react";

import { IApplicationContainerProps } from "./ApplicationContainer.d";
import { ApplicationResponse } from "src/api/repository/Application/application.d";
import { ArrowDown01, ArrowUp01 } from "lucide-react";
import { Button } from "@/components/ui/button";
import CommonLoading from "@/components/commons/CommonLoading/CommonLoading";

const ApplicationContainer: FC<IApplicationContainerProps> = (props) => {
  const studentName = props.studentName;
  const data = props.data;

  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [sortEnabled, setSortEnabled] = useState(false);

  const toggleSort = () => {
    setSortOrder((prev) => (prev === "asc" ? "desc" : "asc"));
    setSortEnabled(true);
  };

  const sortedApplications = useMemo(() => {
    if (!data || data.length === 0) return [];

    // Always return data in default sort order on initial load
    if (!sortEnabled) return [...data];

    return [...data].sort((a, b) => {
      const dateA = new Date(a.created_at).getTime();
      const dateB = new Date(b.created_at).getTime();
      return sortOrder === "asc" ? dateA - dateB : dateB - dateA;
    });
  }, [data, sortOrder, sortEnabled]);

  return (
    <Box mt="40px">
      <Text color="#0A3150" fontSize="24px" mb="40px" fontWeight="bold">
        Welcome, {studentName}
      </Text>
      <span className="flex justify-end font-bold">
        <Button
          variant="ghost"
          onClick={toggleSort}
          className="text-base text-[#0A3150]"
        >
          {sortOrder === "asc" ? (
            <>
              <ArrowUp01 className="mr-2 h-4 w-4" />
            </>
          ) : (
            <ArrowDown01 className="mr-2 h-4 w-4" />
          )}{" "}
          Sort
        </Button>
      </span>
      <>
        {props.loading ? (
          <Box
            borderRadius="16px"
            p={{ md: "32px", base: "16px" }}
            background="white"
            display="flex"
            justifyContent="center"
            alignItems="center"
            height={300}
          >
            <CommonLoading size="medium" />
          </Box>
        ) : (
          sortedApplications?.map((item: ApplicationResponse) => (
            <ApplicationCard
              {...props}
              item={item}
              loading={props.loading}
              key={item?.id}
            />
          ))
        )}
      </>
    </Box>
  );
};

export default ApplicationContainer;
