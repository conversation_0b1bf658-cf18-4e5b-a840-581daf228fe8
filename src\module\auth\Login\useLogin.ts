import { redirect, usePathname, useRouter } from "next/navigation";
import {
  ILoginResponse,
  ILoginValue,
  ILoginVerifyResponse,
  ILoginVerifyValue,
} from "./Login";
import { authApi } from "src/api/config/api";
import { APIResponse } from "src/api/config/api.d";
import { MODULE_ROUTE, Routes } from "src/api/config/routes";
import { useToast } from "@chakra-ui/react";
import axios from "axios";
import { extractAxiosError } from "@/lib/utils/helpers";
import { useEffect, useMemo, useState } from "react";
import {
  getCurrentUserAndStudent,
  useAuthStore,
} from "src/store/AuthenticationStore/authentication";
import { fetchEnrollment } from "../../../api/repository/Enrollment/enrollment";
import get from "lodash/get";
import { ENROLL_STATUS } from "./constants";
import { useCASValidation } from "../CAS/useCASValidation";
import { getUserByAccessToken, redirectToCASService } from "../CAS/cas.utils";
import { canVisitDashboard } from "src/utils/common";

export const useLogin = () => {
  const [showLoader, setShowLoader] = useState(true);

  const router = useRouter();
  const pathname = usePathname();

  const isCas = useMemo(() => pathname.includes("cas/"), [pathname]);

  const toast = useToast();
  const login = useAuthStore((state) => state.login);
  const access_token = useAuthStore((state) => state.access_token);
  const refresh_token = useAuthStore((state) => state.refresh_token);
  const isHydrated = useAuthStore((state) => state.isHydrated);
  const setUserInfo = useAuthStore((state) => state.setUserInfo);
  const setEnrollStatus = useAuthStore((state) => state.setEnrollStatus);
  const { service } = useCASValidation(setShowLoader);
  const [verifyEmail, setVerifyEmail] = useState<string>();

  const handleValidateStudentAndRedirectToDashboard = async (
    access_token: string,
    refresh_token: string,
    isSSo?: boolean,
  ): Promise<void> => {
    await handleValidateStudentRole(access_token);
    login(access_token, refresh_token);
    const { user, student, statusEnroll } = await handleProcessLogin();
    setUserInfo({ user, student });
    setEnrollStatus(statusEnroll);
    toast({
      description: isSSo ? "You are already logged in" : "Login Successful",
      status: "success",
    });
    if (
      student &&
      student.application_details &&
      canVisitDashboard(statusEnroll)
    ) {
      router.replace("/dashboard");
    } else {
      router.replace("/dashboard/application");
    }
  };

  const handleValidateStudentRole = async (access_token: string) => {
    const user = await getUserByAccessToken(access_token);
    if (user.group != "Students") {
      toast({
        title: "Access denied",
        description: "You cannot access the student sis without a student role",
        status: "error",
      });
      redirect("/login");
    }
  };

  const handleProcessLogin = async () => {
    const { student, user } = await getCurrentUserAndStudent();
    const enrollData = await fetchEnrollment({
      studentId: student?.student_profile?.user_id
        ? String(student.student_profile.user_id)
        : "",
    });
    const statusEnroll = get(enrollData, "data[0].status") || "";

    return {
      user,
      student,
      statusEnroll,
    };
  };

  const handleLogin = async ({ email, password }: ILoginValue) => {
    try {
      const res = await authApi.post<APIResponse<ILoginResponse>>(
        Routes[MODULE_ROUTE.AUTH].LOGIN,
        { email, password },
      );
      const access_token = get(res, "data.data.access_token") || "";
      const refresh_token = get(res, "data.data.refresh_token") || "";

      if (!access_token) {
        // User needs to use the token verify flow
        setVerifyEmail(email);
        toast({
          description:
            "We have just sent you an email with your verification code",
          status: "success",
        });
        return;
      }

      if (isCas) {
        await redirectToCASService({
          access_token,
          refresh_token,
          service,
        });
        // Store auth tokens to make sso possible for non CAS sessions
        login(access_token, refresh_token);
        return;
      }

      await handleValidateStudentAndRedirectToDashboard(
        access_token,
        refresh_token,
      );
    } catch (error) {
      if (axios.isAxiosError(error)) {
        toast({
          description: extractAxiosError(error),
          status: "error",
        });
      }
    }
  };

  const handleLoginVerify = async ({ code }: ILoginVerifyValue) => {
    try {
      const result = await authApi.post<APIResponse<ILoginVerifyResponse>>(
        Routes[MODULE_ROUTE.AUTH].LOGIN_VERIFY,
        { email: verifyEmail, code },
      );

      const access_token = get(result, "data.data.access_token") || "";
      const refresh_token = get(result, "data.data.refresh_token") || "";

      if (isCas) {
        await redirectToCASService({
          access_token,
          refresh_token,
          service,
        });
        // Store auth tokens to make sso possible for non CAS sessions
        login(access_token, refresh_token);
        return;
      }

      if (access_token) {
        await handleValidateStudentAndRedirectToDashboard(
          access_token,
          refresh_token,
        );
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        toast({
          description: extractAxiosError(error),
          status: "error",
        });
      }
    }
  };

  useEffect(() => {
    // Check if data is hydrated before checking for auth tokens, if present treat the user as authenticated
    if (isHydrated && !isCas && access_token && refresh_token) {
      void handleValidateStudentAndRedirectToDashboard(
        access_token,
        refresh_token,
        true,
      );
    } else if (isHydrated && !isCas) {
      // Data is hydrated and user is requesting a non CAS login session, remove the loader and show the login form
      setShowLoader(false);
    }
  }, [isHydrated]);

  return {
    handleLogin,
    handleLoginVerify,
    verifyEmail,
    showLoader,
  };
};
