import { APIResponse, Paginated } from "../../config/api.d";
import { baseApi } from "../../config/api";
import { MODULE_ROUTE, Routes } from "../../config/routes";
import { ApplicationResponse, StudentApplicationData } from "./application.d";

export const getApplication = async (
  id: string,
): Promise<StudentApplicationData> => {
  try {
    const response = await baseApi.get<APIResponse<StudentApplicationData>>(
      Routes[MODULE_ROUTE.MISC].GET_APPLICATION,
      {
        params: {
          id,
        },
      },
    );
    return response.data.data;
  } catch (error) {
    console.error("Error creating application:", error);
    throw error;
  }
};
export const getStudentApplications = async ({
  id,
  page,
  perPage,
}: {
  id: string;
  page: number;
  perPage: number;
}): Promise<APIResponse<Paginated<ApplicationResponse[]>>> => {
  try {
    const response = await baseApi.get(
      Routes[MODULE_ROUTE.APPLICATION].APPLICATION_LIST,
      {
        params: {
          page,
          perPage,
          id,
        },
      },
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching students:", error);
    throw error;
  }
};
