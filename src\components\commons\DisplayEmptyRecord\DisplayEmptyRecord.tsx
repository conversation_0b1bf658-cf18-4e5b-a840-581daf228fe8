import { FC } from "react";
import { Flex, Image, Text, Box } from "@chakra-ui/react";

import { IDisplayEmptyRecordProps } from "./DisplayEmptyRecord.d";

const DisplayEmptyRecord: FC<IDisplayEmptyRecordProps> = ({
  title,
  description,
  urlImage,
  children,
}) => {
  return (
    <Flex background="#F9FAFB" py="64px" flexDir="column" alignItems="center">
      <Image
        width="153px"
        height="154px"
        src={urlImage}
        alt="title"
        className=""
      />
      <Box p="16px">
        <Text
          textAlign="center"
          fontSize="24px"
          fontWeight="bold"
          color="#0A3150"
        >
          {title}
        </Text>
        <Text
          textAlign="center"
          fontSize="16px"
          fontWeight="500"
          color="#3B5A73"
        >
          {description}
        </Text>
      </Box>
      {children}
    </Flex>
  );
};

export default DisplayEmptyRecord;
