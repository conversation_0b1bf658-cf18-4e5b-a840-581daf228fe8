import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import { useCASService } from "../useCASService";

export const useLogout = () => {
  const router = useRouter();
  const logout = useAuthStore((state) => state.logout);
  const { service, urlDecodedService, casService, casServiceIsFetching, url } =
    useCASService();

  useEffect(() => {
    if (casServiceIsFetching) {
      return;
    }

    if (!casService && !url) {
      // Only authorized and known client applications should be able to use the CAS server
      router.push(`error/unauthorized`);
      return;
    }

    logout();
    router.push(
      `/cas/login?service=${service || `${url}/login/index.php?authCASattras=CASattras`}`,
    );
  }, [service, urlDecodedService, casService, casServiceIsFetching, url]);

  return {};
};
