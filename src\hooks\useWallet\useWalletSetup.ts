import { useMutation } from "@tanstack/react-query";
import { useToast } from "@chakra-ui/react";
import axios from "axios";
import { extractAxiosError } from "@/lib/utils/helpers";
import { setupWallet } from "../../api/repository/wallet";
import { WalletSetupRequest } from "../../api/repository/wallet.d";

interface UseWalletSetupOptions {
  onSuccess?: () => void;
}

export const useWalletSetup = (options?: UseWalletSetupOptions) => {
  const toast = useToast();

  const mutation = useMutation({
    mutationFn: (payload: WalletSetupRequest) => setupWallet(payload),
    onSuccess: (data) => {
      toast({
        description: data.message || "Wallet setup completed successfully",
        status: "success",
      });
      options?.onSuccess?.();
    },
    onError: (error) => {
      if (axios.isAxiosError(error)) {
        toast({
          description: extractAxiosError(error),
          status: "error",
        });
      } else {
        toast({
          description: "Failed to setup wallet. Please try again.",
          status: "error",
        });
      }
    },
  });

  return {
    setupWallet: mutation.mutate,
    isLoading: mutation.isPending,
    error: mutation.error,
    data: mutation.data,
    isSuccess: mutation.isSuccess,
  };
};
