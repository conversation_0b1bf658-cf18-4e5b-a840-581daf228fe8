import { APIResponse } from "../../config/api.d";
import { baseApi } from "../../config/api";
import { MODULE_ROUTE, Routes } from "../../config/routes";
import {
  ICourseOfferingParams,
  ICourseOfferingData,
  ICreateEnrollmentParams,
  IUpdatePreEnrollmentStatusParams,
  ActiveEnrollment,
  EnrollmentPaymentCompletion,
} from "./enrollment.d";
import { sortEnrollments } from "src/module/courses/utils/sortProgrammeEnrollments";
import { StudentEnrollment } from "../Courses/courses.d";

export async function getCourseOffering({
  programme_id,
  atp_id,
  level,
  programme_intake_id,
}: ICourseOfferingParams): Promise<APIResponse<ICourseOfferingData>> {
  try {
    const response = await baseApi.get(
      Routes[MODULE_ROUTE.MISC].GET_COURSE_OFFERING,
      {
        params: {
          programme_id,
          atp_id,
          level,
          programme_intake_id,
        },
      },
    );
    return response.data;
  } catch (error) {
    console.error("Error getCourseOffering error:", error);
    throw error;
  }
}

export async function createEnrollment({
  programme_id,
  programme_intake_id,
  student_id,
  semester_atp_id,
  course_offering_ids,
  is_moodle,
  application_id,
}: ICreateEnrollmentParams): Promise<APIResponse<ICourseOfferingData>> {
  try {
    const response = await baseApi.post(
      Routes[MODULE_ROUTE.ENROLLMENT].CREATE_ENROLLMENT,
      {
        programme_id,
        programme_intake_id,
        student_id,
        semester_atp_id,
        course_offering_ids,
        is_moodle,
        application_id,
      },
    );
    return response.data;
  } catch (error) {
    console.error("Error create enrollment error:", error);
    throw error;
  }
}

export const fetchEnrollment = async ({ studentId }: { studentId: string }) => {
  try {
    const response = await baseApi.get<APIResponse<StudentEnrollment[]>>(
      Routes[MODULE_ROUTE.STUDENT].COURSE_LIST,
      {
        params: {
          student_id: studentId,
          filter: "all",
        },
      },
    );
    if (response?.data.data?.length > 0) {
      response.data.data = sortEnrollments(response?.data?.data || []);
    }
    return response.data;
  } catch (error) {
    console.error("Error fetching courses:", error);
    throw error;
  }
};

export const courseEnrollment = async ({
  course_offering_id,
  intake_id,
  student_id,
  semester_atp_id,
  programme_id,
}: {
  course_offering_id: string;
  intake_id: string;
  student_id: string;
  semester_atp_id: string;
  programme_id: string;
  is_moodle: boolean;
}) => {
  try {
    const response = await baseApi.post(
      Routes[MODULE_ROUTE.ENROLLMENT].SEMESTER_ENROLLMENT,
      {
        course_offering_id: course_offering_id,
        intake_id: intake_id,
        student_id: student_id,
        semester_atp_id: semester_atp_id,
        programme_id: programme_id,
        is_moodle: true,
      },
    );
    return response.data;
  } catch (error) {
    console.error("Error enrolling courses:", error);
    throw error;
  }
};

export const updateEnrollmentStatus = async (
  params: IUpdatePreEnrollmentStatusParams,
) => {
  try {
    const response = await baseApi.patch<APIResponse<StudentEnrollment[]>>(
      `${Routes[MODULE_ROUTE.STUDENT].UPDATE_PRE_ENROLLMENT_STATUS}?enrollment_status=${params.status}`,
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching courses:", error);
    throw error;
  }
};

export const getActiveEnrollment = async () => {
  try {
    const response = await baseApi.get<APIResponse<ActiveEnrollment>>(
      Routes[MODULE_ROUTE.ENROLLMENT].ACTIVE_ENROLLMENT,
    );
    return response.data;
  } catch (error) {
    console.error("Error getting active enrolment:", error);
    throw error;
  }
};

export const getEnrollmentPaymentCompletion = async ({
  student_id,
  programme_intake_id,
}: {
  student_id: string;
  programme_intake_id: string;
}) => {
  try {
    const response = await baseApi.get<
      APIResponse<EnrollmentPaymentCompletion>
    >(Routes[MODULE_ROUTE.ENROLLMENT].PAYMENT_COMPLETION, {
      params: {
        studentID: student_id,
        intake: programme_intake_id,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error geting payment completion:", error);
    throw error;
  }
};
