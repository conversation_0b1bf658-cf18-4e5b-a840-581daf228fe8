import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { getActiveEnrollment } from "../../api/repository/Enrollment/enrollment";

export const useActiveEnrollment = () => {
  const query = useQuery({
    queryKey: ["getActiveEnrollment"],
    queryFn: () => getActiveEnrollment(),
    retry: false,
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
    select: (response) => {
      const data = response.data;
      return data;
    },
  });
  return query;
};
