import React from "react";
import { Box, Flex, Text } from "@chakra-ui/react";

import Icon from "@/components/commons/Icons/Icon";
import { EIconName } from "@/components/commons/Icons/Icon.enums";
import { BaseColor, EColor } from "@/constants/colors";
import { Summary } from "../StudentDetails";

const CardResult = ({ summary }: { summary: Summary }) => {
  const percentageChange =
    summary?.last_grade_point === 0
      ? 0
      : ((summary?.overall_cgpa - summary?.last_grade_point) /
          summary?.last_grade_point) *
        100;
  return (
    <Flex
      gap="20px"
      flexWrap="wrap"
      alignItems="stretch"
      justifyContent="space-between"
    >
      <Box
        w={["100%", "80%", "350px"]}
        p="24px"
        background="#ffffff"
        borderRadius="16px"
      >
        <Text color="#09314F" fontSize="14px" fontWeight="bold">
          Last Grade Point Average
        </Text>
        <Text color="#09314F" fontSize="56px" fontWeight="800">
          {summary?.last_grade_point
            ? summary?.last_grade_point.toFixed(2)
            : "-"}
        </Text>
        <Flex alignItems="center" justifyContent="flex-start">
          <Icon name={EIconName.ARROW_UP} color={EColor.TROPICAL_RAIN_FOREST} />
          <Flex gap="4px">
            <Text color="#00874E" fontSize="14px" fontWeight="bold">
              20%
            </Text>{" "}
            <Text fontSize="14px" color="#667085">
              {" "}
              vs last session
            </Text>
          </Flex>
        </Flex>
      </Box>
      <Box
        w={["100%", "80%", "350px"]}
        p="24px"
        background="#ffffff"
        borderRadius="16px"
      >
        <Text color="#09314F" fontSize="14px" fontWeight="bold">
          Completed Course Units
        </Text>
        <Flex alignItems="center" justifyContent="space-between">
          <Flex flexDir="column">
            <Text color="#009933" fontSize="56px" fontWeight="800">
              {summary?.course_unit_summary.completed_unit
                ? summary?.course_unit_summary.completed_unit
                : "-"}
            </Text>
            <Text color="#09314F" fontSize="14px" fontWeight="bold">
              Completed Units
            </Text>
          </Flex>
          <Flex flexDir="column">
            <Text color="#09314F" fontSize="56px" fontWeight="800">
              {summary?.course_unit_summary.minimum_unit_required
                ? summary?.course_unit_summary.minimum_unit_required
                : "-"}
            </Text>
            <Text color="#09314F" fontSize="14px" fontWeight="bold">
              Course Units
            </Text>
          </Flex>
        </Flex>
      </Box>
      <Box
        w={["100%", "80%", "350px"]}
        p="24px"
        background="#0A3150"
        borderRadius="16px"
      >
        <Text color="white" fontSize="14px" fontWeight="bold">
          Cumulative Grade Point Average
        </Text>
        <Text color="white" fontSize="56px" fontWeight="800">
          {summary?.overall_cgpa ? summary?.overall_cgpa.toFixed(2) : "-"}
        </Text>
        <Flex alignItems="center" justifyContent="flex-start">
          {percentageChange >= 0 ? (
            <Icon color={BaseColor.SUCCESS} name={EIconName.ARROW_UP} />
          ) : (
            <Icon color={BaseColor.DANGER} name={EIconName.ARROW_DOWN} />
          )}
          <Flex gap="4px">
            <Text
              color={
                percentageChange >= 0 ? BaseColor.SUCCESS : BaseColor.DANGER
              }
              fontSize="14px"
              fontWeight="bold"
            >
              {(summary?.overall_cgpa || summary?.last_grade_point) &&
                Math.abs(percentageChange).toFixed(2)}
              %
            </Text>{" "}
            <Text fontSize="14px" color="#B3BFC9">
              {" "}
              vs last session
            </Text>
          </Flex>
        </Flex>
      </Box>
    </Flex>
  );
};

export default CardResult;
