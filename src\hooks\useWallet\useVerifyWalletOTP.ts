import { useMutation } from "@tanstack/react-query";
import { useToast } from "@chakra-ui/react";
import axios from "axios";
import { extractAxiosError } from "@/lib/utils/helpers";
import { verifyWalletOTP } from "../../api/repository/wallet";
import { VerifyWalletOTPRequest } from "../../api/repository/wallet.d";

interface UseVerifyWalletOTPOptions {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export const useVerifyWalletOTP = (options?: UseVerifyWalletOTPOptions) => {
  const toast = useToast();

  const mutation = useMutation({
    mutationFn: (payload: VerifyWalletOTPRequest) => verifyWalletOTP(payload),
    onSuccess: (data) => {
      toast({
        description: data.message || "OTP verified successfully",
        status: "success",
      });
      options?.onSuccess?.();
    },
    onError: (error) => {
      const errorMessage = axios.isAxiosError(error)
        ? extractAxiosError(error)
        : error.message || "Failed to verify OTP. Please try again.";

      toast({
        description: errorMessage,
        status: "error",
      });
      options?.onError?.(errorMessage);
    },
  });

  return {
    verifyOTP: mutation.mutate,
    isLoading: mutation.isPending,
    error: mutation.error,
    data: mutation.data,
    isSuccess: mutation.isSuccess,
  };
};
