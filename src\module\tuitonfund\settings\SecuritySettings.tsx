"use client";

import { useState } from "react";
import {
  Box,
  VStack,
  useToast,
  Text,
  InputRightElement,
  IconButton,
} from "@chakra-ui/react";
import { Button } from "../../../components/ui/button";
import InputField from "@/components/commons/InputField/InputField";
import { Eye, EyeOff } from "lucide-react";
import { OtpVerificationModal } from "../components/OtpVerificationModal";

const SecuritySettings = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const currentUserPin = "123456";
  const [currentPin, setCurrentPin] = useState(currentUserPin);
  const [newPin, setNewPin] = useState("");
  const [confirmPin, setConfirmPin] = useState("");
  const [showNewPin, setShowNewPin] = useState(false);
  const [showConfirmPin, setShowConfirmPin] = useState(false);
  const [isOtpModalOpen, setIsOtpModalOpen] = useState(false);
  const [otp, setOtp] = useState("");
  const [otpError, setOtpError] = useState("");
  const toast = useToast();

  const handleOpenOtpModal = () => {
    if (!currentPin || currentPin.length !== 6) {
      toast({
        title: "Error",
        description: "Please enter your current 6-digit PIN",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    if (!newPin || newPin.length !== 6) {
      toast({
        title: "Error",
        description: "Please enter a valid 6-digit new PIN",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    if (newPin !== confirmPin) {
      toast({
        title: "Error",
        description: "New PINs do not match",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    setIsOtpModalOpen(true);
    setOtp(""); // Clear previous OTP
    setOtpError(""); // Clear previous OTP error
  };

  const handleCloseOtpModal = () => {
    setIsOtpModalOpen(false);
    setOtp("");
    setOtpError("");
  };

  const handleOtpSubmit = async () => {
    if (!otp || otp.length !== 6) {
      setOtpError("Please enter the 6-digit OTP");
      return;
    }

    setIsSubmitting(true);
    setOtpError("");

    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));

      if (otp === "123456") {
        toast({
          title: "Success",
          description: "PIN changed successfully",
          status: "success",
          duration: 3000,
          isClosable: true,
        });
        setIsEditing(false);
        handleCloseOtpModal();
        setCurrentPin(newPin);
        setNewPin("");
        setConfirmPin("");
      } else {
        setOtpError("OTP code is invalid or incorrect.");
        toast({
          title: "Error",
          description: "OTP verification failed.",
          status: "error",
          duration: 3000,
          isClosable: true,
        });
      }
    } catch (error) {
      setOtpError("Failed to verify OTP. Please try again.");
      toast({
        title: "Error",
        description: "Failed to change PIN. Please try again.",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleForgotPin = () => {
    toast({
      title: "Forgot PIN",
      description: "Please contact support to reset your PIN.",
      status: "info",
      duration: 5000,
      isClosable: true,
    });
  };

  return (
    <Box p={6} bg="white" borderRadius="lg" boxShadow="sm">
      <VStack spacing={4} align="stretch">
        {!isEditing ? (
          <>
            <Text
              fontSize="20px"
              fontWeight="bold"
              mb={6}
              textColor={"#0A3150"}
            >
              Security
            </Text>
            <InputField label="Pin" type="password" value="******" isReadOnly />
            <Box display="flex" justifyContent="flex-end">
              <Button variant="outline" onClick={() => setIsEditing(true)}>
                Change Pin
              </Button>
            </Box>
          </>
        ) : (
          <>
            <Text
              fontSize="20px"
              fontWeight="bold"
              mb={6}
              textColor={"#0A3150"}
            >
              Change PIN
            </Text>
            <InputField
              label="Current PIN"
              type="password"
              value={currentPin}
              isReadOnly
              maxLength={6}
              isRequired
            />

            <InputField
              label="Create PIN"
              description="Set a secure 6-digit PIN for your transactions"
              type={showNewPin ? "text" : "password"}
              value={newPin}
              onChange={(e) =>
                setNewPin(e.target.value.replace(/\D/g, "").slice(0, 6))
              }
              placeholder="Set a 6-Digit PIN"
              maxLength={6}
              isRequired
              rightElement={
                <InputRightElement>
                  <IconButton
                    aria-label={showNewPin ? "Hide pin" : "Show pin"}
                    variant="ghost"
                    onClick={() => setShowNewPin(!showNewPin)}
                    icon={showNewPin ? <EyeOff size={16} /> : <Eye size={16} />}
                  />
                </InputRightElement>
              }
            />

            <Box position="relative" mb={2}>
              <InputField
                label="Confirm PIN"
                description="Re-enter your new PIN for confirmation"
                type={showConfirmPin ? "text" : "password"}
                value={confirmPin}
                onChange={(e) =>
                  setConfirmPin(e.target.value.replace(/\D/g, "").slice(0, 6))
                }
                placeholder="Confirm pin"
                maxLength={6}
                isRequired
                rightElement={
                  <InputRightElement>
                    <IconButton
                      aria-label={showConfirmPin ? "Hide pin" : "Show pin"}
                      variant="ghost"
                      onClick={() => setShowConfirmPin(!showConfirmPin)}
                      icon={
                        showConfirmPin ? (
                          <EyeOff size={16} />
                        ) : (
                          <Eye size={16} />
                        )
                      }
                    />
                  </InputRightElement>
                }
              />
              <Button
                variant="link"
                size="sm"
                onClick={handleForgotPin}
                className="text-[#c4a889] hover:text-[#b39878]"
              >
                Forgot PIN?
              </Button>
            </Box>

            <Box mt={4} display="flex" justifyContent="end" gap={2}>
              <Button
                variant="outline"
                onClick={() => {
                  setIsEditing(false);
                  setCurrentPin(currentUserPin);
                  setNewPin("");
                  setConfirmPin("");
                }}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleOpenOtpModal}
                disabled={
                  isSubmitting ||
                  !currentPin ||
                  !newPin ||
                  !confirmPin ||
                  newPin !== confirmPin
                }
                className="bg-[#BB9E7F] px-6 text-white"
              >
                {isSubmitting ? "Saving..." : "Save"}
              </Button>
            </Box>
          </>
        )}
      </VStack>

      <OtpVerificationModal
        isOpen={isOtpModalOpen}
        onClose={handleCloseOtpModal}
        onConfirm={handleOtpSubmit}
        isSubmitting={isSubmitting}
        otp={otp}
        setOtp={setOtp}
      />
    </Box>
  );
};

export default SecuritySettings;
