"use client";
import { <PERSON>, EyeOff } from "lucide-react";
import { ChangeEvent, useState } from "react";
import {
  Box,
  VStack,
  useToast,
  Text,
  InputRightElement,
  IconButton,
} from "@chakra-ui/react";
import { Button } from "../../../components/ui/button";
import { PinResetModal } from "../components/PinResetModal";
import { useForgotPin } from "@/hooks/useForgetPin/useForgetPin";
import { useChangePin } from "@/hooks/useChangePin/useChangePin";
import InputField from "@/components/commons/InputField/InputField";
import { OtpVerificationModal } from "../components/OtpVerificationModal";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import CommonLoading from "@/components/commons/CommonLoading/CommonLoading";
import { useChangePinOTP } from "@/hooks/useChangePinOTP/useChangePinOTP";

const SecuritySettings = () => {
  const toast = useToast();
  const [otp, setOtp] = useState("");
  const user = useAuthStore((state) => state.user);
  const { mutateAsync: change_pin, isPending: change_pin_pending } =
    useChangePin();
  const { mutateAsync: forgot_password } = useForgotPin();
  const { mutateAsync: send_pin_otp } = useChangePinOTP();
  const [isEditing, setIsEditing] = useState(false);
  const [pin, setPin] = useState({
    otp: "",
    old_pin: "",
    new_pin: "",
    confirm_pin: "",
  });
  const [showNewPin, setShowNewPin] = useState(false);
  const [showConfirmPin, setShowConfirmPin] = useState(false);
  const [isOtpModalOpen, setIsOtpModalOpen] = useState(false);
  const [isForgotModalOpen, setIsForgotModalOpen] = useState(false);

  const handleCloseOtpModal = () => {
    setIsOtpModalOpen(false);
  };

  const inputChangeHandler = (e: ChangeEvent<HTMLInputElement>) => {
    setPin((prev) => ({
      ...prev,
      [e.target.name]: e.target.value.replace(/[^\d]/g, "").slice(0, 6),
    }));
  };
  const pinChangeHandler = async () => {
    const { old_pin, new_pin, confirm_pin, otp: verify_code } = pin;
    if (old_pin.length !== 6) {
      toast({
        title: "Change PIN",
        description: "Current pin is invalid",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } else if (new_pin.length !== 6) {
      toast({
        title: "Change PIN",
        description: "New pin must be 6 digits",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } else if (confirm_pin !== new_pin) {
      toast({
        title: "Change PIN",
        description: "Confirmation pin does not match new pin",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } else {
      await change_pin({
        old_pin,
        new_pin,
        user_id: user?.id as string,
        verify_code,
      });
    }
  };

  const handleForgotPin = async () => {
    setIsOtpModalOpen(true);

    await forgot_password({
      user_id: user?.id as string,
    });
  };

  const switchToEditing = async () => {
    await send_pin_otp({
      user_id: user?.id as string,
    });
    setIsEditing(true);
  };

  const pinSetHandler = (e: ChangeEvent<HTMLInputElement>) => {
    setPin((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  return (
    <Box p={6} bg="white" borderRadius="lg" boxShadow="sm">
      <VStack spacing={4} align="stretch">
        {!isEditing ? (
          <>
            <Text
              fontSize="20px"
              fontWeight="bold"
              mb={6}
              textColor={"#0A3150"}
            >
              Security
            </Text>
            <InputField label="Pin" type="password" value="******" isReadOnly />
            <Box display="flex" justifyContent="flex-end">
              <Button variant="outline" onClick={switchToEditing}>
                Change Pin
              </Button>
            </Box>
          </>
        ) : (
          <>
            <Text
              fontSize="20px"
              fontWeight="bold"
              mb={6}
              textColor={"#0A3150"}
            >
              Change PIN
            </Text>
            <InputField
              label="OTP"
              type="text"
              value={pin.otp}
              maxLength={6}
              isRequired
              onChange={pinSetHandler}
              name="otp"
              placeholder="Enter current pin"
            />
            <InputField
              label="Current PIN"
              type="password"
              value={pin.old_pin}
              maxLength={6}
              isRequired
              onChange={inputChangeHandler}
              name="old_pin"
              placeholder="Enter current pin"
            />

            <InputField
              label="Create PIN"
              description="Set a secure 6-digit PIN for your transactions"
              type={showNewPin ? "text" : "password"}
              value={pin.new_pin}
              maxLength={6}
              isRequired
              onChange={inputChangeHandler}
              name="new_pin"
              placeholder="Set a 6-Digit PIN"
              rightElement={
                <InputRightElement>
                  <IconButton
                    aria-label={showNewPin ? "Hide pin" : "Show pin"}
                    variant="ghost"
                    onClick={() => setShowNewPin(!showNewPin)}
                    icon={showNewPin ? <EyeOff size={16} /> : <Eye size={16} />}
                  />
                </InputRightElement>
              }
            />

            <Box position="relative" mb={2}>
              <InputField
                label="Confirm PIN"
                description="Re-enter your new PIN for confirmation"
                type={showConfirmPin ? "text" : "password"}
                value={pin.confirm_pin}
                maxLength={6}
                isRequired
                onChange={inputChangeHandler}
                name="confirm_pin"
                placeholder="Set a 6-Digit PIN"
                rightElement={
                  <InputRightElement>
                    <IconButton
                      aria-label={showConfirmPin ? "Hide pin" : "Show pin"}
                      variant="ghost"
                      onClick={() => setShowConfirmPin(!showConfirmPin)}
                      icon={
                        showConfirmPin ? (
                          <EyeOff size={16} />
                        ) : (
                          <Eye size={16} />
                        )
                      }
                    />
                  </InputRightElement>
                }
              />
              <Button
                variant="link"
                size="sm"
                onClick={handleForgotPin}
                className="text-[#c4a889] hover:text-[#b39878]"
              >
                Forgot PIN?
              </Button>
            </Box>

            <Box mt={4} display="flex" justifyContent="end" gap={2}>
              <Button
                variant="outline"
                onClick={() => {
                  setIsEditing(false);
                  setPin({
                    old_pin: "",
                    new_pin: "",
                    confirm_pin: "",
                    otp: "",
                  });
                }}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={pinChangeHandler}
                className="bg-[#BB9E7F] px-6 text-white"
              >
                {change_pin_pending ? <CommonLoading size="small" /> : "Save"}
              </Button>
            </Box>
          </>
        )}
      </VStack>

      <OtpVerificationModal
        isOpen={isOtpModalOpen}
        onClose={handleCloseOtpModal}
        otp={otp}
        setOtp={setOtp}
        submit={() => {
          setOtp("");
          setIsOtpModalOpen(false);
          setIsForgotModalOpen(true);
        }}
      />
      <PinResetModal
        isOpen={isForgotModalOpen}
        onClose={handleCloseOtpModal}
        otp={otp}
      />
    </Box>
  );
};

export default SecuritySettings;
