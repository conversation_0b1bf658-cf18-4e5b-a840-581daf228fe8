import React from "react";
import { useTuitionFund } from "../context/TuitionFundContext";
import { Cin<PERSON> } from "next/font/google";
import { cn } from "@/lib/utils";
import { useRouter } from "next/navigation";

type Props = {
  children: React.ReactNode;
};

const cinzel = Cinzel({
  subsets: ["latin"],
  weight: ["400", "700"], // use available weights
  variable: "--font-cinzel",
  display: "swap",
});

const TuitionFundSetupWrapper: React.FC<Props> = ({ children }) => {
  const { currentStep, getActiveStep } = useTuitionFund();

  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center lg:w-[840px]",
        cinzel.variable,
      )}
    >
      <div className="w-full">
        <div className="rounded-t-xl bg-[#0A3150] pb-24 pt-16 text-center text-white">
          <h1 className="mb-2 font-cinzel text-2xl font-bold lg:text-5xl">
            TUITION FUND SETUP
          </h1>
          <p className="font-medium text-blue-50">Let&apos;s get you started</p>
        </div>

        <div className="bg-gray-200 pb-8">
          <div className="flex w-full justify-center">
            <div className="mx-3 -mt-16 rounded-xl bg-white p-3 lg:w-3/4 lg:p-6">
              {currentStep > 1 && currentStep < 5 && (
                <div className="mb-10 mt-4">
                  <div className="relative flex items-center justify-between text-sm font-semibold">
                    {/* Background line */}
                    <div className="absolute left-1.5 right-1.5 top-[15px] h-[7px] -translate-y-1/2 bg-gray-200 lg:top-1/4"></div>

                    <div className="lg:top-1/6 absolute left-1.5 right-0 top-[13.7px] h-[7px] -translate-y-1/2">
                      <div
                        className="h-full bg-[#BB9E7F]"
                        style={{
                          width: getActiveStep() === 0 ? "0%" : "100%",
                        }}
                      ></div>
                    </div>

                    {/* Step circles and labels */}
                    {[0, 1].map((index) => (
                      <div
                        key={index}
                        className={cn("relative z-10 flex flex-col", {
                          "items-start": index === 0,
                          "items-end": index === 1,
                        })}
                      >
                        <div
                          className={`flex h-[24px] w-[24px] items-center justify-center rounded-full border-2 ${index <= getActiveStep() ? "border-[#BB9E7F] bg-[#BB9E7F] text-white" : "border-gray-200 bg-gray-200 text-gray-600"}`}
                        >
                          <span>{index + 1}</span>
                        </div>
                        <div className="mt-3 text-center text-gray-600 lg:text-left">
                          {index === 0 && "OTP Verification"}
                          {index === 1 && "Setup PIN"}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              {children}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TuitionFundSetupWrapper;
