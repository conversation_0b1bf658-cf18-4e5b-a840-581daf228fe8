import React, { SetStateAction, Dispatch } from "react";

import { BaseColor } from "@/constants/colors";
import { Box, HStack, useDisclosure, Text, VStack } from "@chakra-ui/react";
import { XIcon } from "lucide-react";
import Progressbar from "./ProgressBar";
const TuitionGoalModal = ({
  open,
  setOpen,
}: {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
}) => {
  return (
    <div className="relative flex items-center justify-center">
      <div
        className={`fixed bottom-0 left-0 right-0 top-0 z-[50] bg-black/40 ${open ? "visible" : "invisible"}`}
      />
      <div
        className={`absolute z-[60] w-11/12 bg-white lg:w-[711px] ${open ? "visible" : "invisible"}`}
      >
        <div className="bg-[#0A3150] bg-[url('/images/tuition-bg.png')] bg-cover bg-center bg-no-repeat px-3 py-2.5 bg-blend-normal lg:px-6 lg:py-5">
          <Box display="flex" justifyContent="end">
            <XIcon
              color="white"
              onClick={() => setOpen(!open)}
              className="cursor-pointer"
            />
          </Box>
          <VStack>
            <Text
              fontWeight={700}
              fontSize={[18, 20]}
              color="white"
              className=""
            >
              What is your Tuition Fund Goal?
            </Text>
            <Text
              fontSize={[24, 48]}
              fontWeight={700}
              color={BaseColor.DEFAULT}
            >
              ₦1,200,000.00
            </Text>
          </VStack>
        </div>
        <Box
          backgroundColor="white"
          px={["14px", "64px"]}
          py={["12px", "24px"]}
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          gap={[5, 10]}
        >
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            gap={5}
            className="w-full"
          >
            <Text
              fontWeight={500}
              fontSize={14}
              color={BaseColor.PRIMARY}
              className=""
              textAlign={["center", "start"]}
            >
              This is the total amount needed to cover your tuition completely.
            </Text>
            <Box className="mx-auto w-full lg:w-8/12">
              <HStack
                backgroundColor="#F0F2F4"
                px={["12px", "24px"]}
                py="6px"
                className="w-full"
              >
                <Text
                  fontWeight={500}
                  fontSize={[14, 16]}
                  color={BaseColor.PRIMARY}
                  className="w-6/12"
                >
                  Total Tuition
                </Text>
                <Text
                  fontWeight={500}
                  fontSize={[14, 16]}
                  color={BaseColor.PRIMARY}
                  className="w-6/12"
                  textAlign="end"
                >
                  ₦1,200,000.00
                </Text>
              </HStack>
              <HStack
                backgroundColor="#F0F2F4"
                px={["12px", "24px"]}
                py="6px"
                className="w-full"
              >
                <Text
                  fontWeight={500}
                  fontSize={[14, 16]}
                  color={BaseColor.PRIMARY}
                  className="w-6/12"
                >
                  Tuition Paid
                </Text>
                <Text
                  fontWeight={500}
                  fontSize={[14, 16]}
                  color={BaseColor.PRIMARY}
                  className="w-6/12"
                  textAlign="end"
                >
                  ₦0.00
                </Text>
              </HStack>
              <HStack
                backgroundColor={BaseColor.PRIMARY}
                px={["12px", "24px"]}
                py="6px"
                className="w-full"
              >
                <Text
                  fontWeight={700}
                  fontSize={[14, 16]}
                  color="white"
                  className="w-6/12"
                >
                  Tuition Fund Goal
                </Text>
                <Text
                  fontWeight={700}
                  fontSize={[14, 16]}
                  color="white"
                  className="w-6/12"
                  textAlign="end"
                >
                  ₦1,200,000.00
                </Text>
              </HStack>
            </Box>
          </Box>
          <Box display="flex" flexDirection="column" className="w-full" gap={2}>
            <Progressbar
              value={50}
              total={100}
              bar_color="bg-[#F0F2F4]"
              value_color={`bg-[${BaseColor.DEFAULT}]`}
            />
            <HStack
              justifyContent="space-between"
              flexDirection={["column", "row"]}
            >
              {" "}
              <Text
                fontWeight={500}
                fontSize={[12, 14]}
                color={BaseColor.PRIMARY}
              >
                Amount raised: ₦00.00
              </Text>
              <Text
                fontWeight={500}
                fontSize={[12, 14]}
                color={BaseColor.PRIMARY}
              >
                Amount Remaining: ₦1,200,000.00
              </Text>
            </HStack>
          </Box>
        </Box>
      </div>
    </div>
  );
};

export default TuitionGoalModal;
