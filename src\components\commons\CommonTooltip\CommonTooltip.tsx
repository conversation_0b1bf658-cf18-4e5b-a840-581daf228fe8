import { FC } from "react";

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

import { ICommonTooltip } from "./CommonTooltip.d";

const CommonTooltip: FC<ICommonTooltip> = ({
  children,
  descriptionTooltip,
}) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>{children}</TooltipTrigger>
        <TooltipContent>
          <p>{descriptionTooltip}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default CommonTooltip;
