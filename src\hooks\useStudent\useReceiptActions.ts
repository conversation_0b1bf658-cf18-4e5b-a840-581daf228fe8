"use cleint";
import { useMutation } from "@tanstack/react-query";
import { downloadReceipt } from "src/api/repository/receipt";
import { useToast } from "@chakra-ui/react";
import { saveAs } from "file-saver";
import { useState } from "react";

export const useDownloadReceipt = () => {
  const toast = useToast();
  const [loading, setLoading] = useState(false);
  const mutation = useMutation({
    mutationFn: async ({
      receipt_id,
      fileName,
      triggerDownload = false,
    }: {
      receipt_id: string;
      fileName: string;
      triggerDownload?: boolean;
    }) => {
      const blob = await downloadReceipt({ receipt_id, setLoading });

      if (triggerDownload) {
        saveAs(blob, fileName);
      }
      return blob;
    },
    onSuccess: () => {
      setLoading(false);
      toast({
        title: "Success",
        description: "Receipt downloaded successfully",
        status: "success",
        duration: 3000,
        isClosable: true,
        position: "top-right",
      });
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    onError: (error: any) => {
      setLoading(false);
      toast({
        title: "Error",
        description: error?.message || "Failed to download receipt",
        status: "error",
        duration: 3000,
        isClosable: true,
        position: "top-right",
      });
    },
  });

  return {
    downloadReceipt: mutation.mutate, // Mutation function to trigger download
    isLoading: loading, // Loading state
  };
};
