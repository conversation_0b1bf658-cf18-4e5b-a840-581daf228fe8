"use client";

import { Flex } from "@chakra-ui/react";

import { EVIEW_PORT } from "@/constants/enums";
import { TParamPageCommon } from "@/constants/types";
import RightImageBlock from "../components/RightImageBlock";
import ActivateAccount from "./ResendActivation";
import { useResendActivationEmail } from "../ResendActivationEmail/useResendActivationEmail";

const ResendActivationWrapper = ({ searchParams }: TParamPageCommon) => {
  const { handleSubmit, loading } = useResendActivationEmail();

  return (
    <Flex alignItems="center" gap={{ xl: "172px", md: "40px" }}>
      <ActivateAccount handleSubmit={handleSubmit} loading={loading} />
      {searchParams?.viewport !== EVIEW_PORT.MOBILE && <RightImageBlock />}
    </Flex>
  );
};

export default ResendActivationWrapper;
