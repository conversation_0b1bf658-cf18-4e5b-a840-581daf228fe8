/* eslint-disable @typescript-eslint/no-explicit-any */
import { baseApi } from "../config/api";
import { MODULE_ROUTE, Routes } from "../config/routes";
import { APIResponse } from "src/api/config/api.d";

export interface StudentInstallment {
  data: {};
  results: InstallmentResults[];
}
export interface InstallmentResults {
  due_date: string;
  amount_in_naira: number;
  amount_in_dollar: number;
  installment_name: string;
  fee_installment_id: string;
  installment_plan_id: string;
}
export const getStudentInstallment = async (): Promise<
  APIResponse<StudentInstallment>
> => {
  try {
    const { data } = await baseApi.get<APIResponse<StudentInstallment>>(
      Routes[MODULE_ROUTE.STUDENT].NEXT_INSTALLMENT,
    );
    return data;
  } catch (error) {
    console.error("Error fetching student dashboard:", error);
    throw error;
  }
};
