import { useProgrammeIntakeByLevel } from "@/hooks/useProgrammeIntake/useProgrammeIntakeByLevel";
import { useMemo } from "react";
import { sortAndFilterFutureIntakes } from "./sortIntakeUtil";

export const useNextProgrammeIntakesByLevelId = (levelId?: string) => {
  const { data, isFetching } = useProgrammeIntakeByLevel({
    programmeLevelId: levelId,
  });

  const intakes = useMemo(() => {
    const intakesSortedByCorhortNames = sortAndFilterFutureIntakes(data);

    return intakesSortedByCorhortNames.slice(0, 3);
  }, [data]);

  return { intakes, isFetching };
};
