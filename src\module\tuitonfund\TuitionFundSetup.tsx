"use client";

import React from "react";
import { Card } from "@/components/ui/card";
import OnboardingScreen from "./components/steps/OnboardingScreen";
import TermsConditionsScreen from "./components/steps/TermsConditionsScreen";
import OTPVerificationScreen from "./components/steps/OTPVerificationScreen";
import PINSetupScreen from "./components/steps/PINSetupScreen";
import SuccessScreen from "./components/steps/SuccessScreen";
import {
  TuitionFundProvider,
  useTuitionFund,
} from "./context/TuitionFundContext";

export type TuitionFundFormData = {
  otp: string;
  acceptedTerms: boolean;
  pin: string;
  confirmPin: string;
  currency: string;
};

export interface CompNav {
  isNextDisabled: () => boolean;
  handleNext: () => void;
  handlePrevious: () => void;
}

const TuitionFundContent: React.FC = () => {
  const { currentStep } = useTuitionFund();

  const steps = [
    { title: "Onboarding", component: OnboardingScreen },
    { title: "Terms & Conditions", component: TermsConditionsScreen },
    { title: "OTP Verification", component: OTPVerificationScreen },
    { title: "Setup PIN", component: PINSetupScreen },
    { title: "Success", component: SuccessScreen },
  ];

  const CurrentStepComponent = steps[currentStep].component;

  return (
    <div className="flex w-full flex-col items-center justify-center p-4">
      <Card>
        <div>
          <CurrentStepComponent />
        </div>
      </Card>
    </div>
  );
};

const TuitionFundSetup: React.FC = () => {
  return (
    <TuitionFundProvider>
      <TuitionFundContent />
    </TuitionFundProvider>
  );
};

export default TuitionFundSetup;
