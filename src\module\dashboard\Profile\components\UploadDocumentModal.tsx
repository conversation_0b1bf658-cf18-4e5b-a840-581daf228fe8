"use client";
import React, { useState } from "react";
import {
  Accordion,
  AccordionButton,
  AccordionItem,
  AccordionPanel,
  ModalBody,
  Image,
  Stack,
  Text,
  useToast,
  Spinner,
} from "@chakra-ui/react";
import { CommonModal } from "@/components/commons/CommonModal/CommonModal";
import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import { ChevronDown, Trash2 } from "lucide-react";
import { DocumentUploads } from "../Profile";
import { Input } from "@/components/ui/input";
import { useUpload } from "@/hooks/useUpload/useUpload";
import { extractAxiosError } from "@/lib/utils/helpers";
import axios from "axios";
import { baseApi } from "src/api/config/api";
import { MODULE_ROUTE, Routes } from "src/api/config/routes";
import { useMutation } from "@tanstack/react-query";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";

const docs = [
  {
    title: "NIN Slip",
    file_name: "",
    description: "",
    document_link: "",
  },
  {
    title: "JAMB Slip",
    file_name: "",
    description: "",
    document_link: "",
  },
  {
    title: "O’ Level Result",
    file_name: "",
    description: "",
    document_link: "",
  },
  {
    title: " A’ Levels Result or Professional Certificate ",
    file_name: "",
    description:
      "(Cambridge, International Baccalaureate, JUPEB, IJMB, ICAN, ACCA, CIPM,  Registered Nurse Certificate, and others)",
    document_link: "",
  },
  {
    title: "Diploma Certificate (NCE, ND, or HND)",
    file_name: "",
    description: "",
    document_link: "",
  },
  {
    title: "SIWES Acceptance Letter",
    file_name: "",
    description: "",
    document_link: "",
  },
  {
    title: "Transcript",
    file_name: "",
    description: "",
    document_link: "",
  },
  {
    title: "Degree Certificate or Postgraduate Diploma",
    file_name: "",
    description: "",
    document_link: "",
  },
  {
    title: "CV or Other Certificates",
    file_name: "",
    description: "",
    document_link: "",
  },
  {
    title: "Other",
    file_name: "",
    description: "",
    document_link: "",
  },
];
const UploadDocumentModal = ({
  open,
  onClose,
  refetch,
}: {
  open: boolean;
  onClose: () => void;
  refetch: () => void;
}) => {
  const toast = useToast();
  const [field, setField] = useState<string>("");
  const user = useAuthStore((state) => state);
  const { isLoading, handleFileChange, fileInputRef } = useUpload((file) =>
    setField(file),
  );
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
  const [uploads, setUploads] = useState<DocumentUploads[]>(docs);
  const [openIndex, setOpenIndex] = useState<number>(0);
  const onSuccess = () => {
    const resetUploads = uploads.map((upload) => ({
      ...upload,
      document_link: "",
      file_name: "",
    }));

    setUploads(resetUploads);
    setField(""); // Reset the field state
    setSelectedIndex(null);
    setLoading(false);
    onClose();
    toast({
      description: "Updated successfully",
      status: "success",
    });
    refetch();
  };
  const onError =
    (message = "Error Uploading Documents") =>
    (error: Error) => {
      setLoading(false);
      toast({
        title: message,
        description: axios.isAxiosError(error)
          ? extractAxiosError(error)
          : "Somehting went wrong",

        status: "error",
      });
    };
  const updateDocuments = useMutation({
    mutationFn: async (data: {
      document_file_type?: string;
      document_link?: string;
      document_type?: string;
      application_id?: string;
      user_id?: string;
    }) => {
      setLoading(true);
      const result = await baseApi.post(
        Routes[MODULE_ROUTE.MISC].DOCUMENT_UPLOAD,
        data,
      );
      return result.data;
    },
    onSuccess,
    onError: onError(),
  });
  const handleFileSelect = async (
    index: number,
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      handleFileChange(e);
      const newUploads = [...uploads];
      newUploads[index].file_name = file.name;
      newUploads[index].document_link = file;
      setUploads(newUploads);
      setSelectedIndex(index);
    }
  };
  const handleAccordionChange = (index: number) => {
    setOpenIndex(index);
  };
  const handleFileDelete = (index: number) => {
    const newUploads = [...uploads];
    newUploads[index] = {
      ...newUploads[index],
      file_name: "",
    };
    setUploads(newUploads);

    const fileInput = document.getElementById(
      `file-upload-${index}`,
    ) as HTMLInputElement;
    if (fileInput) {
      fileInput.value = "";
    }
  };
  const handleSubmit = async (index: number) => {
    if (index === selectedIndex && !isLoading) {
      if (selectedIndex === null) return;

      const selectedUpload = uploads[selectedIndex];
      const payload = {
        document_file_type: selectedUpload.file_name,
        document_link: field,
        document_type: selectedUpload.title,
        application_id: user?.student?.application_details.id,
        user_id: user?.user?.id,
      };
      updateDocuments.mutate(payload);
    }
  };
  return (
    <CommonModal title="Upload documents" isOpen={open} onClose={onClose}>
      <ModalBody>
        {" "}
        <Accordion
          defaultValue={openIndex}
          mt={2}
          allowToggle
          onChange={handleAccordionChange}
        >
          {uploads.map((upload, index) => (
            <AccordionItem
              key={index}
              py={1}
              px={2}
              mb={3}
              className="shadow-msmd rounded border border-[#0A3150]"
            >
              <AccordionButton
                px={0}
                py={4}
                _hover={{ background: "transparent" }}
                className="flex items-center justify-between gap-2"
              >
                <Text color={"#0A3150"} fontSize={"14px"} fontWeight={600}>
                  {upload?.title}
                </Text>

                <ChevronDown
                  size={16}
                  color="black"
                  className={`rounded-full ${openIndex === index ? "rotate-180" : ""}`}
                />
              </AccordionButton>
              <AccordionPanel p={0}>
                <div>
                  <label
                    htmlFor={`file-upload-${index}`}
                    className="mt-[16px] flex cursor-pointer flex-col items-center justify-center border border-[#EEF1F3] px-[24px] py-[32px]"
                  >
                    <Image
                      src="./images/icons/upload.svg"
                      alt="upload-img"
                      width={"40px"}
                    />
                    <Text color={"#667085"} fontWeight={500} fontSize={"14px"}>
                      or Drag and drop here or{" "}
                      <span
                        style={{
                          color: "red",
                          fontWeight: "bold",
                          cursor: "pointer",
                        }}
                      >
                        browse
                      </span>
                    </Text>
                    <Text fontSize={"14px"} color={"#667085"} fontWeight={500}>
                      JPG or PDF (max. 10MB)
                    </Text>
                    {upload.file_name !== "" && (
                      <div className="flex items-center gap-2 pt-2">
                        {isLoading ? (
                          selectedIndex === index &&
                          isLoading && (
                            <span className="flex items-center gap-2">
                              <Spinner />
                              <p className="text-sm font-bold">Uploading</p>
                            </span>
                          )
                        ) : (
                          <p className="text-xs text-green-500">
                            {upload.file_name}
                          </p>
                        )}

                        {!isLoading && (
                          <Trash2
                            color="red"
                            size={16}
                            onClick={() => handleFileDelete(index)}
                          />
                        )}
                      </div>
                    )}
                    <Input
                      type="file"
                      accept="image/jpeg,image/png,image/gif,image/webp,application/pdf"
                      id={`file-upload-${index}`}
                      className="hidden"
                      ref={fileInputRef}
                      onChange={(e) => handleFileSelect(index, e)}
                    />
                  </label>

                  <Stack
                    marginTop={"20px"}
                    display={"flex"}
                    alignItems={"flex-end"}
                  >
                    <ButtonCTA
                      disabled={!isLoading && loading}
                      onClick={() => handleSubmit(index)}
                      className="flex w-auto items-center justify-center gap-2 p-2 text-[14px] text-white"
                    >
                      {loading && <Spinner />}Save
                    </ButtonCTA>
                  </Stack>
                </div>
              </AccordionPanel>
            </AccordionItem>
          ))}
        </Accordion>
      </ModalBody>
    </CommonModal>
  );
};

export default UploadDocumentModal;
