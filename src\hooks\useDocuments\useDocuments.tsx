import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { getDocuments } from "../../api/repository/Documents/documents";
import { Document } from "../../api/repository/Documents/documents.d";
import { APIResponse } from "src/api/config/api.d";

export const useStudentDocuments = () => {
  const query = useQuery({
    enabled: true,
    queryKey: ["getDocuments"],
    queryFn: () => getDocuments(),
    retry: false,
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
    select: (response: APIResponse<Document>) => {
      const { data } = response;
      return {
        data,
      };
    },
  });
  return query;
};
