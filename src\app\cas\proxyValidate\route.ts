import { NextRequest } from "next/server";
import {
  getCasService,
  getCASTicket,
  getFailureResponse,
  getProxyValidationSuccessResponse,
  getUserByAccessToken,
  isTicketInvalid,
  refreshToken,
} from "src/module/auth/CAS/cas.utils";

export async function GET(req: NextRequest) {
  const reqUrl = req.url;
  const { searchParams } = new URL(reqUrl);
  const service = searchParams.get("service");
  const ticket = searchParams.get("ticket");
  const format = searchParams.get("format");

  if (!ticket || !service) {
    return getFailureResponse(
      "INVALID_REQUEST",
      "Ticket or service missing",
      format,
    );
  }

  const completed: string[] = [];
  try {
    const casTicket = await getCASTicket(ticket);
    const casService = await getCasService(casTicket.cas_service_id);

    if (!casService) {
      return getFailureResponse(
        "SERVICE_INVALID",
        "Service is invalid",
        format,
      );
    }
    completed.push("CAS Service is valid");

    if (casTicket.type == "PT") {
      completed.push("CAS ticket is PT");
      const urlDecodedService = decodeURIComponent(service);

      if (casTicket.backend_service !== urlDecodedService) {
        return getFailureResponse(
          "TICKET_SERVICE_MISMATCH",
          `Ticket is invalid for this service {${casTicket.backend_service} !== ${urlDecodedService}}`,
          format,
        );
      }
      if (await isTicketInvalid(casTicket, "PT")) {
        return getFailureResponse(
          "TICKET_EXPIRED",
          "Ticket is expired",
          format,
        );
      }
    } else {
      completed.push("CAS Tickey is " + casTicket.type);
      if (await isTicketInvalid(casTicket, undefined, false)) {
        return getFailureResponse(
          "TICKET_EXPIRED",
          "Ticket is expired",
          format,
        );
      }
    }

    completed.push("Fetching token details");
    const tokenDetails = await refreshToken(casTicket.refresh_token);
    completed.push("Fetching user details");
    const user = await getUserByAccessToken(tokenDetails.access_token);

    if (user) {
      completed.push("Responding");
      return getProxyValidationSuccessResponse(
        user,
        casService.service_url,
        format,
      );
    }
  } catch (e) {
    let errorMessage = "";
    if (typeof e === "string") {
      errorMessage = e.toUpperCase(); // works, `e` narrowed to string
    } else if (e instanceof Error) {
      errorMessage = e.message; // works, `e` narrowed to Error
    }
    return getFailureResponse(
      "INVALID_TICKET",
      `Ticket is invalid service - ${service}, ticket - ${ticket}, error - ${errorMessage}, completed - ${completed.join(" | ")}`,
      format,
    );
  }

  return getFailureResponse(
    "INTERNAL_ERROR",
    "Ticket could not be validated",
    format,
  );
}
