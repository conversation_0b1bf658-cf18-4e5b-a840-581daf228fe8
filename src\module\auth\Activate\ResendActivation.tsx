"use client";
import { useState } from "react";

// import { useRouter } from "next/navigation";
import { Flex, Text, Stack, Box, useToast } from "@chakra-ui/react";

import HeaderForm from "../components/HeaderForm";
import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";

const ActivateAccount = ({
  handleSubmit,
  loading,
}: {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  handleSubmit: any;
  loading: boolean;
}) => {
  // const router = useRouter();
  const [isResendDisabled, setIsResendDisabled] = useState(false);
  const toast = useToast();

  const handleResendCode = async () => {
    setIsResendDisabled(true);
    try {
      await handleSubmit();
      toast({
        description: "Email resent successfully",
        status: "success",
      });
    } catch (ex) {
      toast({
        description: "Failed to resend OTP. Please try again",
        status: "error",
      });
    }

    setTimeout(() => {
      setIsResendDisabled(false); // Re-enable the button after 30 seconds
    }, 10000);
  };

  return (
    <Flex
      m={{ base: "20px" }}
      flexDir="column"
      width={{ md: "400px", base: "320px" }}
    >
      <HeaderForm
        title="Resend Activation Email"
        subTitle="Click to resend activation email"
      />

      <Stack mt="48px">
        <Box
          display="flex"
          justifyContent="center"
          gap="10px"
          paddingBottom={"10px"}
        >
          <Text>Did receive email?</Text>
          <Text
            fontSize="sm"
            fontWeight={isResendDisabled ? "normal" : "bold"}
            as="button"
            color={isResendDisabled ? "grey.200" : "blue.500"}
            textDecoration={"underline"}
            onClick={() => {
              handleResendCode();
            }}
            cursor={isResendDisabled ? "not-allowed" : "pointer"}
            opacity={isResendDisabled ? 0.5 : 1}
          >
            Resend Email
          </Text>
        </Box>
        <ButtonCTA
          isLoading={loading}
          disabled={loading}
          onClick={() => {
            handleSubmit();
          }}
        >
          Send
        </ButtonCTA>
      </Stack>
    </Flex>
  );
};

export default ActivateAccount;
