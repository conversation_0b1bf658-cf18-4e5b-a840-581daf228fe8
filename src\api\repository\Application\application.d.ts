/* eslint-disable @typescript-eslint/no-explicit-any */
import { PreEnrollmentStatus } from "../Enrollment/enrollment";

export interface Programme {
  id: string;
  name: string;
  duration: number;
  duration_unit: string;
  type: string;
  description: string;
  programme_unit: number;
  department: string;
  original_tution_fee_per_session: number;
  discount_tution_fee_per_session: number;
  tution_fee_per_semester: number;
  maximum_credit_unit: number;
  minimum_credit_unit: number;
  image: string;
  created_at: string;
  updated_at: string;
  total_credit_required?: number;
  minimum_duration?: string;
}

export interface StudentApplicationResponse {
  status: string;
  message: string;
  data: StudentApplicationData;
}

export interface ProductDetails {
  created_at: string;
  meta_data: ProgrammeMetaData;
  updated_at: string;
  programme_intake_id: string;
  product_name: string;
  product_id: string;
  fee: FeeDetails[];
}

export interface StudentApplicationData {
  data: StudentApplication;
  products: any;
}

export interface FeeDetails {
  amount_in_dollar: number;
  amount_in_naira: number;
  created_at: string;
  fee_id: string;
  plans: Plan[];
}

export interface Installment {
  id: string;
  name: string;
  due_date: string;
  amount_in_naira: number;
  amount_in_dollar: number;
  created_at: string;
  updated_at: string;
}

export interface Plan {
  created_at: string;
  installment_name: string;
  installment_type: string;
  installments: Installment[];
  plan_id: string;
  updated_at: string;
}

export interface StudentApplication {
  academic_time_period: string;
  application_status: string;
  application_type: string;
  atp_name: string;
  created_at: string;
  current_course_of_study: string;
  current_faculty: string;
  current_university: string;
  email_of_registrar: string;
  faculty_id: string;
  faculty_name: string;
  id: string;
  level: string;
  level_id: string;
  name_of_registrar: string;
  programme: ProgrammeDetails;
  programme_id: string;
  programme_image: string;
  programme_intake_id: string;
  referral_code: string;
  semesters: SemesterDetails[];
  student_email: string;
  student_name: string;
  updated_at: string;
  user_id: string;
  enrollment_status: PreEnrollmentStatus;
  country: string;
  atp_enrollment_start_date: string;
  atp_enrollment_end_date: string;
}

export interface ProgrammeDetails {
  programme_id: string;
  name: string;
  duration: number;
  duration_unit: string;
  type: string;
  description: string;
  programme_unit: number;
  original_tution_fee_per_session: number;
  discount_tution_fee_per_session: number;
  tution_fee_per_semester: number;
  image: string;
  created_at: string;
  updated_at: string;
  bundle_name: string;
  bundle_id: string;
  programme_code: string;
  short_name: string;
  faculty_id: string;
  faculty_name: string;
  maximum_duration: string;
  minimum_duration: string;
  status: string;
  meta_data: ProgrammeMetaData;
}

export interface ProgrammeMetaData {
  modified_by: string;
  modified: string;
}

export interface SemesterDetails {
  created_at: string;
  semester_application_end_date: string;
  semester_application_start_date: string;
  semester_atp_code: string;
  semester_atp_id: string;
  semester_end_date: string;
  semester_enrolment_end_date: string;
  semester_enrolment_start_date: string;
  semester_exam_deferment_end_date: string;
  semester_exam_deferment_start_date: string;
  semester_name: string;
  semester_start_date: string;
  semester_status: string;
  semester_switch_enrollment_cut_off_date: string;
  semester_type: string;
  type: string;
  updated_at: string;
}

type Installment = {
  id: string;
  name: string;
  due_date: string;
  amount_in_naira: number;
  amount_in_dollar: number;
  created_at: string;
  updated_at: string;
};

type Plan = {
  created_at: string;
  installment_name: string;
  installment_type: string;
  installments: Installment[];
  plan_id: string;
  updated_at: string;
};

type Fee = {
  amount_in_dollar: number;
  amount_in_naira: number;
  created_at: string;
  fee_id: string;
  plans: Plan[];
  updated_at: string;
};

type ProductMetaData = {
  modified_by: string;
  modified: string;
};

type Product = {
  product_id: string;
  programme_intake_id: string;
  product_name: string;
  fee: Fee[];
  created_at: string;
  updated_at: string;
  meta_data: ProductMetaData;
};

type ProgrammeMetaData = {
  modified_by: string;
  modified: string;
};

type Programme = {
  programme_id: string;
  name: string;
  duration: number;
  duration_unit: string;
  type: string;
  description: string;
  programme_unit: number;
  original_tution_fee_per_session: number;
  discount_tution_fee_per_session: number;
  tution_fee_per_semester: number;
  image: string;
  created_at: string;
  updated_at: string;
  bundle_name: string;
  bundle_id: string;
  programme_code: string;
  short_name: string;
  faculty_id: string;
  faculty_name: string;
  maximum_duration: string;
  minimum_duration: string;
  number_of_semesters: number;
  status: string;
  meta_data: ProgrammeMetaData;
};

type ApplicationSemester = {
  created_at: string;
  semester_application_end_date: string;
  semester_application_start_date: string;
  semester_atp_code: string;
  semester_atp_id: string;
  semester_end_date: string;
  semester_enrolment_end_date: string;
  semester_enrolment_start_date: string;
  semester_exam_deferment_end_date: string;
  semester_exam_deferment_start_date: string;
  semester_name: string;
  semester_start_date: string;
  semester_status: string;
  semester_switch_enrollment_cut_off_date: string;
  semester_type: string;
  type: string;
  updated_at: string;
};

export interface ApplicationResponse {
  about_us: string;
  about_us_2: string;
  academic_time_period: string;
  application_status: string;
  application_type: string;
  atp_enrollment_end_date: string;
  atp_enrollment_start_date: string;
  atp_name: string;
  country: string;
  created_at: string;
  current_course_of_study: string;
  current_faculty: string;
  current_university: string;
  email_of_registrar: string;
  enrollment_status: string;
  faculty_id: string;
  faculty_name: string;
  id: string;
  level: string;
  level_id: string;
  name_of_registrar: string;
  products: Product;
  programme: Programme;
  programme_id: string;
  programme_image: string;
  programme_intake_id: string;
  referral_code: string;
  semesters: ApplicationSemester[];
  specialization: string;
  student_email: string;
  student_name: string;
  updated_at: string;
  user_id: string;
}
