"use client";
import LoginWrapper from "../../module/auth/Login/LoginWrapper";
import { TParamPageCommon } from "../../constants/types";
import CommonLoading from "@/components/commons/CommonLoading/CommonLoading";
import { Suspense } from "react";

const LoginPage = (props: TParamPageCommon) => {
  return (
    <Suspense fallback={<CommonLoading size="medium" />}>
      <div className="mt-4 flex justify-center">
        <LoginWrapper {...props} />
      </div>
    </Suspense>
  );
};

export default LoginPage;
