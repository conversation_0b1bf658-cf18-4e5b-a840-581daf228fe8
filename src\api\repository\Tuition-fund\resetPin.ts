import { baseApi } from "../../config/api";
import { MODULE_ROUTE, Routes } from "../../config/routes";

export interface IPinReset {
  confirm_pin: string;
  new_pin: string;
  user_id: string;
  verify_code: string;
}

export async function ResetPin(payload: IPinReset) {
  try {
    const response = await baseApi.post(
      Routes[MODULE_ROUTE.WALLET].RESET_PIN,
      payload,
    );
    return response.data;
  } catch (error) {
    return error;
  }
}
