import React from "react";

const Progressbar = ({
  value,
  total,
  value_color,
  bar_color,
}: {
  value: number;
  total: number;
  value_color: string;
  bar_color: string;
}) => {
  return (
    <div className="">
      <div className={`h-3 rounded-full ${bar_color}`}>
        <div
          className={`h-full rounded-full ${value_color}`}
          style={{
            width: value && total ? `${(value / total) * 100}%` : "0%",
            //transition: "all 0.5s ease-in-out",
          }}
        />
      </div>
    </div>
  );
};

export default Progressbar;
