/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { Image } from "@chakra-ui/react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import { Transaction } from "../../payments/Payments";
import { formatCurrency } from "@/lib/utils/helpers";
import { Button } from "@/components/ui/button";
import { MIVA_SIS_PAYMENT_URL } from "@/constants/api";

export function OustandingAlert({
  data,
  oldestOutstanding,
}: {
  data: any;
  oldestOutstanding: any;
}) {
  const user = useAuthStore((state) => state.student);

  const accessToken = useAuthStore.getState().access_token;
  const handleNavigate = (student_id: string, fee_installment_id: string) => {
    const url = `${MIVA_SIS_PAYMENT_URL}/payment-with-installment?studentSisId=${user?.student_profile.student_id}&userToken=${accessToken}&installmentId=${fee_installment_id}`;
    window.open(url, "_blank"); // Open in a new tab
  };
  return (
    <>
      {data?.map((item: Transaction) => (
        <Alert
          key={item.fee_installment_id}
          variant="destructive"
          className="mt-5 rounded-3xl border border-[#E83831] bg-[#FDEBEA]"
        >
          <AlertDescription className="flex items-center justify-between">
            <div className="flex justify-between gap-3">
              <Image
                src="./images/icons/notice.svg"
                alt="notice"
                width={"20px"}
                height={"20px"}
              />
              <p>
                You have an outstanding payment of{" "}
                <span className="font-semibold text-[#0A3150]">
                  {formatCurrency(
                    parseInt(item?.amount_in_naira),
                    item.currency.toUpperCase(),
                  )}
                </span>
              </p>
            </div>
            <div className="flex flex-col justify-between">
              <Button
                disabled={item !== oldestOutstanding}
                onClick={() =>
                  handleNavigate(item.student_id, item.fee_installment_id)
                }
                className={`rounded-[8px] p-[8px] text-[12px] font-bold text-[#ffffff] ${!item.outstanding ? "bg-[#E83831]" : "bg-[#103150]"}`}
              >
                Make Payment
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      ))}
    </>
  );
}
