import { useToast } from "@chakra-ui/react";
import { useQuery, keepPreviousData } from "@tanstack/react-query";
import { useEffect, useMemo } from "react";
import axios from "axios";
import { extractAxiosError } from "@/lib/utils/helpers";
import {
  getStudentDashboard,
  getStudentTransactions,
  getStudentNextInstallment,
  NextInstallment,
} from "../../api/repository/student";
import { IStudentTransactionParam } from "../../api/repository/student";
import { NextInstallmentResponse } from "../../api/repository/student";

export const useStudentDashboard = () => {
  const toast = useToast();

  const query = useQuery({
    queryKey: ["getStudentDashboard"],
    queryFn: () => getStudentDashboard(),
    retry: false,
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
    select: (response) => response.data,
  });

  useEffect(() => {
    if (query.error && axios.isAxiosError(query.error)) {
      toast({
        description: extractAxiosError(query.error),
        status: "error",
      });
    }
  }, [query.isError]);

  return { data: query.data, isLoading: query.isLoading };
};

export const useStudentTransactions = ({
  page,
  perPage,
}: IStudentTransactionParam) => {
  const toast = useToast();
  const query = useQuery({
    queryKey: ["getStudentTransactions", { page, perPage }],
    queryFn: () => getStudentTransactions({ page, perPage }),
    retry: false,
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
    select: (response) => {
      return response.data;
    },
  });

  useEffect(() => {
    if (query.error && axios.isAxiosError(query.error)) {
      toast({
        description: extractAxiosError(query.error),
        status: "error",
      });
    }
  }, [query.isError]);

  return { data: query.data, isLoading: query.isLoading };
};

export const useStudentNextInstallment = () => {
  const toast = useToast();

  const query = useQuery({
    queryKey: ["getStudentNextInstallment"],
    queryFn: getStudentNextInstallment,
    retry: false,
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
    select: (response) => response.data,
  });

  useEffect(() => {
    if (query.error && axios.isAxiosError(query.error)) {
      toast({
        description: extractAxiosError(query.error),
        status: "error",
      });
    }
  }, [query.isError]);

  const nextInstallment = useMemo(() => {
    // Ensure nextInstallmentData.results is properly handled
    const today = new Date();
    return (query.data?.results || [])
      ?.filter((installment) => new Date(installment.due_date) > today)
      ?.sort(
        (a, b) =>
          new Date(a.due_date).getTime() - new Date(b.due_date).getTime(),
      )[0];
  }, [query.data]);

  return {
    data: query.data as NextInstallmentResponse | undefined,
    isLoading: query.isLoading,
    nextInstallment,
  };
};
