import { IValidations } from "@/lib/utils/validation.d";
import { checkValueError, validateRequired } from "@/lib/utils/validation";
import { FormikBag, withFormik } from "formik";
import RequestWithdrawalForm from "./RequestWithdrawalForm";

const validateFields: IValidations<IWithdrawalFormValues> = {
  bank_name: [
    {
      validator: validateRequired,
      code: "Bank name is required",
    },
  ],
  account_number: [
    {
      validator: validateRequired,
      code: "Account number is required",
    },
  ],
  account_name: [
    {
      validator: validateRequired,
      code: "Account name is required",
    },
  ],
  reason: [
    {
      validator: validateRequired,
      code: "Reason for withdrawal is required",
    },
  ],
  termsAccepted: [
    {
      validator: (val) => val === true,
      code: "You must accept the terms",
    },
  ],
};

export const onSubmit = async (
  values: IWithdrawalFormValues,
  {
    setErrors,
    setSubmitting,
    props,
  }: FormikBag<IWithdrawalFormProps, IWithdrawalFormValues>,
) => {
  setSubmitting(true);
  try {
    await props.handleRequestSubmission(values);
  } catch (e: any) {
    setErrors(e);
  } finally {
    setSubmitting(false);
  }
};

const WithdrawalFormContainer = withFormik<
  IWithdrawalFormProps,
  IWithdrawalFormValues
>({
  mapPropsToValues: () => ({
    bank_name: "",
    account_number: "",
    account_name: "",
    reason: "",
    additional_info: "",
    requested_amount: 0,
    termsAccepted: false,
  }),
  validate: checkValueError(validateFields),
  handleSubmit: onSubmit,
  validateOnChange: true,
})(RequestWithdrawalForm);

export default WithdrawalFormContainer;
