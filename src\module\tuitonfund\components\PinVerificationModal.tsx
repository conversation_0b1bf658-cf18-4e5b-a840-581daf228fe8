import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  KeyboardEvent,
  useRef,
  useEffect,
  SetStateAction,
  Dispatch,
} from "react";
import { X, CheckCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { useToast } from "@chakra-ui/react";

interface PinVerificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isSubmitting: boolean;
  isSuccess: boolean;
  title?: string;
  description?: string;
  successMessage?: string;
  pin: string;
  setPin: Dispatch<SetStateAction<string>>;
}

export const PinVerificationModal = ({
  isOpen,
  onClose,
  onConfirm,
  isSubmitting,
  title = "Enter PIN",
  description = "Enter your PIN to save changes",
  pin,
  setPin,
}: PinVerificationModalProps) => {
  const toast = useToast();
  const pinArray = pin.split("").concat(Array(6).fill("")).slice(0, 6);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  useEffect(() => {
    inputRefs.current = inputRefs.current.slice(0, 6);
  }, []);

  const handlePinChange = (index: number, value: string) => {
    if (value && !/^\d$/.test(value)) return;

    const newPin = [...pinArray];
    newPin[index] = value;
    setPin(newPin.join(""));

    if (value === "") {
      if (index > 0) {
        inputRefs.current[index - 1]?.focus();
      }
      return;
    }

    if (value && index < 5) {
      setTimeout(() => {
        inputRefs.current[index + 1]?.focus();
      }, 0);
    }
  };

  const handleKeyDown = (index: number, e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Backspace" && !pinArray[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }

    if (e.key === "ArrowRight" && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }

    if (e.key === "ArrowLeft" && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex h-screen items-center justify-center bg-black/70">
      <div className="relative w-[90%] w-full max-w-md rounded-md bg-white p-6 lg:w-[530px]">
        <button
          onClick={onClose}
          disabled={isSubmitting}
          className="absolute right-4 top-4 text-gray-500 hover:text-gray-700 disabled:opacity-50"
        >
          <X size={18} />
        </button>

        <>
          <h2 className="mb-1 text-3xl font-bold text-[#0A3150]">{title}</h2>
          <p className="mb-6 text-sm font-medium text-[#0A3150]">
            {description}
          </p>

          <div className="mb-6 flex justify-center gap-3">
            {Array(6)
              .fill(0)
              .map((_, index) => (
                <Input
                  key={index}
                  type="text"
                  inputMode="numeric"
                  pattern="[0-9]*"
                  maxLength={1}
                  value={pinArray[index] || ""}
                  onChange={(e) => {
                    if (e.target.value === "" || /^\d$/.test(e.target.value)) {
                      handlePinChange(index, e.target.value);
                    }
                  }}
                  onPaste={(e) => {
                    e.preventDefault();
                    const pastedData = e.clipboardData.getData("text/plain");
                    const numbers = pastedData.replace(/\D/g, "").slice(0, 6);
                    setPin(numbers);
                    const lastIndex = Math.min(index + numbers.length, 5);
                    setTimeout(() => {
                      inputRefs.current[lastIndex]?.focus();
                    }, 0);
                  }}
                  onKeyDown={(e) => handleKeyDown(index, e)}
                  ref={(el) => {
                    inputRefs.current[index] = el;
                  }}
                  disabled={isSubmitting}
                  className={cn(
                    "h-12 w-12 rounded-[8px] text-center text-lg font-medium",
                    "focus:ring-0 focus:ring-offset-0",
                    "border-gray-300",
                    isSubmitting && "opacity-70",
                  )}
                />
              ))}
          </div>

          <Button
            onClick={() => {
              if (pinArray.filter(Boolean).length === 6) {
                toast({
                  title: "Success",
                  description: "Changes saved",
                  status: "success",
                  duration: 3000,
                  isClosable: true,
                });
                onConfirm();
              }
            }}
            disabled={isSubmitting || pinArray.filter(Boolean).length !== 6}
            className="w-full rounded bg-[#c4a889] py-2 font-medium text-white hover:bg-[#b39878] disabled:opacity-70"
          >
            {isSubmitting ? "Verifying..." : "Done"}
          </Button>

          <p className="mt-4 text-center text-sm text-[#0A3150]">
            Have any issues? Contact support at{" "}
            <a
              href="mailto:<EMAIL>"
              className="font-bold hover:underline"
            >
              <EMAIL>
            </a>
          </p>
        </>
      </div>
    </div>
  );
};
