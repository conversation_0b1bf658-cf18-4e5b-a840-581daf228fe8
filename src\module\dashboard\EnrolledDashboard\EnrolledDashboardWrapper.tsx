/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import PageTitle from "@/components/commons/PageTitle/PageTitle";
import TransactionTable from "@/components/commons/Table/TransactionTable";
import { BaseColor } from "@/constants/colors";
import { useStudentDashboard } from "@/hooks/useStudent/useStudentDashboard";
import { Box, Flex, SimpleGrid, Skeleton, Stack, Text } from "@chakra-ui/react";
import { useMemo } from "react";
import DashboardSection from "../components/DashboardSection";
import DashboardTitle from "../components/DashboardTitle";
import { OustandingAlert } from "./OutstandingAlert";

import { useStudentCourse } from "@/hooks/useStudent/useStudentCourse";
import { ProgrammeLevel } from "src/api/repository/Courses/courses.d";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import { DashboardSemesterWrapper } from "../../courses/components/DashboardSemesterWrapper";
import {
  formatEnrolmentStatus,
  getEnrollmentBaseColor,
} from "../../courses/utils/CourseOptions";
import { setTagBgColor } from "../../payments/payments.utils";
import { usePayments } from "../../payments/usePayments";
import { NewSessionBanner } from "src/module/payments/NewSessionBanner";
import { useNewSession } from "src/module/payments/useNewSession";
import { DashboardBanners } from "./DashboardBanners";
import { sortProgrammeLevelEnrollments } from "src/module/courses/utils/sortProgrammeEnrollments";

const EnrolledDashboardWrapper = () => {
  const user = useAuthStore((state) => state.user);
  const { data } = useStudentDashboard();

  const studentId: string = user?.id ?? "";
  const { data: studentEnrollments, isLoading: isLoadingCourse } =
    useStudentCourse({
      studentId,
    });

  const mostRecentLevel = useMemo(() => {
    return sortProgrammeLevelEnrollments(
      (studentEnrollments || [])[0]?.levels || [],
    )?.findLast((l) => !!l.programme_intake_id);
  }, [studentEnrollments]);

  const currentEnrollment = useMemo(() => {
    if (Array.isArray(studentEnrollments)) {
      return studentEnrollments[0];
    }
    return {
      programme_name: "",
      status: "",
    };
  }, [studentEnrollments]);

  const otherDetails = {
    student_id: studentEnrollments?.[0]?.student_id ?? "",
    status: studentEnrollments?.[0]?.status ?? "",
    programme_id: studentEnrollments?.[0]?.programme_id ?? "",
    enrollment_end_date: studentEnrollments?.[0]?.enrollment_end_date ?? "",
  };

  const {
    isLoading: isLoadingTransactions,
    filteredOutstandingTransactions,
    oldestOutstanding,
    allTransactions,
  } = usePayments();
  const { newSessionIsActive, isLoadingActiveEnrollment } = useNewSession();

  return (
    <Box>
      <PageTitle> Welcome, {user?.biography?.first_name}</PageTitle>
      {filteredOutstandingTransactions ? (
        <OustandingAlert
          data={filteredOutstandingTransactions?.splice(0, 2)}
          oldestOutstanding={oldestOutstanding}
        />
      ) : (
        ""
      )}

      <SimpleGrid columns={{ base: 1, md: 2, xl: 3 }} spacing="20px">
        <DashboardSection flexGrow={1}>
          <Text fontWeight={700} fontSize={14} color={BaseColor.PRIMARY}>
            Last Grade Point Average
          </Text>
          <Text fontWeight={900} fontSize={56} color={BaseColor.PRIMARY} mt={4}>
            {data?.summary?.last_grade_point.toFixed(2)}
          </Text>
          <Flex flexDir="row" alignItems="center" gap={1}>
            <Text
              fontWeight={600}
              fontSize={14}
              color={BaseColor.SUCCESS}
              display="flex"
              alignItems="center"
            >
              <Text as="span" fontSize={20}>
                <i className="uil-arrow-up"></i>
              </Text>
              0%
            </Text>
            <Text fontWeight={600} fontSize={14} color={BaseColor.PRIMARY_300}>
              vs last session
            </Text>
          </Flex>
        </DashboardSection>
        <DashboardSection flexGrow={1}>
          <Text fontWeight={700} fontSize={14} color={BaseColor.PRIMARY}>
            Completed Course Units
          </Text>
          <Flex flexDir="row" justifyContent="space-between">
            <Box pr={3}>
              <Text
                fontWeight={900}
                fontSize={56}
                color={BaseColor.SECONDARY}
                mt={4}
              >
                {data?.summary?.course_unit_summary?.completed_unit}
              </Text>
              <Flex flexDir="row" alignItems="center" gap={1}>
                <Text
                  fontWeight={600}
                  fontSize={14}
                  color={BaseColor.PRIMARY}
                  display="flex"
                  alignItems="center"
                >
                  <Text as="span" fontSize={20}>
                    <i className="uil-arrow-down"></i>
                  </Text>
                  {data?.summary?.course_unit_summary?.total_course_unit}
                </Text>
                <Text
                  fontWeight={600}
                  fontSize={14}
                  color={BaseColor.PRIMARY_300}
                >
                  {data?.summary?.total_grade_points} units
                </Text>
              </Flex>
            </Box>
            <Box>
              <Text
                fontWeight={900}
                fontSize={56}
                color={BaseColor.PRIMARY}
                mt={4}
              >
                {data?.summary?.course_unit_summary?.minimum_unit_required}
              </Text>
              <Text
                fontWeight={600}
                fontSize={14}
                color={BaseColor.PRIMARY_300}
                height="30px"
                display="flex"
                alignItems="center"
              >
                Minimum units required
              </Text>
            </Box>
          </Flex>
        </DashboardSection>
        <DashboardSection bg={BaseColor.PRIMARY} color="white" flexGrow={1}>
          <Text fontWeight={700} fontSize={14}>
            Cumulative Grade Point Average
          </Text>
          <Text fontWeight={900} fontSize={56} mt={4}>
            {data?.summary?.overall_cgpa.toFixed(2)}
          </Text>
          <Flex flexDir="row" alignItems="center" gap={1}>
            <Text
              fontWeight={600}
              fontSize={14}
              display="flex"
              alignItems="center"
            >
              <Text as="span" fontSize={20}>
                <i className="uil-arrow-up"></i>
              </Text>
              0%
            </Text>
            <Text fontWeight={600} fontSize={14} color={BaseColor.PRIMARY_100}>
              vs last session
            </Text>
          </Flex>
        </DashboardSection>
      </SimpleGrid>
      <NewSessionBanner />
      {!newSessionIsActive && !isLoadingActiveEnrollment && (
        <>
          {isLoadingCourse ? (
            <Stack p="24px" background="white">
              <Skeleton height="20px" />
              <Skeleton height="20px" />
              <Skeleton height="20px" />
            </Stack>
          ) : (
            <DashboardSection>
              <div>
                <DashboardTitle
                  title={currentEnrollment?.programme_name}
                  tag={formatEnrolmentStatus(currentEnrollment?.status)}
                  baseColor={getEnrollmentBaseColor(currentEnrollment?.status)}
                />
                <DashboardBanners currentEnrollment={studentEnrollments?.[0]} />
                {["enrolled", "completed"].includes(
                  currentEnrollment?.status.toLowerCase(),
                ) &&
                  mostRecentLevel && (
                    <DashboardSemesterWrapper
                      semesters={mostRecentLevel?.semesters || []}
                      otherDetails={otherDetails}
                      activeLevel={mostRecentLevel}
                    />
                  )}
              </div>
            </DashboardSection>
          )}
        </>
      )}
      <DashboardSection>
        <DashboardTitle title="Recent Transactions" />
        <Box mt={8}>
          <TransactionTable
            oldestOutstanding={oldestOutstanding}
            transactions={allTransactions}
            baseColor={BaseColor.PRIMARY}
            setTagBgColor={setTagBgColor}
            tableBaseColor={BaseColor.PRIMARY}
            tableRowBaseColor={BaseColor.PRIMARY}
            isLoading={isLoadingTransactions}
          />
        </Box>
      </DashboardSection>
    </Box>
  );
};

export default EnrolledDashboardWrapper;
