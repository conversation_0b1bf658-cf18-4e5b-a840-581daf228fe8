"use client";
import { FormikBag, withFormik } from "formik";
import ActivateAccountForm from "./ActivateAccountForm";
import {
  IActivateAccountContainerProps,
  IActivateAccountValue,
} from "./ActivateAccount";
import { checkValueError, validateRequired } from "@/lib/utils/validation";
import { IValidations } from "@/lib/utils/validation.d";

const validateFields: IValidations<IActivateAccountValue> = {
  password: [
    {
      validator: validateRequired,
      code: "This field is required",
    },
  ],
  confirm_password: [
    {
      validator: validateRequired,
      code: "This field is required",
    },
  ],
};

export const onSubmit = async (
  values: IActivateAccountValue,
  {
    setErrors,
    props,
    setSubmitting,
  }: FormikBag<IActivateAccountContainerProps, IActivateAccountValue>,
) => {
  setSubmitting(true);
  try {
    await props.handleSubmit(values);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (e: any) {
    setErrors(e);
  }
};

const ActivateAccountContainer = withFormik<
  IActivateAccountContainerProps,
  IActivateAccountValue
>({
  mapPropsToValues: () => {
    return { password: "", confirm_password: "" };
  },
  validate: checkValueError(validateFields),
  handleSubmit: onSubmit,
})(ActivateAccountForm);

export default ActivateAccountContainer;
