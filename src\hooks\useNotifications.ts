import {
  keepPreviousData,
  useQuery,
  useQueryClient,
  useMutation,
} from "@tanstack/react-query";
import {
  getNotifications,
  markAllAsRead,
  markAsRead,
} from "../api/repository/Notification/notification";

export const useNotifications = () => {
  // const [pagination, setPagination] = useState<TPagination>({
  //   pageIndex: page - 1,
  //   pageSize: perPage,
  // });
  const notificationsQuery = useQuery({
    queryKey: ["notifications"],
    queryFn: getNotifications,
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
    select: (response) => {
      const {
        data,
        currentPage,
        nextPage,
        perPage,
        prevPage,
        total,
        totalPages,
      } = response.data;
      return {
        data,
        meta: {
          currentPage,
          nextPage,
          perPage,
          prevPage,
          total,
          totalPages,
        },
      };
    },
  });

  const queryClient = useQueryClient();

  const markAllAsReadMutation = useMutation({
    mutationFn: markAllAsRead,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["notifications"] });
    },
  });

  const markAsReadMutation = useMutation({
    mutationFn: markAsRead,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["notifications"] });
    },
  });

  return {
    data: notificationsQuery.data,
    isLoading: notificationsQuery.isLoading,
    error: notificationsQuery.error,
    markAllAsRead: markAllAsReadMutation.mutate,
    isMarkingAllAsRead: markAllAsReadMutation.isPending,
    markAsRead: markAsReadMutation.mutate,
    isMarkingAsRead: markAsReadMutation.isPending,
  };
};
