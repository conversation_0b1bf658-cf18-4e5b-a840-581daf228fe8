"use client";
import React, { useState } from "react";
import AddFundsModal from "./AddFundsModal";
import {
  EIconName,
  EIconSize,
  TypeIcon,
} from "@/components/commons/Icons/Icon.enums";
import { BaseColor } from "@/constants/colors";
import { Button } from "@/components/ui/button";
import TuitionGoalModal from "./TuitionGoalModal";
import Icon from "@/components/commons/Icons/Icon";
import { PlusIcon, EyeOffIcon, EyeIcon } from "lucide-react";
import { Box, HStack, useDisclosure, Text, VStack } from "@chakra-ui/react";
import CopyToClipboard from "@/components/commons/CopyToClipboard/CopyToClipboard";
import Actions from "./Actions";
import Link from "next/link";

const TuitionFundHeader = () => {
  const [visible, setVisible] = useState(true);
  const [open, setOpen] = useState(false);
  const {
    isOpen: isOpenConfirm,
    onOpen: onOpenConfirm,
    onClose: onCloseConfirm,
  } = useDisclosure();
  const visibilityHandler = () => {
    setVisible(!visible);
  };
  return (
    <div className="py-7 lg:py-16">
      <Box
        className="space-y-6 bg-[#0A3150] bg-[url('/images/tuition-bg.png')] bg-cover bg-no-repeat bg-blend-normal"
        borderRadius={16}
        px={["11px", "36px"]}
        py={["23px", "24px"]}
      >
        <HStack justifyContent="space-between" alignItems="start">
          <Box className="grow">
            <HStack gap={4} alignItems="center">
              <Text color="white" fontSize={[12, 16]} fontWeight={700}>
                Tuition Fund
              </Text>
              {visible ? (
                <EyeOffIcon
                  color="white"
                  className="h-auto w-5 cursor-pointer lg:w-7"
                  onClick={visibilityHandler}
                />
              ) : (
                <EyeIcon
                  color="white"
                  className="h-auto w-5 cursor-pointer lg:w-7"
                  onClick={visibilityHandler}
                />
              )}
            </HStack>
            <Text fontSize={[24, 56]} fontWeight={700} color="white">
              {visible ? <>₦50,000</> : <>******</>}
            </Text>
          </Box>
          <Actions onOpenConfirm={onOpenConfirm} />
          <HStack gap={3} display={["none", "none", "flex"]}>
            <Link href={"/tuition-fund/settings"}>
              <Button
                color={BaseColor.PRIMARY_400}
                className="flex items-center gap-x-2 bg-[#3B5A73] text-sm font-medium text-white"
              >
                <Icon
                  name={EIconName.SETTINGS}
                  size={EIconSize.SM}
                  type={TypeIcon.SVG}
                />{" "}
                Settings
              </Button>
            </Link>
            <Button
              onClick={() => {
                onOpenConfirm();
              }}
              color={BaseColor.PRIMARY_400}
              className="flex items-center gap-x-2 bg-[#3B5A73] text-sm font-medium text-white"
            >
              <Icon
                name={EIconName.MONEY_UP}
                size={EIconSize.SM}
                type={TypeIcon.SVG}
              />{" "}
              Pay Tuition
            </Button>
            <Button
              onClick={() => {
                onOpenConfirm();
              }}
              color={BaseColor.PRIMARY}
              className="flex gap-x-1 bg-white text-sm font-medium text-[#0A3150]"
            >
              <PlusIcon color="#0A3150" size={20} /> Add Funds
            </Button>
          </HStack>
        </HStack>
        <VStack alignItems="start">
          <HStack justifyContent="space-between" className="w-full">
            <HStack>
              <Text color="white" fontSize={[12, 16]} fontWeight={[400, 600]}>
                Student ID: 0987654321
              </Text>
              <CopyToClipboard elementId={"0987654321"} />
            </HStack>
            <HStack>
              <button className="" onClick={() => setOpen(!open)}>
                <Icon
                  name={EIconName.INFO}
                  size={EIconSize.SM}
                  type={TypeIcon.SVG}
                />
              </button>
              <Text
                color="white"
                fontSize={16}
                fontWeight={600}
                className="hidden lg:block"
              >
                Tuition Fund Goal: ₦1,200,000.00
              </Text>
            </HStack>
          </HStack>
        </VStack>
      </Box>
      <TuitionGoalModal open={open} setOpen={setOpen} />
      <AddFundsModal open={isOpenConfirm} onClose={onCloseConfirm} />
    </div>
  );
};

export default TuitionFundHeader;
