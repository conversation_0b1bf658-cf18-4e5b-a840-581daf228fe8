import { keepPreviousData, useQuery } from "@tanstack/react-query";
import {
  getNextProgrammeIntakeList,
  INextProgrammeIntakeParam,
  IProgrammeIntakeData,
} from "../../api/repository/programmeIntake";

export const useNextProgrammeIntakeList = ({
  current_programme_intake_id,
}: INextProgrammeIntakeParam) => {
  const query = useQuery({
    queryKey: ["getNextProgrammeIntakeList", { current_programme_intake_id }],
    queryFn: () =>
      getNextProgrammeIntakeList({
        current_programme_intake_id,
      }),
    retry: false,
    enabled: !!current_programme_intake_id,
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
    select: (response) => {
      const data = response?.data as IProgrammeIntakeData[];

      return {
        data,
      };
    },
  });

  return query;
};
