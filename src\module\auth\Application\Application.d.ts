export interface IApplicationFormValue {
  firstName: string;
  lastName: string;
  phoneNumber: string | number;
  emailAddress: string;
  programme: string;
  dob: string;
  gender: string;
  nationality: string;
  residentialAddress: string;
  country: string;
  state: string;
  admissionType: string;
  aLevelResults: string;
  acceptTerms: boolean;
}

export interface IApplicationContainerProps {
  handleSubmit: (value: IApplicationFormValue) => Promise<void>;
}
