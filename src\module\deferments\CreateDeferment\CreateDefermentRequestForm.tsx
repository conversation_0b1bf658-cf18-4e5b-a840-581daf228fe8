/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useEffect, useMemo, useState } from "react";
import { Box, Text } from "@chakra-ui/react";
import CommonSelect from "@/components/commons/CommonSelect/CommonSelect";
import SemesterDefermentForm from "src/module/deferments/CreateDeferment/SemesterDefermentForm";
import ExamDefermentForm from "src/module/deferments/CreateDeferment/ExamDefermentForm";
import CourseDefermentForm from "src/module/deferments/CreateDeferment/CourseDefermentForm";
import { FormikProps } from "formik";
import { ICreateDeferment } from "src/module/deferments/CreateDeferment/CreateDefermentForm";
import CommonLoading from "src/components/commons/CommonLoading/CommonLoading";
import ComponentSelect from "src/module/deferments/CreateDeferment/components/ComponentSelect";
import { InputError } from "@/components/commons/InputField/InputError";
import { useStudentDetails } from "@/hooks/useStudent/useStudentDetails";
import { useSuccessAdvisor } from "./hooks/useSuccessAdvisor";
import { useActiveEnrollment } from "@/hooks/useEnrollment/useActiveEnrollment";

const CreateDefermentRequestForm = (props: FormikProps<ICreateDeferment>) => {
  const [isSubmitEnabled, setIsSubmitEnabled] = useState(false);
  const [successAdvisor, setSuccessAdvisor] = useState("");
  const [defermentType, setDefermentType] = useState("");
  const [formTypes, setFormTypes] = useState("");
  const [examDefermentOpen, setExamDefermentOpen] = useState(true);

  const { data } = useStudentDetails();
  const { data: advisors, isLoading } = useSuccessAdvisor();
  const { data: activeEnrollment } = useActiveEnrollment();

  const advisorOptions = useMemo(() => {
    return advisors.map((advisor) => {
      return { label: advisor.staff_name, value: advisor.staff_id };
    });
  }, [advisors]);

  const defermentTypeOptions = useMemo(() => {
    return [
      {
        value: "Semester deferment",
        label: "Semester deferment",
      },
      {
        value: "Course deferment",
        label: "Course deferment",
      },
      {
        value: "Exam deferment",
        label: "Exam deferment",
        disabled: false,
        component: !examDefermentOpen && (
          <p className="z-100 rounded bg-[#FDEBEA] px-2 py-1 text-xs font-medium text-red-700">
            Deferment window closed
          </p>
        ),
      },
      {
        value: "On-demand exam deferment",
        label: "On-demand exam deferment",
      },
    ];
  }, [examDefermentOpen]);

  const {
    touched,
    errors,
    handleSubmit,
    handleChange,
    handleBlur,
    values,
    isSubmitting,
    setFieldValue,
  } = props;

  useEffect(() => {
    setFieldValue("user_id", data?.personal_details.id);
    setFieldValue(
      "academic_time_period_id",
      activeEnrollment?.programme_intake_atp_id ||
        data?.application_details.atp_id,
    );

    if (activeEnrollment) {
      const parseDate = (dateString: string | undefined) => {
        if (!dateString) return null;
        return new Date(dateString.replace(" GMT+1", ""));
      };

      const startDate = parseDate(
        activeEnrollment?.programme_intake_atp_exam_deferrment_start_date,
      );
      const endDate = parseDate(
        activeEnrollment?.programme_intake_atp_exam_deferrment_end_date,
      );
      const currentDate = new Date();
      if (startDate && endDate) {
        const dateWithinRange =
          currentDate >= startDate && currentDate <= endDate;
        setExamDefermentOpen(dateWithinRange);
      }
    }
  }, [data, activeEnrollment]);

  const handleFormType = (value: string) => {
    setDefermentType(value);
    setFormTypes(value);
    handleChange({ target: { name: "deferment_type", value } });
  };

  const handleFormSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    handleSubmit(event as any);
  };

  useEffect(() => {
    switch (values.deferment_type) {
      case "Semester deferment":
        setIsSubmitEnabled(
          !!values.advisor_id && !!values.programme_intake && !!values.reason,
        );
        break;
      case "Course deferment":
        setIsSubmitEnabled(
          !!values.advisor_id &&
            values.courses.length > 0 &&
            !!values.programme_intake &&
            !!values.reason,
        );
        break;
      case "Exam deferment":
        setIsSubmitEnabled(
          !!values.advisor_id &&
            values.courses.length > 0 &&
            !!values.exam_center &&
            !!values.programme_intake &&
            !!values.location &&
            !!values.start_date &&
            !!values.end_date &&
            !!values.reason,
        );
        break;
      case "On-demand exam deferment":
        setIsSubmitEnabled(
          !!values.advisor_id &&
            values.courses.length > 0 &&
            !!values.exam_center &&
            !!values.programme_intake &&
            !!values.location &&
            !!values.start_date &&
            !!values.end_date &&
            !!values.reason,
        );
        break;
    }
  }, [values]);

  return (
    <div className="">
      {isLoading ? (
        <CommonLoading />
      ) : (
        <form onSubmit={handleFormSubmit}>
          <Box mb={5}>
            <Text
              paddingBottom={2}
              color="#0A3150"
              fontWeight={600}
              fontSize={14}
            >
              Who is your Success Advisor?
              <span className="text-red-500">*</span>
            </Text>
            <CommonSelect
              label=""
              value={successAdvisor}
              showIndicatorIcon={true}
              options={advisorOptions || []}
              onChange={(value) => {
                handleChange({ target: { name: "advisor_id", value } });
                setSuccessAdvisor(value);
              }}
              className="h-[47px]"
              placeholder="Select your Success Advisor"
            />
            <InputError
              error={errors.advisor_id}
              touched={touched.advisor_id}
            />
          </Box>

          <Box mb={8}>
            <Text
              paddingBottom={2}
              color="#0A3150"
              fontWeight={600}
              fontSize={14}
            >
              What would you like to defer?
              <span className="text-red-500">*</span>
            </Text>
            <ComponentSelect
              value={defermentType}
              options={defermentTypeOptions}
              onChange={(value: any) => handleFormType(value)}
              placeholder="Select a deferment option"
              className="h-[47px]"
            />
            <InputError
              error={errors.deferment_type}
              touched={touched.deferment_type}
            />
          </Box>

          <>
            {formTypes === "Semester deferment" && (
              <SemesterDefermentForm
                handleChange={handleChange}
                values={values}
                handleBlur={handleBlur}
                touched={touched}
                errors={errors}
                setFieldValue={setFieldValue}
                isSubmitting={isSubmitting}
                isSubmitEnabled={isSubmitEnabled}
              />
            )}
            {formTypes === "Course deferment" && (
              <CourseDefermentForm
                handleChange={handleChange}
                values={values}
                handleBlur={handleBlur}
                touched={touched}
                errors={errors}
                setFieldValue={setFieldValue}
                isSubmitting={isSubmitting}
                isSubmitEnabled={isSubmitEnabled}
              />
            )}
            {formTypes === "Exam deferment" && (
              <ExamDefermentForm
                handleChange={handleChange}
                values={values}
                handleBlur={handleBlur}
                touched={touched}
                errors={errors}
                setFieldValue={setFieldValue}
                isSubmitting={isSubmitting}
                isSubmitEnabled={isSubmitEnabled}
              />
            )}
            {formTypes === "On-demand exam deferment" && (
              <ExamDefermentForm
                handleChange={handleChange}
                values={values}
                handleBlur={handleBlur}
                touched={touched}
                errors={errors}
                setFieldValue={setFieldValue}
                isSubmitting={isSubmitting}
                isSubmitEnabled={isSubmitEnabled}
              />
            )}
          </>
        </form>
      )}
    </div>
  );
};

export default CreateDefermentRequestForm;
