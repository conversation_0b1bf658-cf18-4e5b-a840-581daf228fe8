import React, { useMemo } from "react";
import { Flex, Text } from "@chakra-ui/react";
import { E_STATUS_APPLICATION } from "@/constants/enums";
import Icon from "@/components/commons/Icons/Icon";
import { EIconName } from "@/components/commons/Icons/Icon.enums";
import { EColor } from "@/constants/colors";

const AlertApplication = ({ status }: { status: string }) => {
  const mapAlertWithStatus = (status: string) => {
    switch (status) {
      case E_STATUS_APPLICATION.PENDING_REVIEW:
        return "Your application will be reviewed within 24 hours and you will receive an email informing you of our decision and providing further instructions regarding the registration process.";
      case E_STATUS_APPLICATION.INCOMPLETE_DOCUMENTS:
      case E_STATUS_APPLICATION.INCORRECT_UPLOADS:
      case E_STATUS_APPLICATION.MISSING_SUBJECT:
      case E_STATUS_APPLICATION.IDENTITY_DISCREPANCIES:
      case E_STATUS_APPLICATION.DO_NOT_QUALIFY:
        return "We sent you an email providing further instructions regarding your documents.";
      default:
        return "";
    }
  };

  const styleAlert = useMemo(() => {
    if (status === E_STATUS_APPLICATION.PENDING_REVIEW) {
      return {
        backgroundColor: "#F8F5F2",
        border: "1px solid #BB9E7F",
      };
    }
    return {
      backgroundColor: "#FDEBEA",
      border: "1px solid #E83831",
    };
  }, [status]);

  return (
    <Flex
      {...styleAlert}
      mt="40px"
      alignItems="center"
      justifyContent="flex-start"
      gap="8px"
      px="24px"
      borderRadius="16px"
      py="8px"
    >
      <Icon
        isStaticIcon={true}
        name={EIconName.EXCLAMATION_CIRCLE}
        color={
          status === E_STATUS_APPLICATION.PENDING_REVIEW
            ? EColor.MONGOOSE
            : EColor.CINNABAR
        }
      />
      <Text fontSize="14px">{mapAlertWithStatus(status)}</Text>
    </Flex>
  );
};

export default AlertApplication;
