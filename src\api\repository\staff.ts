import { baseApi } from "../config/api";
import { MODULE_ROUTE, Routes } from "../config/routes";
import { APIResponse } from "src/api/config/api.d";

export interface IStaff {
  staff_id: string;
  staff_name: string;
}

export const getStaff = async ({
  role,
}: {
  role: string;
}): Promise<APIResponse<IStaff[]>> => {
  try {
    const { data } = await baseApi.get<APIResponse<IStaff[]>>(
      Routes[MODULE_ROUTE.STUDENT].STAFF,
      {
        params: { role },
      },
    );
    return data;
  } catch (error) {
    console.error("Error fetching staff:", error);
    throw error;
  }
};
