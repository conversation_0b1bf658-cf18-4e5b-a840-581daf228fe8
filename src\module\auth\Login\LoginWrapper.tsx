"use client";

import { Flex } from "@chakra-ui/react";

import { TParamPageCommon } from "@/constants/types";
import { EVIEW_PORT } from "@/constants/enums";
import LoginFormContainer from "./LoginContainer";
import RightImageBlock from "../components/RightImageBlock";
import { useLogin } from "./useLogin";
import LoginVerifyFormContainer from "./LoginVerifyContainer";
import CommonLoading from "@/components/commons/CommonLoading/CommonLoading";

const LoginWrapper = ({ searchParams }: TParamPageCommon) => {
  const { handleLogin, handleLoginVerify, verifyEmail, showLoader } =
    useLogin();

  if (showLoader) {
    return <CommonLoading size="large" />;
  }

  return (
    <Flex alignItems="center" gap={{ xl: "172px", md: "40px" }}>
      {verifyEmail ? (
        <LoginVerifyFormContainer
          handleLoginVerify={handleLoginVerify}
          email={verifyEmail}
        />
      ) : (
        <LoginFormContainer handleLogin={handleLogin} />
      )}
      {searchParams?.viewport !== EVIEW_PORT.MOBILE && <RightImageBlock />}
    </Flex>
  );
};

export default LoginWrapper;
