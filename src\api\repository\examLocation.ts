import { baseApi } from "src/api/config/api";
import { APIResponse, Paginated } from "src/api/config/api.d";
import { MODULE_ROUTE, Routes } from "src/api/config/routes";
import { IParamsExamLocation } from "src/hooks/useExamLocation/useExamLocationList";

export interface IExamLocation {
  Id: string;
  Center: string;
  City: string;
  State: string;
}

export async function getExamLocationList({
  perPage = 10,
  page = 1,
  search,
}: IParamsExamLocation): Promise<APIResponse<Paginated<IExamLocation>>> {
  try {
    const response = await baseApi.get<APIResponse<Paginated<IExamLocation>>>(
      Routes[MODULE_ROUTE.MISC].GET_EXAM_LOCATION_LIST,
      {
        params: {
          page,
          perPage,
          ...(search ? { search } : {}),
        },
      },
    );
    return response.data;
  } catch (error) {
    console.error("Error get list faculty:", error);
    throw error;
  }
}
