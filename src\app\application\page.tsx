import SecondaryHeader from "@/components/templates/SecondaryHeader";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { cn } from "@/lib/utils";
import ApplicationDetails from "../../components/templates/ApplicationDetails/index";
import ApplicationWrapper from "../../module/auth/Application/ApplicationWrapper";
import { TParamPageCommon } from "../../constants/types";

const cinzel = Cinzel({ subsets: ["latin"], variable: "--font-secondary" });

const application = (props: TParamPageCommon) => {
  return (
    <div>
      <div className="fixed z-50 w-full">
        <SecondaryHeader />
      </div>

      {/*  */}
      <div className="h-[220px] bg-[url(https://res.cloudinary.com/ddwdiuklw/image/upload/v1720558032/Frame_265_v4xq0e.png)] bg-cover bg-center lg:h-[420px]">
        <div className="flex flex-col items-center justify-center pt-20 lg:pt-[150px]">
          <h2
            className={cn(
              cinzel.variable,
              "m-auto text-center font-secondary text-2xl font-bold uppercase text-white lg:text-5xl",
            )}
          >
            Start your Admission Application
          </h2>
          <p className="w-8/12 pt-4 text-center text-sm font-medium text-white lg:w-full lg:text-base">
            Every great journey begins with a single step. We will be with you
            every step of the way.
          </p>
        </div>
      </div>
      <section className="flex gap-10 bg-[#F9FAFB] px-6 lg:px-[112px]">
        <div
          className="mt-16 w-full rounded-[16px] bg-white p-3 lg:-mt-[120px] lg:w-[70%] lg:p-8"
          style={{ boxShadow: "0px 8px 40px 0px #0A315012]" }}
        >
          <ApplicationWrapper {...props} />
        </div>
        <div className="mt-[45px] hidden w-[30%] lg:block">
          <ApplicationDetails />
        </div>
      </section>
    </div>
  );
};

export default application;
