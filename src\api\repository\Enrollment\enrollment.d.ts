export interface ICourseOfferingParams {
  programme_id: string;
  atp_id: string;
  level: string;
  programme_intake_id: string;
}

export interface ICreateEnrollmentParams {
  programme_id: string;
  programme_intake_id: string;
  student_id: string;
  semester_atp_id: string;
  course_offering_ids: string[];
  is_moodle: boolean;
  application_id?: string;
}

export interface ICourseOfferingData {
  id: string;
  name: string;
  duration: number;
  duration_unit: string;
  type: string;
  description: string;
  programme_unit: number;
  department: string;
  original_tution_fee_per_session: number;
  discount_tution_fee_per_session: number;
  tution_fee_per_semester: number;
  maximum_credit_unit: number;
  minimum_credit_unit: number;
  image: string;
  created_at: string;
  updated_at: string;
}

export enum PreEnrollmentStatus {
  NOT_STARTED = "not_started",
  ENROLLMENT_PENDING = "enrollment_pending",
  PAYMENT_IN_PROGRESS = "payment_in_progress",
  PAYMENT_COMPLETED = "payment_completed",
  ENROLLMENT_CREATED = "enrollment_created",
}

export interface IUpdatePreEnrollmentStatusParams {
  status: PreEnrollmentStatus;
}

export type ActiveEnrollment = {
  academic_time_period_name: string;
  level: string;
  next_programme_intake_atp_id: string;
  next_programme_intake_atp_name: string;
  next_programme_intake_id: string;
  next_programme_intake_publish_date: string;
  programme_atp_name: string;
  programme_id: string;
  programme_intake_id: string;
  programme_level_enrollment_id: string;
  programme_level_id: string;
  programme_name: string;
  programme_short_name: string;
  programme_code: string;
  programme_number_of_semesters: number;
  next_programme_intake_level: string;
  programme_intake_atp_id: string;
  programme_intake_atp_exam_deferrment_end_date: string;
  programme_intake_atp_exam_deferrment_start_date: string;
};

export type EnrollmentPaymentCompletion = {
  data: {
    payment_product_id: string;
    product_id: string;
    programme_id: string;
    programme_intake_id: string;
  };
  type: "payment_completed" | "make_payment";
};
