export const SEMESTERS = [
  "FIRST_SEMESTER",
  "SECOND_SEMESTER",
  "THIRD_SEMESTER",
  "FOURTH_SEMESTER",
  "FIFTH_SEMESTER",
  "SIXTH_SEMESTER",
];

export const sortSemesters = (a: string, b: string) => {
  const aPos = SEMESTERS.findIndex(
    (semester) => semester.toUpperCase() === a.toUpperCase(),
  );
  const bPos = SEMESTERS.findIndex(
    (semester) => semester.toUpperCase() === b.toUpperCase(),
  );
  if (aPos < bPos) {
    return -1;
  }
  if (bPos < aPos) {
    return 1;
  }
  return 0;
};
