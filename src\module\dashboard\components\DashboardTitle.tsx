import Tag from "@/components/commons/Tag/Tag";
import { BaseColor } from "@/constants/colors";
import { Flex, Text } from "@chakra-ui/react";
import { ReactNode } from "react";

const DashboardTitle = ({
  title,
  tag,
  baseColor = BaseColor.DEFAULT,
  showDetails,
}: {
  title: ReactNode;
  tag?: ReactNode | string;
  baseColor?: BaseColor;
  showDetails?: boolean;
}) => {
  return (
    <Flex gap="16px" flexDir={{ md: "row", base: "column" }}>
      <Text fontWeight="semibold" fontSize="20px" color={BaseColor.PRIMARY}>
        {title}
      </Text>
      {tag ? (
        <Tag
          padding="8px 24px"
          borderRadius="24px"
          fontSize="14px"
          width="fit-content"
          baseColor={baseColor}
        >
          {tag}
        </Tag>
      ) : (
        ""
      )}
    </Flex>
  );
};

export default DashboardTitle;
