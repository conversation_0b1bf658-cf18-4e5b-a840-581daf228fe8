import { Flex, Image, Text } from "@chakra-ui/react";
import Tag from "@/components/commons/Tag/Tag";
import { capitalizeFirstLetter } from "@/lib/utils/helpers";
import { CourseSectionProps } from "./Courses.type";
import { COURSE_STATUS, MAP_COURSE_STATUS_LABEL } from "../constants";
import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import { EButtonType } from "@/components/commons/ButtonCTA/ButtonCTA.d";

const CourseSection = ({
  course: { course_name, course_code, credit_unit, group_type, status },
  onEnroll,
  semesterEnrollmentEndDate,
}: CourseSectionProps) => {
  const mapStyleCourseStatus = () => {
    switch (status.toLowerCase()) {
      case COURSE_STATUS.COMPLETED: {
        return {
          color: "#00802B",
          bg: "#E5F4EA",
        };
      }
      case COURSE_STATUS.CARRY_OVER: {
        return {
          color: "#D3332D",
          bg: "#FDEBEA",
        };
      }
      case COURSE_STATUS.OUTSTANDING: {
        return {
          color: "#D3332D",
          bg: "#FDEBEA",
        };
      }
      case COURSE_STATUS.DEFERRED: {
        return {
          color: "#AA7200",
          bg: "#FFF7E5",
        };
      }
      case COURSE_STATUS.DISCONTINUED: {
        return {
          color: "#092D49",
          bg: "#E7EAEE",
        };
      }
      case COURSE_STATUS.ENROLLED: {
        return {
          color: "#092D49",
          bg: "#E7EAEE",
        };
      }
      default:
        return {
          color: "#092D49",
          bg: "#E7EAEE",
        };
    }
  };

  return (
    <div>
      <Flex flexDir="column" gap="10px">
        <Image src={"/images/dashboard-2.png"} alt={"image"} />
        {[
          COURSE_STATUS.ENROLLED,
          COURSE_STATUS.COMPLETED,
          COURSE_STATUS.CARRY_OVER,
          COURSE_STATUS.OUTSTANDING,
          COURSE_STATUS.DEFERRED,
          COURSE_STATUS.DISCONTINUED,
        ].every((s) => s != status.toLowerCase()) &&
          new Date(semesterEnrollmentEndDate ?? "") > new Date() && (
            <ButtonCTA
              fontSize={12}
              height="auto"
              onClick={() => onEnroll()}
              py={3}
              px={7}
              customType={EButtonType.SECONDARY}
              variant={EButtonType.SECONDARY}
              colorScheme="secondary"
            >
              Enrol in Course
            </ButtonCTA>
          )}
        <Text color="#301446" mt="3px" fontWeight={500} fontSize="14px">
          {course_name}
        </Text>
        <Flex justifyContent="space-between">
          <Text fontSize="12px" color="#5B758A" fontWeight={600}>
            {course_code}
          </Text>
          <Text fontSize="12px" color="#5B758A" fontWeight={600}>
            {credit_unit} Units
          </Text>
        </Flex>
        {status && (
          <Flex gap="8px">
            <Tag
              color="#092D49"
              bg="#E7EAEE"
              fontSize="12px"
              width="fit-content"
              borderRadius="8px"
            >
              {capitalizeFirstLetter(group_type ?? "") || "N/A"}
            </Tag>
            {status && (
              <Tag
                {...mapStyleCourseStatus()}
                fontSize="12px"
                width="fit-content"
                borderRadius="8px"
              >
                {MAP_COURSE_STATUS_LABEL[status.toLowerCase()] || "N/A"}
              </Tag>
            )}
          </Flex>
        )}
      </Flex>
    </div>
  );
};
export default CourseSection;
