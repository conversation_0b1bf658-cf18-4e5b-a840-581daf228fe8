import { APIResponse, Paginated } from "src/api/config/api.d";
import { authApi } from "src/api/config/api";
import { MODULE_ROUTE, Routes } from "src/api/config/routes";
import { AuthUser } from "src/store/AuthenticationStore/authentication.d";
import { CASService, CASTicket, GenerateServiceType } from "./cas.d";
import { undefinedToString } from "@/lib/utils/string";
import { ZodError } from "zod";
import crypto from "crypto";
import { addMinutes, isAfter } from "date-fns";
import axios from "axios";

export const generateTicket = (prefix: "ST" | "PT" | "PGT"): string => {
  const randomString = crypto.randomBytes(32).toString("hex");
  return `${prefix}-${randomString}`.substring(0, 255);
};

export const formatZodErrors = (error: ZodError) => {
  return error.errors.map((err) => ({
    path: err.path.join("."), // Convert path array to dot notation (e.g., "user.email")
    message: err.message, // Error message
  }));
};

export const generateServiceTicket = async (
  request: GenerateServiceType,
): Promise<string> => {
  try {
    const ticket = (
      await axios.post<{ ticket: string }>(
        Routes[MODULE_ROUTE.CAS].GENERATE_TICKET,
        request,
      )
    ).data;
    return ticket.ticket;
  } catch (e) {
    return "";
  }
};

export const getCASTicket = async (ticket: string): Promise<CASTicket> => {
  const casTicket = (
    await authApi.get<APIResponse<CASTicket>>(
      Routes[MODULE_ROUTE.CAS].GET_TICKET,
      { params: { ticket } },
    )
  ).data.data;

  return casTicket;
};

export const refreshToken = async (
  refresh_token: string,
): Promise<{ access_token: string; refresh_token: string }> => {
  const tokens = (
    await authApi.post<
      APIResponse<{ access_token: string; refresh_token: string }>
    >(Routes[MODULE_ROUTE.AUTH].REFRESH_TOKEN, { refresh_token })
  ).data.data;

  return tokens;
};

export const getUserByAccessToken = async (
  accessToken: string,
): Promise<AuthUser> => {
  const user = (
    await authApi.get<APIResponse<AuthUser>>(Routes[MODULE_ROUTE.AUTH].ME, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    })
  ).data.data;

  return user;
};

export const getCasService = async (
  service_id: string,
): Promise<CASService> => {
  const service = (
    await authApi.get<APIResponse<CASService>>(Routes[MODULE_ROUTE.CAS].GET, {
      params: { id: service_id },
    })
  ).data.data;
  return service;
};

export const validateCasService = async (
  service: string,
): Promise<CASService> => {
  const services = (
    await authApi.get<APIResponse<Paginated<CASService[]>>>(
      Routes[MODULE_ROUTE.CAS].LIST,
      {
        params: { search: service },
      },
    )
  ).data.data;
  return services.data[0];
};

export const createTicket = async (
  data: Partial<CASTicket>,
  duration: number,
): Promise<CASTicket> => {
  const ticket = (
    await authApi.post<APIResponse<CASTicket>>(
      Routes[MODULE_ROUTE.CAS].CREATE_TICKET,
      { ...data, expires_at: addMinutes(new Date(), duration) },
    )
  ).data.data;
  return ticket;
};

export const sendPGTToCallback = async (
  pgtUrl: string,
  pgtTicket: string,
  pgtIou: string,
): Promise<void> => {
  await axios.get<APIResponse<void>>(pgtUrl, {
    params: { pgtId: pgtTicket, pgtIou },
  });
};

export const isTicketInvalid = async (
  ticket: CASTicket,
  prefix?: "ST" | "PT" | "PGT",
  deleteTicket = true,
): Promise<boolean> => {
  if (prefix && ticket.type !== prefix) {
    throw "Ticket type mismatch";
  }
  const invalid = isAfter(new Date(), ticket.expires_at);

  if (deleteTicket) {
    await authApi.delete(`${Routes[MODULE_ROUTE.CAS].DELETE_TICKET}`, {
      params: { id: ticket.id },
    });
  }

  return invalid;
};

export const getFailureResponse = (
  code: string,
  message: string,
  format: string | null = "XML",
) => {
  if (format == "JSON") {
    return Response.json({
      serviceResponse: {
        authenticationFailure: {
          code,
          description: message,
        },
      },
    });
  }

  return new Response(`<cas:serviceResponse xmlns:cas="http://www.yale.edu/tp/cas">
    <cas:authenticationFailure code="${code}">
      ${message}
    </cas:authenticationFailure>
  </cas:serviceResponse>`);
};

export const getServiceValidationSuccessResponse = (
  user: AuthUser,
  proxyGrantingTicket: string | undefined,
  pgtIou: string | undefined,
  format: string | null = "XML",
) => {
  // ...(proxyGrantingTicket ? { proxyGrantingTicket } : {}),
  if (format == "JSON") {
    return Response.json({
      serviceResponse: {
        authenticationSuccess: {
          user: user.contact_information.email,
          ...(pgtIou ? { pgtIou } : {}),
        },
      },
    });
  }
  // ${proxyGrantingTicket ? `<cas:proxyGrantingTicket>${undefinedToString(proxyGrantingTicket)}</cas:proxyGrantingTicket>` : ""}

  return new Response(`<cas:serviceResponse xmlns:cas="http://www.yale.edu/tp/cas">
    <cas:authenticationSuccess>
      <cas:user>${user.contact_information.email}</cas:user>
      ${pgtIou ? `<cas:pgtIou>${undefinedToString(pgtIou)}</cas:pgtIou>` : ""}
      <cas:attributes>
        <cas:email>${user.contact_information.email}</cas:email>
        <cas:firstname>${user.biography.first_name}</cas:firstname>
        <cas:lastname>${user.biography.last_name}</cas:lastname>
        <cas:city>${user.contact_information.city || "None"}</cas:city>
        <cas:country>${user.contact_information.country || "NG"}</cas:country>
        <cas:institution>MIVA University</cas:institution>
        <cas:phone>${user.contact_information.phone_number || "None"}</cas:phone>
        <cas:mobile_phone>${user.contact_information.phone_number || "None"}</cas:mobile_phone>
        <cas:address>${encodeURIComponent(user.contact_information.residential_address) || "None"}</cas:address>
      </cas:attributes>
    </cas:authenticationSuccess>
  </cas:serviceResponse>`);
};

export const getProxySuccessResponse = (
  proxyTicket: string,
  format: string | null = "XML",
) => {
  if (format == "JSON") {
    return Response.json({
      serviceResponse: {
        proxySuccess: {
          proxyTicket,
        },
      },
    });
  }

  return new Response(`<cas:serviceResponse xmlns:cas="http://www.yale.edu/tp/cas">
    <cas:proxySuccess>
      <cas:proxyTicket>${undefinedToString(proxyTicket)}</cas:proxyGrantingTicket>
    </cas:proxySuccess>
  </cas:serviceResponse>`);
};

export const getProxyValidationSuccessResponse = (
  user: AuthUser,
  serviceURL: string,
  format: string | null = "XML",
) => {
  if (format == "JSON") {
    return Response.json({
      serviceResponse: {
        authenticationSuccess: {
          user: user.contact_information.email,
          proxies: {
            proxy: serviceURL,
          },
        },
      },
    });
  }

  return new Response(`<cas:serviceResponse xmlns:cas="http://www.yale.edu/tp/cas">
    <cas:authenticationSuccess>
      <cas:user>${user.contact_information.email}</cas:user>
      <cas:proxies>
        <cas:proxy>${serviceURL}</cas:proxy>
      </cas:proxies>
      <cas:attributes>
        <cas:email>${user.contact_information.email}</cas:email>
        <cas:firstname>${user.biography.first_name}</cas:firstname>
        <cas:lastname>${user.biography.last_name}</cas:lastname>
        <cas:city>${user.contact_information.city}</cas:city>
        <cas:country>${user.contact_information.country}</cas:country>
        <cas:institution>MIVA University</cas:institution>
        <cas:phone>${user.contact_information.phone_number}</cas:phone>
        <cas:mobile_phone>${user.contact_information.phone_number}</cas:mobile_phone>
        <cas:address>${user.contact_information.residential_address}</cas:address>
      </cas:attributes>
    </cas:authenticationSuccess>
  </cas:serviceResponse>`);
};

export const redirectToCASService = async ({
  access_token,
  refresh_token,
  service,
}: {
  access_token: string;
  refresh_token: string;
  service?: string;
}): Promise<void> => {
  if (service) {
    const ticket = await generateServiceTicket({
      service,
      access_token,
      refresh_token,
    });
    // This login session is for a CAS session, redirect to the service with the ticket
    const separator = service.includes("?") ? "&" : "?";
    window.location.href = `${service}${separator}ticket=${ticket}`;
  } else {
    window.location.href = `/cas/success`;
  }
};
