import { AxiosError } from "axios";
import { format, isValid, parseISO } from "date-fns";

export function getFormattedTime(date: Date | string) {
  if (date === null) return "-";

  const dateValue = new Date(date);

  let hours: number = dateValue.getHours();
  const minutes: number = dateValue.getMinutes();
  const ampm = hours >= 12 ? "PM" : "AM";

  hours = hours % 12;
  hours = hours ? hours : 12;
  const formatter_minutes = `0${minutes}`.slice(-2);

  return `${hours}:${formatter_minutes} ${ampm}`;
}

export const formatDate = (date: Date | string): string => {
  if (!date) return "-";

  const parsedDate = date instanceof Date ? date : parseISO(date);
  if (!isValid(parsedDate)) return "-";

  return format(parsedDate, "dd MMM yyyy");
};

export const capitalizeFirstLetter = (s: string): string => {
  return s?.charAt(0).toUpperCase() + s?.slice(1);
};

export const formatCurrency = (amount: number, currency: string): string => {
  const formattedAmount = amount?.toLocaleString("en-US", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });

  const currencySymbol = currency === "NGN" ? "₦" : "$";

  return `${currencySymbol}${formattedAmount}`;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const extractAxiosError = (error: AxiosError<any, any>): string => {
  if (typeof error.response?.data.errors == "string") {
    return capitalizeFirstLetter(error.response?.data.errors);
  }
  if (typeof error.response?.data.errors == "object") {
    return capitalizeFirstLetter(
      Object.values(error.response?.data.errors).join(", "),
    );
  }
  return capitalizeFirstLetter(error.message);
};
