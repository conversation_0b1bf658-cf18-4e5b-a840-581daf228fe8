"use client";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  KeyboardEvent,
  useRef,
  useEffect,
  Dispatch,
  SetStateAction,
} from "react";
import { X } from "lucide-react";
import { cn } from "@/lib/utils";
import { useToast } from "@chakra-ui/react";

interface OtpVerificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isSubmitting: boolean;
  title?: string;
  description?: string;
  otp: string;
  setOtp: Dispatch<SetStateAction<string>>;
}

export const OtpVerificationModal = ({
  isOpen,
  onClose,
  onConfirm,
  isSubmitting,
  title = "Enter OTP",
  description = "Enter OTP code sent to your email",
  otp,
  setOtp,
}: OtpVerificationModalProps) => {
  const toast = useToast();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex h-screen items-center justify-center bg-black/70">
      <div className="relative w-[90%] w-full max-w-md rounded-md bg-white p-6 lg:w-[530px]">
        <button
          onClick={onClose}
          disabled={isSubmitting}
          className="absolute right-4 top-4 text-gray-500 hover:text-gray-700 disabled:opacity-50"
        >
          <X size={18} />
        </button>

        <>
          <h2 className="mb-1 text-3xl font-bold text-[#0A3150]">{title}</h2>
          <p className="mb-6 text-sm font-medium text-[#0A3150]">
            {description}
          </p>

          <div className="mb-6">
            <Input
              type="text"
              inputMode="numeric"
              pattern="[0-9]*"
              maxLength={6}
              value={otp}
              onChange={(e) => {
                const value = e.target.value.replace(/\D/g, "").slice(0, 6);
                setOtp(value);
              }}
              onPaste={(e) => {
                e.preventDefault();
                const pastedData = e.clipboardData.getData("text/plain");
                const numbers = pastedData.replace(/\D/g, "").slice(0, 6);
                setOtp(numbers);
              }}
              disabled={isSubmitting}
              className={cn(
                "h-12 w-full rounded-[8px] text-base font-medium",
                "focus:ring-0 focus:ring-offset-0",
                "border-gray-300",
                isSubmitting && "opacity-70",
              )}
              placeholder="Enter OTP"
            />
          </div>

          <div className="mb-6">
            <Button
              variant="link"
              onClick={() => {
                toast({
                  title: "Resend OTP",
                  description: "OTP has been resent to your email",
                  status: "success",
                  duration: 3000,
                  isClosable: true,
                });
              }}
              className="text-[#c4a889] hover:text-[#b39878]"
            >
              Resend OTP
            </Button>
          </div>

          <Button
            onClick={onConfirm}
            disabled={isSubmitting || !otp || otp.length !== 6}
            className="w-full rounded bg-[#c4a889] py-2 font-medium text-white hover:bg-[#b39878] disabled:opacity-70"
          >
            {isSubmitting ? "Verifying..." : "Done"}
          </Button>

          <p className="mt-4 text-center text-sm text-[#0A3150]">
            Have any issues? Contact support at{" "}
            <a
              href="mailto:<EMAIL>"
              className="font-bold hover:underline"
            >
              <EMAIL>
            </a>
          </p>
        </>
      </div>
    </div>
  );
};
