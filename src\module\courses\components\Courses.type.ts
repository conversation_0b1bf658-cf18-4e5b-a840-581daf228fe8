import { Course } from "src/module/result/StudentDetails";

export type CourseSectionProps = {
  course: Course;
  semesterEnrollmentEndDate: string;
  otherDetails: {
    student_id: string;
    status: string;
    programme_id: string;
  };
  onEnroll: () => Promise<void>;
};

export enum CourseType {
  Compulsory = "Compulsory",
  Elective = "Elective",
}

export type OtherDetails = {
  student_id?: any;
  status?: any;
};

export interface ICoursesProps {
  otherDetails?: OtherDetails;
}
