/* eslint-disable @typescript-eslint/no-explicit-any */
import { FormikErrors, FormikTouched } from "formik";

export interface ICreateDeferment {
  academic_time_period_id: string;
  advisor_id: string;
  courses: string[];
  deferment_type: string;
  documents: File[];
  end_date: string;
  exam_center: string;
  location: string;
  programme_intake: string;
  reason: string;
  start_date: string;
  user_id: string;
}

export interface ICreateDefermentContainerProps {
  isEdit: boolean;
  initialValues: any;
  handleCreateDeferment: (payload: any) => Promise<any>;
}

export interface ICreateDefermentForm {
  values: ICreateDeferment;
  handleChange: any;
  handleBlur: any;
  touched: FormikTouched<ICreateDeferment>;
  errors: FormikErrors<ICreateDeferment>;
  isSubmitting: boolean;
  setFieldValue: any;
  isSubmitEnabled: boolean;
}
