"use client";

import React from "react";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRightIcon, CircleAlert } from "lucide-react";
import { useTuitionFund } from "../../context/TuitionFundContext";

const OnboardingScreen: React.FC = () => {
  const { setCurrentStep, sendOTP, isOTPLoading } = useTuitionFund();

  const goToTermsAndConditions = (e: React.MouseEvent) => {
    e.preventDefault();
    setCurrentStep(1);
  };
  const gotToOtpScreen = (e: React.MouseEvent) => {
    e.preventDefault();
    sendOTP();
    setCurrentStep(2);
  };

  return (
    <div className="rounded-lg bg-[#092D49] text-center text-white lg:w-[550px]">
      <div className="relative mb-6 h-[200px] w-full lg:h-[270px]">
        <Image
          src="/images/tuition-banner.png"
          alt="Students"
          fill
          className="rounded-lg object-cover object-top"
        />
      </div>
      <div className="m-auto mt-6 max-w-[430px] space-y-8 p-6 lg:px-0">
        <div className="m-auto lg:w-[395px]">
          <h2 className="text-2xl font-bold">Build your Tuition Fund</h2>
          <p className="text-base font-normal">
            Start paying your fees by making small, flexible payments whenever
            it suits you
          </p>
        </div>

        <Button
          variant="primary"
          className="w-full bg-[#E83831] hover:bg-red-700 lg:w-[195px]"
          onClick={gotToOtpScreen}
          disabled={isOTPLoading}
        >
          {isOTPLoading ? "Sending OTP..." : "Get Started"}
          {!isOTPLoading && <ArrowRightIcon className="ml-2" />}
        </Button>
        <div className="m-auto mb-6 flex h-[60px] items-center rounded-md bg-blue-50 p-4 text-[#0A3150]">
          <div className="mr-2 mt-1">
            <CircleAlert className="h-4 w-4" />
          </div>
          <div className="text-left text-xs">
            You can only add money to your Tuition Fund up to the total cost of
            your entire academic programme. View
            <button
              onClick={goToTermsAndConditions}
              className="ml-1 text-[#E83831] hover:underline"
            >
              Terms and Conditions.
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OnboardingScreen;
