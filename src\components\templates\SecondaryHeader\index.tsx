"use client";

import React from "react";
import { Image } from "@chakra-ui/react";
import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import { useRouter } from "next/navigation";

const SecondaryHeader = () => {
  const router = useRouter();

  const viewProgrammes = () => {};

  return (
    <div className="flex h-[62px] w-full items-center justify-between bg-white px-10 py-3 lg:px-28">
      <Image
        alt="Logo"
        src="/images/logo.svg"
        onClick={() => router.push("/")}
      />
      <ButtonCTA
        background="#E83831"
        color="white"
        onClick={() => viewProgrammes()}
        fontSize="12px"
        fontWeight="bold"
        padding="12px 16px"
      >
        View Programmes
      </ButtonCTA>
    </div>
  );
};

export default SecondaryHeader;
