import {
  <PERSON>lex,
  <PERSON>dal,
  <PERSON>dal<PERSON>lose<PERSON>utton,
  <PERSON>dal<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  ModalOverlay,
} from "@chakra-ui/react";
import { CommonModalProps } from "./CommonModal.d";
import { BaseColor } from "@/constants/colors";

export const CommonModal = ({
  isOpen,
  onClose,
  children,
  title,
  ...rest
}: CommonModalProps) => {
  return (
    <Modal closeOnOverlayClick isOpen={isOpen} onClose={onClose} {...rest}>
      <ModalOverlay />
      <ModalContent>
        <Flex
          justifyContent="space-between"
          alignItems="center"
          m={4}
          px={4}
          py={3}
          backgroundColor={BaseColor.PRIMARY}
          rounded={6}
        >
          <ModalHeader p={0} fontSize="16px" color="white" flexGrow={1}>
            {title}
          </ModalHeader>
          <ModalCloseButton position="initial" color="white" />
        </Flex>
        {children}
      </ModalContent>
    </Modal>
  );
};
