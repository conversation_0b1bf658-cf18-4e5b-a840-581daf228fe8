/* eslint-disable react-hooks/exhaustive-deps */
import { useQuery } from "@tanstack/react-query";
import { getFacultyById } from "../../api/repository/faculty";
import axios from "axios";
import { extractAxiosError } from "@/lib/utils/helpers";
import { useToast } from "@chakra-ui/react";
import { useEffect } from "react";

interface IUseFacultyById {
  facultyId?: string;
  enabled?: boolean;
}

export const useFacultyById = ({
  facultyId,
  enabled = true,
}: IUseFacultyById) => {
  const toast = useToast();

  const query = useQuery({
    queryKey: ["getFacultyById", facultyId],
    queryFn: () => getFacultyById({ facultyId: facultyId as string }),
    enabled: enabled && !!facultyId,
    select: (response) => response.data,
  });

  useEffect(() => {
    if (query.error && axios.isAxiosError(query.error)) {
      toast({
        description: extractAxiosError(query.error),
        status: "error",
      });
    }
  }, [query.isError]);

  return query.data;
};
