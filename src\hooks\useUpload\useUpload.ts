/* eslint-disable @typescript-eslint/no-explicit-any */
import { useToast } from "@chakra-ui/react";
import { useRef, useState } from "react";
import { UploadImage } from "src/api/repository/fileUpload";

export const useUpload = (setUploadedFile?: (f: string) => void) => {
  const toast = useToast();

  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      if (selectedFile.size > 10 * 1024 * 1024) {
        toast({
          description: "File size exceeds 10MB.",
          status: "error",
        });
        return;
      }
      handleUpload(selectedFile);
    }
  };

  const triggerFileUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click(); // Triggers the file input click
    }
  };

  const handleUpload = async (
    uploaded_file: File,
    skipSigning?: string,
  ): Promise<string | undefined> => {
    if (!uploaded_file) {
      toast({
        description: "Please select a file.",
        status: "error",
      });
      return;
    }
    setIsLoading(true);

    const formData: any = new FormData();
    formData.append("image", uploaded_file);
    try {
      const resp: any = await UploadImage(formData, skipSigning);
      if (resp.response) {
        setIsLoading(false);
        if (resp.status !== 200) {
          toast({
            description: "Unsupported file type. Please choose another file",
            status: "error",
          });
        }
      } else if (resp.status === 200) {
        setIsLoading(false);
        toast({
          description: resp?.data?.message,
          status: "success",
        });
        setUploadedFile?.(resp?.data?.data as string);
        return resp?.data?.data;
      }
    } catch (error) {
      console.error("err", error);
    }
  };

  return {
    isLoading,
    handleFileChange,
    triggerFileUpload,
    fileInputRef,
    handleUpload,
  };
};
