import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { getStudentApplications } from "src/api/repository/Application/application";

export const useStudentApplication = ({
  id,
  page,
  perPage,
}: {
  id: string;
  page: number;
  perPage: number;
}) => {
  const [pagination, setPagination] = useState({
    pageIndex: page - 1,
    pageSize: perPage,
  });

  const query = useQuery({
    enabled: !!id,
    queryKey: ["getStudentApplications", { id, pagination }],
    queryFn: () => {
      return getStudentApplications({
        page: pagination.pageIndex + 1,
        perPage: pagination.pageSize,
        id,
      });
    },
    retry: false,
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
    staleTime: 0,
    refetchOnMount: true,
    select: (response) => {
      const {
        data,
        currentPage,
        nextPage,
        perPage,
        prevPage,
        total,
        totalPages,
      } = response.data;

      return {
        data,
        meta: {
          currentPage,
          nextPage,
          perPage,
          prevPage,
          total,
          totalPages,
        },
      };
    },
  });
  return {
    ...query,
    pagination,
    setPagination,
  };
};
