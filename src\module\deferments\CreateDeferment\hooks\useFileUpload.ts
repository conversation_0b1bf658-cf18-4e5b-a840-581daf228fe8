/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from "react";
import { UploadImage } from "src/api/repository/fileUpload";

interface UseFileUploadReturn {
  uploadFiles: (files: File[]) => Promise<string[]>;
  isUploading: boolean;
  error: string | null;
}

export const useFileUpload = (): UseFileUploadReturn => {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const uploadSingleFile = async (file: File): Promise<string> => {
    const formData = new FormData();
    formData.append("image", file);

    try {
      const resp: any = await UploadImage(formData);
      return resp?.data?.data;
    } catch (err) {
      console.error("Error uploading file:", err);
      throw new Error(`Failed to upload file ${file.name}`);
    }
  };

  const uploadFiles = async (files: File[]): Promise<string[]> => {
    setIsUploading(true);
    setError(null);

    try {
      const uploadPromises = files.map(uploadSingleFile);
      const urls = await Promise.all(uploadPromises);
      return urls;
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to upload files");
      return [];
    } finally {
      setIsUploading(false);
    }
  };

  return { uploadFiles, isUploading, error };
};
