import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { getStudentResult } from "src/api/repository/Results/studentResult";

export const useStudentResult = ({
  student_id,
  programme_id,
}: {
  student_id: string | undefined;
  programme_id: string | undefined;
}) => {
  const query = useQuery({
    queryKey: [
      "getStudentResult",
      {
        student_id,
        programme_id,
      },
    ],
    queryFn: () => {
      return getStudentResult({
        student_id,
        programme_id,
      });
    },
    retry: true,
    enabled: !!student_id && !!programme_id,
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
    select: (response) => {
      const { data } = response;
      return data;
    },
  });

  return query;
};
