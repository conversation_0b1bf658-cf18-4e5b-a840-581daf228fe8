import { Box, Flex, Stack } from "@chakra-ui/react";
import { FormikProps } from "formik";
import { useState } from "react";
import { EyeOpenIcon } from "@radix-ui/react-icons";

import HeaderForm from "../components/HeaderForm";
import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import InputField from "@/components/commons/InputField/InputField";

import { IResetPasswordValue } from "./ResetPassword.d";
import { InputError } from "@/components/commons/InputField/InputError";

const ResetPasswordForm = (props: FormikProps<IResetPasswordValue>) => {
  const {
    touched,
    errors,
    handleSubmit,
    handleChange,
    handleBlur,
    values,
    isSubmitting,
  } = props;
  const {
    confirm_password: confirmPasswordTouched,
    password: passwordTouched,
    reset_token: resetTouched,
  } = touched;
  const {
    confirm_password: confirmPasswordError,
    password: passwordError,
    reset_token: resetError,
  } = errors;

  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [isConfirmPasswordVisible, setIsConfirmPasswordVisible] =
    useState(false);

  const togglePasswordView = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  const toggleConfirmPasswordView = () => {
    setIsConfirmPasswordVisible(!isConfirmPasswordVisible);
  };
  return (
    <Flex
      m={{ base: "20px" }}
      flexDir="column"
      width={{ md: "400px", base: "320px" }}
    >
      <HeaderForm
        title="Set new password"
        subTitle="Create a password to activate your account"
      />
      <Stack mt="48px" spacing="16px">
        <Box>
          <InputField
            placeholder="Enter reset token"
            type="text"
            name="reset_token"
            onChange={handleChange}
            onBlur={handleBlur}
            value={values.reset_token}
            size="lg"
          />
          <InputError error={resetError} touched={resetTouched} />
        </Box>
        <Box>
          <div className="relative">
            <InputField
              placeholder="Create your password"
              type={isPasswordVisible ? "text" : "password"}
              name="password"
              onChange={handleChange}
              onBlur={handleBlur}
              value={values.password}
              size="lg"
            />
            <span className="absolute right-3 top-4 z-[9999] !cursor-pointer">
              <EyeOpenIcon
                className="h-4 w-4"
                onClick={() => togglePasswordView()}
              />
            </span>
          </div>
          <InputError error={passwordError} touched={passwordTouched} />
        </Box>
        <Box>
          <div className="relative">
            <InputField
              placeholder="Confirm your password"
              type={isConfirmPasswordVisible ? "text" : "password"}
              name="confirm_password"
              onChange={handleChange}
              onBlur={handleBlur}
              value={values.confirm_password}
              size="lg"
            />
            <span className="absolute right-3 top-4 z-[9999] !cursor-pointer">
              <EyeOpenIcon
                className="h-4 w-4"
                onClick={() => toggleConfirmPasswordView()}
              />
            </span>
          </div>

          <InputError
            error={confirmPasswordError}
            touched={confirmPasswordTouched}
          />
        </Box>
      </Stack>
      <Stack mt="48px">
        <ButtonCTA onClick={() => handleSubmit()} isLoading={isSubmitting}>
          Reset password
        </ButtonCTA>
      </Stack>
    </Flex>
  );
};

export default ResetPasswordForm;
