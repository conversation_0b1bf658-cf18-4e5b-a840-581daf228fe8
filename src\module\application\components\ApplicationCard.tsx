import React, { <PERSON>actNode, FC, useMemo } from "react";
import {
  Flex,
  Text,
  Box,
  Image,
  Card,
  useToast,
  Alert,
  AlertIcon,
  AlertDescription,
} from "@chakra-ui/react";

import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import AlertApplication from "./AlertApplication";
import {
  APPLICATION_TYPE,
  mapStyleByStatus,
  mapTextStatusApplication,
  WarningStatusApplication,
} from "../constants";
import { E_STATUS_APPLICATION } from "@/constants/enums";
import { useRouter } from "next/navigation";

import { IApplicationContainerProps } from "../ApplicationContainer.d";
import { useAuthStore } from "../../../store/AuthenticationStore/authentication";
import { ENROLL_STATUS } from "../../auth/Login/constants";
import Icon from "@/components/commons/Icons/Icon";
import { EIconName } from "@/components/commons/Icons/Icon.enums";
import { EColor } from "@/constants/colors";
import { updateEnrollmentStatus } from "src/api/repository/Enrollment/enrollment";
import { PreEnrollmentStatus } from "src/api/repository/Enrollment/enrollment.d";
import { MIVA_SIS_PAYMENT_URL } from "@/constants/api";

const ApplicationCard: FC<IApplicationContainerProps> = ({
  item,
  studentSisId,
}) => {
  const router = useRouter();
  const access_token = useAuthStore((state) => state.access_token);
  const toast = useToast();

  const isEnrollmentClosed = item?.atp_enrollment_start_date
    ? new Date(item?.atp_enrollment_end_date) < new Date()
    : false;

  const renderLabelValue = (label: string, value: string | ReactNode) => {
    return (
      <Flex gap="5px" width="308px" flexDir="column">
        <Text
          textTransform="uppercase"
          fontWeight="bold"
          color="#8498A7"
          fontSize="12px"
        >
          {label}
        </Text>
        {value && (typeof value === "string" || typeof value === "number") ? (
          <Text color="#0A3150" fontWeight="600" fontSize="16px">
            {value}
          </Text>
        ) : (
          "N/A"
        )}
      </Flex>
    );
  };

  const goToEnrolled = () => {
    if (
      item?.enrollment_status.toLowerCase() ==
      PreEnrollmentStatus.ENROLLMENT_PENDING.toLowerCase()
    ) {
      // Open payment page
      const fullUrl = `${MIVA_SIS_PAYMENT_URL}/payment-v2?studentSisId=${studentSisId}&applicationId=${item?.id}&userToken=${access_token}`;
      window.open(fullUrl, "_blank"); // Open payment in new tab
    } else if (
      item?.enrollment_status.toLowerCase() ==
      PreEnrollmentStatus.PAYMENT_IN_PROGRESS.toLowerCase()
    ) {
      // Show toast message
      toast({
        title: "Payment in progress",
        description:
          "Please wait for the payment to complete to complete the enrollment",
        status: "info",
        duration: 5000,
        isClosable: true,
      });
    } else if (
      item?.enrollment_status.toLowerCase() ==
      PreEnrollmentStatus.PAYMENT_COMPLETED.toLowerCase()
    ) {
      // Redirect to /enrollment
      router.push(`/enrollment?applicationId=${item.id}`);
    }
  };

  const goToEnrollment = async () => {
    // Update enrollment status to pending
    try {
      await updateEnrollmentStatus({
        status: PreEnrollmentStatus.ENROLLMENT_PENDING,
      });
      // Open payment page
      const fullUrl = `${MIVA_SIS_PAYMENT_URL}/payment-v2?studentSisId=${studentSisId}&applicationId=${item?.id}&userToken=${access_token}`;
      window.open(fullUrl, "_blank"); // Open payment in new tab
    } catch (ex) {
      console.error("Failed to update enrollment status", ex);
      toast({
        title: "Failed to update enrollment status",
        description: "Please try again later",
        status: "error",
      });
    }
  };

  const getApplicationStatus = (
    currentStatus?: string,
    enrollmentStatus?: string,
  ): string => {
    if (!currentStatus || !enrollmentStatus) return "";

    const currentEnrollmentStatus = enrollmentStatus.toUpperCase();

    if (currentStatus == E_STATUS_APPLICATION.ACCEPTED) {
      if (
        currentEnrollmentStatus == PreEnrollmentStatus.ENROLLMENT_PENDING ||
        currentEnrollmentStatus == PreEnrollmentStatus.PAYMENT_IN_PROGRESS
      ) {
        return PreEnrollmentStatus.ENROLLMENT_PENDING;
      }

      if (currentEnrollmentStatus == PreEnrollmentStatus.PAYMENT_COMPLETED) {
        return PreEnrollmentStatus.PAYMENT_COMPLETED;
      }
    }

    return currentStatus;
  };

  const applicationStatus = useMemo(
    () =>
      getApplicationStatus(item?.application_status, item?.enrollment_status),
    [item?.application_status, item?.enrollment_status],
  );

  const renderContinueBtn = () => {
    if (
      item?.enrollment_status.toLowerCase() ==
      PreEnrollmentStatus.PAYMENT_COMPLETED.toLowerCase()
    ) {
      return (
        <>
          <Flex alignItems="center" gap="8px" mb="8px">
            <Icon
              name={EIconName.CHECK_CIRCLE}
              color={EColor.TROPICAL_RAIN_FOREST}
            />
            <Text fontSize="16px" fontWeight="bold" color="blue.900">
              Tuition Paid!
            </Text>
          </Flex>
          <ButtonCTA
            minWidth="100%"
            onClick={goToEnrolled}
            background="#E53935"
            color="white"
            borderRadius="8px"
            fontSize="16px"
            fontWeight="bold"
            mt="8px"
            _hover={{ background: "#C62828" }}
          >
            Register your courses
          </ButtonCTA>
        </>
      );
    }

    if (
      applicationStatus.toLowerCase() ==
      E_STATUS_APPLICATION.ENROLLMENT_PENDING.toLowerCase()
    ) {
      return (
        <ButtonCTA
          minWidth="288px"
          onClick={goToEnrolled}
          background="#BB9E7F"
          mt="40px"
        >
          Continue Enrollment
        </ButtonCTA>
      );
    }

    if (
      applicationStatus.toLowerCase() ==
      E_STATUS_APPLICATION.ACCEPTED.toLowerCase()
    ) {
      return (
        <ButtonCTA
          minWidth="288px"
          onClick={goToEnrollment}
          background="#009933"
          mt="40px"
          width={{ md: "100%", base: "100%" }}
        >
          {"Start Enrollment"}
        </ButtonCTA>
      );
    }

    return <></>;
  };
  return (
    <Box
      borderRadius="16px"
      p={{ md: "32px", base: "16px" }}
      background="white"
      className="mb-10 pb-10 shadow-lg"
    >
      <Flex gap="16px" alignItems="center">
        <Text fontSize="24px" fontWeight="bold" color="#0A3150">
          Application Status
        </Text>
        <Box
          {...(mapStyleByStatus[applicationStatus || ""] || {})}
          borderRadius="24px"
          px="24px"
          py="8px"
        >
          <Text fontSize="14" fontWeight="600">
            {mapTextStatusApplication[applicationStatus || ""]}
          </Text>
        </Box>
      </Flex>
      {item?.enrollment_status == ENROLL_STATUS.ENROLLED && (
        <Flex
          backgroundColor="#F8F5F2"
          border="1px solid #BB9E7F"
          mt="40px"
          alignItems="center"
          justifyContent="flex-start"
          gap="8px"
          px="24px"
          py="10px"
          w={"100%"}
          borderRadius="16px"
        >
          <Icon
            isStaticIcon={true}
            name={EIconName.EXCLAMATION_CIRCLE}
            color={EColor.MONGOOSE}
          />
          <Text fontSize="14px">
            You have successfully paid your tuition, continue enrolment to enrol
            into your courses.
            <span
              className="cursor-pointer px-[2px] text-sky-500 underline"
              onClick={() => goToEnrolled()}
            >
              Continue enrollment
            </span>
          </Text>
        </Flex>
      )}

      {WarningStatusApplication.includes(applicationStatus || "") && (
        <AlertApplication status={applicationStatus || ""} />
      )}
      <Card
        mt="40px"
        position="inherit"
        direction={{ base: "column", lg: "row" }}
        overflow="hidden"
        variant="unstyled"
        gap="32px"
      >
        <Image
          objectFit="cover"
          maxW={{ base: "100%", sm: "340px" }}
          height={{ base: "100%", sm: "320px" }}
          borderRadius="16px"
          src={item?.programme?.image}
          alt="application_image"
        />
        <Flex flexDir="column">
          <Text mb="12px" color="#0A3150" fontSize="24px" fontWeight="bold">
            {item?.programme.name}
          </Text>
          <Text
            maxWidth={{ md: "100%", base: "90%" }}
            fontSize="16px"
            fontWeight="500"
            color="#0A3150"
          >
            {item?.programme.description}
          </Text>
          <Box mt="32px">
            <Flex
              gap={{ base: "24px" }}
              flexWrap="wrap"
              mb="32px"
              textTransform="capitalize"
            >
              {renderLabelValue("Study Level", item?.application_type)}
              {renderLabelValue(
                "Course units",
                item?.programme?.total_credit_required,
              )}
            </Flex>
            <Flex gap={{ base: "24px" }} flexWrap="wrap" mb="32px">
              {renderLabelValue(
                "Admission Type",
                APPLICATION_TYPE[item?.application_type || ""] ||
                  item?.application_type,
              )}

              {renderLabelValue(
                "tuition per semester",
                item?.country === "Nigeria"
                  ? `₦${item?.products.fee[0].plans
                      .find(
                        (plan) =>
                          plan.installment_name === "SEMESTER" ||
                          plan.installment_name === "PER SEMESTER PLAN",
                      )
                      ?.installments[0].amount_in_naira.toLocaleString()}`
                  : `$${item?.products.fee[0].plans
                      .find((plan) => plan.installment_name === "SEMESTER")
                      ?.installments[0].amount_in_dollar.toLocaleString()}`,
              )}
            </Flex>
            <Flex gap={{ base: "24px" }} flexWrap="wrap">
              {renderLabelValue(
                "Study duration",
                `${item?.programme.minimum_duration} ${item?.programme.duration_unit}`,
              )}
              {renderLabelValue(
                "tuition per session",
                item?.country === "Nigeria"
                  ? `₦${item?.products.fee[0].plans
                      .find((plan) => plan.installment_name === "")
                      ?.installments[0].amount_in_naira.toLocaleString()}`
                  : `$${item?.products.fee[0].plans
                      .find((plan) => plan.installment_name === "")
                      ?.installments[0].amount_in_dollar.toLocaleString()}`,
              )}
            </Flex>
            {/* <Flex gap={{ base: "24px" }} flexWrap="wrap">
              {renderLabelValue(
                "Study duration",
                atpEnrollmentStartDate || "N/A",
              )}
              {renderLabelValue(
                "tuition per session",
                atpEnrollmentEndDate || "N/A",
              )}
            </Flex> */}
          </Box>
          {isEnrollmentClosed ? (
            <Alert
              status="error"
              variant="subtle"
              borderRadius="md"
              border="1px solid"
              borderColor="red.400"
              bg="red.100"
              py={3}
              px={4}
              mt={4}
            >
              <AlertIcon />
              <AlertDescription fontWeight="bold">
                Your application for this cohort has expired. Click on this{" "}
                <span className="cursor-pointer text-blue-500">
                  <a
                    href="https://miva.university/get-started/"
                    target="_blank"
                  >
                    link to apply{" "}
                  </a>
                </span>
                to the next cohort.
              </AlertDescription>
            </Alert>
          ) : (
            renderContinueBtn()
          )}
        </Flex>
      </Card>
    </Box>
  );
};

export default ApplicationCard;
