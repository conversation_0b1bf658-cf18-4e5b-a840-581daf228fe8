"use client";

import React from "react";
import { Text, Box } from "@chakra-ui/react";
import PersonalInformation from "./components/PersonalInformation";
import BasicDetailsContainer from "./components/BasicDetails/BasicDetailsContainer";
import EducationDetails from "./components/EducationDetails";
import Documents from "./components/Documents";
import { useProfile } from "./useProfile";

const ProfileWrapper = () => {
  const { handleProfileUpdate, student, displayPictureLoading } = useProfile();
  const [currentTab, setCurrentTab] = React.useState(1);

  const handleEventFromChild = (eventMessage: number) => {
    setCurrentTab(eventMessage);
  };
  return (
    <div>
      <Text as={"h2"} fontWeight={700} fontSize={"24px"}>
        Profile
      </Text>
      <Box
        display={"flex"}
        gap={"20px"}
        marginTop={"28px"}
        flexDirection={{ base: "column", md: "row" }}
      >
        <Box
          width={{ base: "100%", md: "40%", lg: "30%" }}
          padding={"32px"}
          overflow={"hidden"}
          wordBreak={"break-word"}
          background={"#ffffff"}
          borderRadius={"16px"}
        >
          <PersonalInformation
            currentTab={currentTab}
            handleEventFromChild={handleEventFromChild}
            handleProfileUpdate={handleProfileUpdate}
            loading={displayPictureLoading}
          />
        </Box>
        <Box
          width={{ base: "100%", md: "60%", lg: "70%" }}
          padding={"32px"}
          background={"#ffffff"}
          borderRadius={"16px"}
        >
          {currentTab === 1 ? (
            <div>
              <BasicDetailsContainer
                handleProfileUpdate={handleProfileUpdate}
              />
            </div>
          ) : (
            ""
          )}
          {currentTab === 2 ? (
            <EducationDetails
              matric_number={student?.student_profile.matric_no}
              student_id={student?.student_profile.student_id}
            />
          ) : (
            ""
          )}
          {currentTab === 3 ? <Documents /> : ""}
        </Box>
      </Box>
    </div>
  );
};

export default ProfileWrapper;
