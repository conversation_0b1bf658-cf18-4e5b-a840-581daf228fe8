export interface WalletSetupRequest {
  account_name?: string;
  account_number?: string;
  bank_code?: string;
  bank_name?: string;
  bvn_verification?: string;
  currency?: string;
  user_id: string;
  wallet_pin: string;
  confirm_wallet_pin: string;
}

export interface WalletSetupResponse {
  data: string;
  errors: string;
  message: string;
  status: string;
}

export interface SendWalletOTPRequest {
  user_id: string;
}

export interface SendWalletOTPResponse {
  data: string;
  errors: string;
  message: string;
  status: string;
}

export interface VerifyWalletOTPRequest {
  user_id: string;
  verify_code: string;
}

export interface VerifyWalletOTPResponse {
  data: string;
  errors: string;
  message: string;
  status: string;
}

export interface WalletFormData {
  otp: string;
  bankName: string;
  bankCode: string;
  accountNumber: string;
  accountName: string;
  bvn: string;
  acceptedTerms: boolean;
  pin: string;
  userId: string;
  currency: string;
}
