import { IOptionsItem } from "@/constants/types";
import { ReactNode } from "react";

export interface ICommonSelectProps {
  label?: string;
  labelContent?: string;
  value: string | number | object | undefined;
  options: IOptionsItem[];
  onChange: (value: string) => void;
  onClick?: (e: MouseEventHandler<HTMLButtonElement>) => void;
  openMenu?: boolean;
  showIndicatorIcon?: boolean;
  className?: string;
  isDisabled?: boolean;
  tooltipDes?: string | ReactNode;
  placeholder?: string;
}
