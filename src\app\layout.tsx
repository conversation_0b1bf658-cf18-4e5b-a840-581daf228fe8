import "@/styles/globals.scss";

import { Metadata } from "next";
import { Manrope } from "next/font/google";
import { ReactNode } from "react";

import { MainProvider } from "@/components/providers/MainProvider";
import { MainLayout } from "@/components/templates/MainLayout";
import { ChakraProvider } from "@chakra-ui/react";

import { cn } from "@/lib/utils";

const manrope = Manrope({
  subsets: ["latin"],
  variable: "--font-primary",
  display: "swap",
});

export const metadata: Metadata = {
  title: "Miva SIS Portal",
  description: "Miva SIS Portal for Student Journey",
};

interface RootLayoutProps {
  children: ReactNode;
}

const RootLayout = ({ children }: RootLayoutProps) => {
  return (
    <html lang="en">
      <head>
        <link
          rel="stylesheet"
          href="https://unicons.iconscout.com/release/v4.0.8/css/line.css"
        />
        <link
          rel="stylesheet"
          href="https://unicons.iconscout.com/release/v4.0.8/css/solid.css"
        />
        <link
          rel="stylesheet"
          href="https://unicons.iconscout.com/release/v4.0.8/css/thinline.css"
        />
      </head>
      <body
        className={cn(manrope.variable, "overflow-x-hidden font-primary")}
        suppressHydrationWarning
      >
        <ChakraProvider>
          <MainProvider>
            <MainLayout>{children}</MainLayout>
          </MainProvider>
        </ChakraProvider>
      </body>
    </html>
  );
};

export default RootLayout;
