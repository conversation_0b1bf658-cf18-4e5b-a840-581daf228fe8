import { undefinedToString } from "@/lib/utils/string";
import { useQuery } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { Dispatch, SetStateAction, useEffect } from "react";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import { getUserByAccessToken, redirectToCASService } from "./cas.utils";
import { useCASQueryParams } from "./useCASQueryParams";
import { useCASService } from "./useCASService";
import { usePathname } from "next/navigation";

export const useCASValidation = (
  setShowLoader?: Dispatch<SetStateAction<boolean>>,
) => {
  const { service, urlDecodedService, casService, casServiceIsFetching } =
    useCASService();
  const {
    query: { renew, gateway },
  } = useCASQueryParams();
  const router = useRouter();
  const pathname = usePathname();
  const access_token = useAuthStore((state) => state.access_token);
  const refresh_token = useAuthStore((state) => state.refresh_token);
  const isHydrated = useAuthStore((state) => state.isHydrated);

  const userQuery = useQuery({
    queryKey: ["curreny_user"],
    enabled: !!access_token,
    retry: 1,
    queryFn: async () => {
      const user = await getUserByAccessToken(undefinedToString(access_token));
      return user;
    },
  });

  useEffect(() => {
    if (
      casServiceIsFetching ||
      userQuery.isFetching ||
      !pathname.includes("cas/") ||
      !isHydrated
    ) {
      return;
    }
    if (renew == "true" && gateway == "true") {
      // URI SHOULD NOT set both the renew and gateway request parameters
      router.push(`error`);
      return;
    }

    if (!service) {
      // If a service is not specified and a session already exists, CAS SHOULD display a message notifying the client that it is already logged in
      // The login flow takes the user the dashboard, when there's no service so do nothing here
      if (userQuery.data) {
        router.push(`success`);
      } else {
        // If a service is not specified and a session does not yet exist, CAS SHOULD request credentials
        setShowLoader?.(false);
      }
      return;
    }

    if (!casService) {
      // Only authorized and known client applications should be able to use the CAS server
      router.push(`error/unauthorized`);
      return;
    }

    if (!userQuery.data) {
      if (gateway == "true") {
        // If the client does not have a pre-existing session, redirect the client to the  service URL with no “ticket” parameter
        window.location.href = urlDecodedService;
      } else {
        setShowLoader?.(false);
      }
      return;
    } else if (userQuery.data) {
      if (renew == "true") {
        // Service specifies that the user must authenticate afresh irrespective of an active session
        setShowLoader?.(false);
        return;
      } else {
        // if (gateway == "true")
        // If the client has a pre-existing session, redirect the client to the service URL
        void redirectToCASService({
          access_token: undefinedToString(access_token),
          refresh_token: undefinedToString(refresh_token),
          service: urlDecodedService,
        });
        return;
      }
    }

    // method: POST|HEADER|GET not supported, GET is used by default
  }, [
    service,
    urlDecodedService,
    userQuery.data,
    userQuery.isFetching,
    casService,
    casServiceIsFetching,
    isHydrated,
  ]);

  return {
    user: userQuery.data,
    service: urlDecodedService,
  };
};
