export interface IProgrammeIntakeSemesterParams {
  programme_intake_id: string;
}

interface MetaData {
  modified_by: string;
  modified: string;
}

export interface IProgrammeIntakeSemesterData {
  application_end_date: string;
  application_start_date: string;
  atp_code: string;
  created_at: string;
  end_date: string;
  enrolment_end_date: string;
  enrolment_start_date: string;
  exam_deferment_end_date: string;
  exam_deferment_start_date: string;
  id: string;
  meta_data: MetaData;
  name: string;
  start_date: string;
  semester_start_date?: string;
  semester_end_date?: string;
  semester_status: "ACTIVE" | "INACTIVE";
  switch_enrollment_cut_off_date: string;
  semester_type: "SEMESTER";
  updated_at: string;
  semester_atp_id: string;
  programme_name: string;
}
