import { undefinedToString } from "@/lib/utils/string";
import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import { validateCasService } from "./cas.utils";
import { useCASQueryParams } from "./useCASQueryParams";

export const useCASService = () => {
  const {
    query: { service, url },
  } = useCASQueryParams();

  const urlDecodedService = useMemo(
    () => decodeURIComponent(undefinedToString(service)),
    [service],
  );

  const casServicesQuery = useQuery({
    queryKey: ["cas_services"],
    enabled: !!service,
    queryFn: async () => {
      return await validateCasService(urlDecodedService);
    },
  });

  return {
    casService: casServicesQuery.data,
    casServiceIsFetching: casServicesQuery.isFetching,
    service,
    urlDecodedService,
    url,
  };
};
