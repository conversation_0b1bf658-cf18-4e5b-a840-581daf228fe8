"use client";
import { Alert, AlertDescription } from "@/components/ui/alert";

const CASSuccessPage = () => {
  return (
    <div className="m-8 flex justify-center">
      <Alert
        variant="success"
        className="mt-5 border border-green-700 bg-green-100 p-5"
      >
        <AlertDescription className="flex flex-col items-start justify-between">
          <div className="flex flex-col justify-between lg:gap-y-5">
            <p className="text-lg font-bold">Log In Successful</p>
          </div>
          <div className="mt-5 flex flex-col justify-between lg:mt-0 lg:gap-y-2">
            <p>
              You, have successfully logged into the Central Authentication
              Service. However, you are seeing this page because CAS does not
              know about your target destination and how to get you there.
              Examine the authentication request again and make sure a target
              service/application that is authorized and registered with CAS is
              specified.
            </p>
          </div>
        </AlertDescription>
      </Alert>
    </div>
  );
};

export default CASSuccessPage;
