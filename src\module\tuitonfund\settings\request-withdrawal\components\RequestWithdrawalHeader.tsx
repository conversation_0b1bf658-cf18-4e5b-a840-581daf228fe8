import { BaseColor } from "@/constants/colors";
import { Card, Text } from "@chakra-ui/react";
import React from "react";
import WarningAlert from "./Alert";

export default function RequestWithdrawalHeader() {
  return (
    <>
      <Text fontWeight={700} fontSize={20} color={BaseColor.PRIMARY}>
        Request Withdrawal
      </Text>
      <Card
        mt="40px"
        mb={"8px"}
        shadow={0}
        padding="16px"
        backgroundColor={"#E9ECEF"}
      >
        <Text fontWeight={600} fontSize={14} color={BaseColor.PRIMARY}>
          Tuition Fund Balance
        </Text>
        <Text fontWeight={700} fontSize={28} color={BaseColor.PRIMARY}>
          ₦300,000
        </Text>
      </Card>
      <WarningAlert text="Money added to your Tuition Fund can ONLY be withdrawn when you conclude your final year." />
    </>
  );
}
