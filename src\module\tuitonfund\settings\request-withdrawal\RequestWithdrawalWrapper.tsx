"use client";

import { Box, Text } from "@chakra-ui/react";
import React, { useState } from "react";
import FormSection from "./components/FormSection";
import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import RequestWithdrawalForm from "./RequestWithdrawalForm";
import RequestWithdrawalHeader from "./components/RequestWithdrawalHeader";
import CommonSuccessScreen from "@/components/commons/CommonSuccessScreen/CommonSuccessScreen";
import { useRouter } from "next/navigation";

export default function RequestWithdrawalWrapper() {
  const router = useRouter();
  const [showForm, setShowForm] = useState(false);
  const [onSuccess, setOnSuccess] = useState(false);

  const onFormSubmit = () => {
    setOnSuccess(true);
  };
  return (
    <Box margin={0}>
      {onSuccess ? (
        <CommonSuccessScreen
          title="Request Submitted"
          buttonText="Go to Dashboard"
          showLogo={true}
          subTitle="Your request has been submitted. Our team will review it and let you
            know if it’s approved."
          onClick={() => router.push("/dashboard")}
          imageUrl="/images/icons/success.svg"
        />
      ) : (
        <FormSection
          px={{ base: "16px", md: "56px" }}
          py={{ base: "24px", md: "41px" }}
        >
          <RequestWithdrawalHeader />

          {showForm ? (
            <Box py={"40px"}>
              <RequestWithdrawalForm onFormSubmit={onFormSubmit} />
            </Box>
          ) : (
            <Box justifyContent={"end"} display={"flex"}>
              <ButtonCTA
                onClick={() => setShowForm(true)}
                disabled={false}
                mt={"40px"}
                fontSize={12}
                height="auto"
                py={3}
                px={7}
              >
                <Text fontSize={14} fontWeight={600}>
                  Request Withdrawal
                </Text>
              </ButtonCTA>
            </Box>
          )}
        </FormSection>
      )}
    </Box>
  );
}
