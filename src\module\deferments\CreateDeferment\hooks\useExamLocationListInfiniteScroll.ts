import { useEffect, useRef, useState } from "react";
import { baseApi } from "src/api/config/api";
import { MODULE_ROUTE, Routes } from "src/api/config/routes";
import { Paginated, APIResponse } from "src/api/config/api.d";
import { useDebounce } from "use-debounce";
import { IExamLocation } from "src/api/repository/examLocation";

export const useExamLocationListInfiniteScroll = () => {
  const [search, setSearch] = useState<string>("");
  const [examLocation, setExamLocation] = useState<IExamLocation>();
  const [examLocations, setExamLocations] = useState<IExamLocation[]>([]);
  const pagination = useRef<Paginated<number>>({
    currentPage: 1,
    nextPage: 1,
    data: 0,
    perPage: 10,
    prevPage: 0,
    total: 0,
    totalPages: 0,
  });

  const handleSearchExamLocation = async (page = 1) => {
    const result = await baseApi.get<APIResponse<Paginated<IExamLocation[]>>>(
      `${Routes[MODULE_ROUTE.MISC].GET_EXAM_LOCATION_LIST}?search=${search}&page=${page}`,
    );

    const { data, ...paginationData } = result.data.data;
    pagination.current = { ...paginationData, data: data.length };
    setExamLocations(page == 1 ? data : [...examLocations, ...data]);
  };

  const loadMoreExamLocations = async () => {
    await handleSearchExamLocation(pagination.current.nextPage);
  };

  useEffect(
    () => {
      handleSearchExamLocation(1);
    },
    useDebounce(search, 1000),
  );

  return {
    search,
    setSearch,
    examLocations,
    examLocation,
    setExamLocation,
    loadMoreExamLocations,
    hasMore: pagination.current.currentPage < pagination.current.totalPages,
  };
};
