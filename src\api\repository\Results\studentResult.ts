import { APIResponse } from "src/api/config/api.d";
import { baseApi } from "src/api/config/api";
import { MODULE_ROUTE, Routes } from "src/api/config/routes";
import { StudentResultDetails } from "src/module/result/StudentDetails";

export interface IStudentResultParams {
  student_id: string | undefined;
  programme_id: string | undefined;
}

export async function getStudentResult({
  student_id,
  programme_id,
}: IStudentResultParams): Promise<APIResponse<StudentResultDetails>> {
  try {
    const response = await baseApi.get(
      Routes[MODULE_ROUTE.RESULT].STUDENT_RESULT,
      {
        params: {
          student_id,
          programme_id,
        },
      },
    );
    return response.data;
  } catch (error) {
    console.error("Error get list enrollment countries:", error);
    throw error;
  }
}
